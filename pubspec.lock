# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _fe_analyzer_shared:
    dependency: transitive
    description:
      name: _fe_analyzer_shared
      sha256: "16e298750b6d0af7ce8a3ba7c18c69c3785d11b15ec83f6dcd0ad2a0009b3cab"
      url: "https://pub.dev"
    source: hosted
    version: "76.0.0"
  _flutterfire_internals:
    dependency: transitive
    description:
      name: _flutterfire_internals
      sha256: f5628cd9c92ed11083f425fd1f8f1bc60ecdda458c81d73b143aeda036c35fe7
      url: "https://pub.dev"
    source: hosted
    version: "1.3.16"
  _macros:
    dependency: transitive
    description: dart
    source: sdk
    version: "0.3.3"
  action_slider:
    dependency: "direct main"
    description:
      name: action_slider
      sha256: fad0720cde9bf06c12594c15da17dba087556a3285875a91aee3d3a64a3072e2
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0"
  aescryptojs:
    dependency: "direct main"
    description:
      name: aescryptojs
      sha256: "2727de8a3006cda4fdb7b0ebd6155b777fc85169b34dbc632c07ed0fcfc84eeb"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  analyzer:
    dependency: "direct overridden"
    description:
      name: analyzer
      sha256: "1f14db053a8c23e260789e9b0980fa27f2680dd640932cae5e1137cce0e46e1e"
      url: "https://pub.dev"
    source: hosted
    version: "6.11.0"
  android_id:
    dependency: transitive
    description:
      name: android_id
      sha256: "748ba5f93dd5c497e675d8eaa1404346ce4d1794464ea654576ff192d153b92a"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0"
  animated_custom_dropdown:
    dependency: "direct main"
    description:
      name: animated_custom_dropdown
      sha256: "5d651fd00c770c32ef7c98978d19b9d8c0a9ebc6e305974af05c1e1a03692289"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  animated_loading_border:
    dependency: "direct main"
    description:
      name: animated_loading_border
      sha256: e30aaadff7c39b6218e58015788095760d3fa260b17a1e707fd79e43feadcaee
      url: "https://pub.dev"
    source: hosted
    version: "0.0.2"
  animated_text_kit:
    dependency: transitive
    description:
      name: animated_text_kit
      sha256: adba517adb7e6adeb1eb5e1c8a147dd7bc664dfdf2f5e92226b572a91393a93d
      url: "https://pub.dev"
    source: hosted
    version: "4.2.3"
  appcheck:
    dependency: "direct main"
    description:
      name: appcheck
      sha256: "6971b1ae5833b15b1abc29f7d03d08db79577b2934ceb34fe39ab937b53c2f17"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.4+1"
  archive:
    dependency: "direct main"
    description:
      name: archive
      sha256: cb6a278ef2dbb298455e1a713bda08524a175630ec643a242c399c932a0a1f7d
      url: "https://pub.dev"
    source: hosted
    version: "3.6.1"
  args:
    dependency: transitive
    description:
      name: args
      sha256: eef6c46b622e0494a36c5a12d10d77fb4e855501a91c1b9ef9339326e58f0596
      url: "https://pub.dev"
    source: hosted
    version: "2.4.2"
  asn1lib:
    dependency: transitive
    description:
      name: asn1lib
      sha256: c9c85fedbe2188b95133cbe960e16f5f448860f7133330e272edbbca5893ddc6
      url: "https://pub.dev"
    source: hosted
    version: "1.5.2"
  async:
    dependency: transitive
    description:
      name: async
      sha256: "758e6d74e971c3e5aceb4110bfd6698efc7f501675bcfe0c775459a8140750eb"
      url: "https://pub.dev"
    source: hosted
    version: "2.13.0"
  audioplayers:
    dependency: transitive
    description:
      name: audioplayers
      sha256: c05c6147124cd63e725e861335a8b4d57300b80e6e92cea7c145c739223bbaef
      url: "https://pub.dev"
    source: hosted
    version: "5.2.1"
  audioplayers_android:
    dependency: transitive
    description:
      name: audioplayers_android
      sha256: b00e1a0e11365d88576320ec2d8c192bc21f1afb6c0e5995d1c57ae63156acb5
      url: "https://pub.dev"
    source: hosted
    version: "4.0.3"
  audioplayers_darwin:
    dependency: transitive
    description:
      name: audioplayers_darwin
      sha256: "3034e99a6df8d101da0f5082dcca0a2a99db62ab1d4ddb3277bed3f6f81afe08"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.2"
  audioplayers_linux:
    dependency: transitive
    description:
      name: audioplayers_linux
      sha256: "60787e73fefc4d2e0b9c02c69885402177e818e4e27ef087074cf27c02246c9e"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  audioplayers_platform_interface:
    dependency: transitive
    description:
      name: audioplayers_platform_interface
      sha256: "365c547f1bb9e77d94dd1687903a668d8f7ac3409e48e6e6a3668a1ac2982adb"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.0"
  audioplayers_web:
    dependency: transitive
    description:
      name: audioplayers_web
      sha256: "22cd0173e54d92bd9b2c80b1204eb1eb159ece87475ab58c9788a70ec43c2a62"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0"
  audioplayers_windows:
    dependency: transitive
    description:
      name: audioplayers_windows
      sha256: "9536812c9103563644ada2ef45ae523806b0745f7a78e89d1b5fb1951de90e1a"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  auto_route:
    dependency: "direct main"
    description:
      name: auto_route
      sha256: eb33554581a0a4aa7e6da0f13a44291a55bf71359012f1d9feb41634ff908ff8
      url: "https://pub.dev"
    source: hosted
    version: "7.9.2"
  auto_route_generator:
    dependency: "direct dev"
    description:
      name: auto_route_generator
      sha256: "11067a3bcd643812518fe26c0c9ec073990286cabfd9d74b6da9ef9b913c4d22"
      url: "https://pub.dev"
    source: hosted
    version: "7.3.2"
  auto_sms_verification:
    dependency: "direct main"
    description:
      name: auto_sms_verification
      sha256: "389261e65e4234b8bb4d813f52931943898eb78eb5e43fa5416548348bca1d11"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.8"
  auto_start_flutter:
    dependency: "direct main"
    description:
      path: "."
      ref: master
      resolved-ref: "3581cb8bb09e0296c51c6ed4dfa3a11b442330bd"
      url: "https://github.com/ashiq-kodali/auto_start_flutter.git"
    source: git
    version: "0.1.4"
  awesome_circular_chart:
    dependency: "direct main"
    description:
      name: awesome_circular_chart
      sha256: efbf8b84b871fc5fc10680cd812dc5983507f7bad0910c4ced4a7816fe59a25e
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  awesome_notifications:
    dependency: "direct main"
    description:
      name: awesome_notifications
      sha256: "0d5fa4457f2ba4e536adc3ef6af709cdcecf4a05a1f3035981e9afa2f899b2a8"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.1"
  badges:
    dependency: "direct main"
    description:
      name: badges
      sha256: a7b6bbd60dce418df0db3058b53f9d083c22cdb5132a052145dc267494df0b84
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  bloc:
    dependency: transitive
    description:
      name: bloc
      sha256: f53a110e3b48dcd78136c10daa5d51512443cea5e1348c9d80a320095fa2db9e
      url: "https://pub.dev"
    source: hosted
    version: "8.1.3"
  bloc_concurrency:
    dependency: "direct main"
    description:
      name: bloc_concurrency
      sha256: "5857eb6653b4dd5e30e1ffab91037957fd64a9b9c5e53d26714ef25a46c04679"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.4"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "8aab1771e1243a5063b8b0ff68042d67334e3feab9e95b9490f9a6ebf73b42ea"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  build:
    dependency: transitive
    description:
      name: build
      sha256: "80184af8b6cb3e5c1c4ec6d8544d27711700bc3e6d2efad04238c7b5290889f0"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  build_config:
    dependency: transitive
    description:
      name: build_config
      sha256: bf80fcfb46a29945b423bd9aad884590fb1dc69b330a4d4700cac476af1708d1
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  build_daemon:
    dependency: transitive
    description:
      name: build_daemon
      sha256: "0343061a33da9c5810b2d6cee51945127d8f4c060b7fbdd9d54917f0a3feaaa1"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.1"
  build_resolvers:
    dependency: transitive
    description:
      name: build_resolvers
      sha256: "339086358431fa15d7eca8b6a36e5d783728cf025e559b834f4609a1fcfb7b0a"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.2"
  build_runner:
    dependency: "direct dev"
    description:
      name: build_runner
      sha256: "581bacf68f89ec8792f5e5a0b2c4decd1c948e97ce659dc783688c8a88fbec21"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.8"
  build_runner_core:
    dependency: transitive
    description:
      name: build_runner_core
      sha256: "4ae8ffe5ac758da294ecf1802f2aff01558d8b1b00616aa7538ea9a8a5d50799"
      url: "https://pub.dev"
    source: hosted
    version: "7.3.0"
  built_collection:
    dependency: transitive
    description:
      name: built_collection
      sha256: "376e3dd27b51ea877c28d525560790aee2e6fbb5f20e2f85d5081027d94e2100"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  built_value:
    dependency: transitive
    description:
      name: built_value
      sha256: fedde275e0a6b798c3296963c5cd224e3e1b55d0e478d5b7e65e6b540f363a0e
      url: "https://pub.dev"
    source: hosted
    version: "8.9.1"
  cached_network_image:
    dependency: "direct main"
    description:
      name: cached_network_image
      sha256: "28ea9690a8207179c319965c13cd8df184d5ee721ae2ce60f398ced1219cea1f"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.1"
  cached_network_image_platform_interface:
    dependency: transitive
    description:
      name: cached_network_image_platform_interface
      sha256: "9e90e78ae72caa874a323d78fa6301b3fb8fa7ea76a8f96dc5b5bf79f283bf2f"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  cached_network_image_web:
    dependency: transitive
    description:
      name: cached_network_image_web
      sha256: "42a835caa27c220d1294311ac409a43361088625a4f23c820b006dd9bffb3316"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  camera:
    dependency: "direct main"
    description:
      name: camera
      sha256: dfa8fc5a1adaeb95e7a54d86a5bd56f4bb0e035515354c8ac6d262e35cec2ec8
      url: "https://pub.dev"
    source: hosted
    version: "0.10.6"
  camera_android:
    dependency: "direct main"
    description:
      name: camera_android
      sha256: bd9737671a00d979e0310a946e5be2fdc621b6a36b95378755cb3e4498e61485
      url: "https://pub.dev"
    source: hosted
    version: "0.10.10+2"
  camera_avfoundation:
    dependency: transitive
    description:
      name: camera_avfoundation
      sha256: a33cd9a250296271cdf556891b7c0986a93772426f286595eccd5f45b185933c
      url: "https://pub.dev"
    source: hosted
    version: "0.9.18+14"
  camera_platform_interface:
    dependency: transitive
    description:
      name: camera_platform_interface
      sha256: "2f757024a48696ff4814a789b0bd90f5660c0fb25f393ab4564fb483327930e2"
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  camera_web:
    dependency: transitive
    description:
      name: camera_web
      sha256: "595f28c89d1fb62d77c73c633193755b781c6d2e0ebcd8dc25b763b514e6ba8f"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.5"
  carousel_slider:
    dependency: "direct main"
    description:
      name: carousel_slider
      sha256: "9c695cc963bf1d04a47bd6021f68befce8970bcd61d24938e1fb0918cf5d9c42"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.1"
  change_app_package_name:
    dependency: "direct dev"
    description:
      name: change_app_package_name
      sha256: f9ebaf68a4b5a68c581492579bb68273c523ef325fbf9ce2f1b57fb136ad023b
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: f71061c654a3380576a52b451dd5532377954cf9dbd272a78fc8479606670803
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  checked_yaml:
    dependency: transitive
    description:
      name: checked_yaml
      sha256: feb6bed21949061731a7a75fc5d2aa727cf160b91af9a3e464c5e3a32e28b5ff
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  chopper:
    dependency: transitive
    description:
      name: chopper
      sha256: "813cabd029ad8c020874429a671f2c15f45cfc3ced66b566bfa181a9b61b89b8"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.6"
  circular_countdown_timer:
    dependency: "direct main"
    description:
      name: circular_countdown_timer
      sha256: "9ba5fbc076cedbcbf6190ed86762e679f43d7c67cdd903ea34df059dabdc08d4"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.3"
  cli_util:
    dependency: transitive
    description:
      name: cli_util
      sha256: "66f86e916d285c1a93d3b79587d94bd71984a66aac4ff74e524cfa7877f1395c"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.5"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: fddb70d9b5277016c77a80201021d40a2247104d9f4aa7bab7157b7e3f05b84b
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  code_builder:
    dependency: transitive
    description:
      name: code_builder
      sha256: f692079e25e7869c14132d39f223f8eec9830eb76131925143b2129c4bb01b37
      url: "https://pub.dev"
    source: hosted
    version: "4.10.0"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: "2f5709ae4d3d59dd8f7cd309b4e023046b57d8a6c82130785d2b0e5868084e76"
      url: "https://pub.dev"
    source: hosted
    version: "1.19.1"
  color:
    dependency: transitive
    description:
      name: color
      sha256: ddcdf1b3badd7008233f5acffaf20ca9f5dc2cd0172b75f68f24526a5f5725cb
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  connectivity_plus:
    dependency: "direct overridden"
    description:
      name: connectivity_plus
      sha256: "051849e2bd7c7b3bc5844ea0d096609ddc3a859890ec3a9ac4a65a2620cc1f99"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.4"
  connectivity_plus_platform_interface:
    dependency: transitive
    description:
      name: connectivity_plus_platform_interface
      sha256: "42657c1715d48b167930d5f34d00222ac100475f73d10162ddf43e714932f204"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  context_holder:
    dependency: "direct main"
    description:
      name: context_holder
      sha256: "29561fdf80133077538bd9db59102550b4de520aa78adf8ec0bfb714b9dc98cd"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.5"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: "0f08b14755d163f6e2134cb58222dd25ea2a2ee8a195e53983d57c075324d592"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      sha256: "7caf6a750a0c04effbb52a676dce9a4a592e10ad35c34d6d2d0e4811160d5670"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.4+2"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: ff625774173754681d66daaf4a448684fb04b78f902da9cb3d308c19cc5e8bab
      url: "https://pub.dev"
    source: hosted
    version: "3.0.3"
  csslib:
    dependency: transitive
    description:
      name: csslib
      sha256: "831883fb353c8bdc1d71979e5b342c7d88acfbc643113c14ae51e2442ea0f20f"
      url: "https://pub.dev"
    source: hosted
    version: "0.17.3"
  csv:
    dependency: transitive
    description:
      name: csv
      sha256: "63ed2871dd6471193dffc52c0e6c76fb86269c00244d244297abbb355c84a86e"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      sha256: d57953e10f9f8327ce64a508a355f0b1ec902193f66288e8cb5070e7c47eeb2d
      url: "https://pub.dev"
    source: hosted
    version: "1.0.6"
  custom_pin_screen:
    dependency: "direct main"
    description:
      name: custom_pin_screen
      sha256: f4312585a7363eb1b37700c4d2fc5765e30a94307906618a9a51a099537ae411
      url: "https://pub.dev"
    source: hosted
    version: "0.3.0"
  dart_style:
    dependency: transitive
    description:
      name: dart_style
      sha256: "7306ab8a2359a48d22310ad823521d723acfed60ee1f7e37388e8986853b6820"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.8"
  dartx:
    dependency: transitive
    description:
      name: dartx
      sha256: "8b25435617027257d43e6508b5fe061012880ddfdaa75a71d607c3de2a13d244"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  dartz:
    dependency: "direct main"
    description:
      name: dartz
      sha256: e6acf34ad2e31b1eb00948692468c30ab48ac8250e0f0df661e29f12dd252168
      url: "https://pub.dev"
    source: hosted
    version: "0.10.1"
  dbus:
    dependency: transitive
    description:
      name: dbus
      sha256: "365c771ac3b0e58845f39ec6deebc76e3276aa9922b0cc60840712094d9047ac"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.10"
  device_frame:
    dependency: transitive
    description:
      name: device_frame
      sha256: d031a06f5d6f4750009672db98a5aa1536aa4a231713852469ce394779a23d75
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  device_info_plus:
    dependency: "direct main"
    description:
      name: device_info_plus
      sha256: "72d146c6d7098689ff5c5f66bcf593ac11efc530095385356e131070333e64da"
      url: "https://pub.dev"
    source: hosted
    version: "11.3.0"
  device_info_plus_platform_interface:
    dependency: transitive
    description:
      name: device_info_plus_platform_interface
      sha256: "0b04e02b30791224b31969eb1b50d723498f402971bff3630bca2ba839bd1ed2"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.2"
  device_preview:
    dependency: "direct main"
    description:
      name: device_preview
      sha256: a694acdd3894b4c7d600f4ee413afc4ff917f76026b97ab06575fe886429ef19
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  dio:
    dependency: "direct main"
    description:
      name: dio
      sha256: "253a18bbd4851fecba42f7343a1df3a9a4c1d31a2c1b37e221086b4fa8c8dbc9"
      url: "https://pub.dev"
    source: hosted
    version: "5.8.0+1"
  dio_web_adapter:
    dependency: transitive
    description:
      name: dio_web_adapter
      sha256: "7586e476d70caecaf1686d21eee7247ea43ef5c345eab9e0cc3583ff13378d78"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  dotted_border:
    dependency: "direct main"
    description:
      name: dotted_border
      sha256: "108837e11848ca776c53b30bc870086f84b62ed6e01c503ed976e8f8c7df9c04"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  dropdown_search:
    dependency: "direct main"
    description:
      name: dropdown_search
      sha256: "55106e8290acaa97ed15bea1fdad82c3cf0c248dd410e651f5a8ac6870f783ab"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.6"
  easy_localization:
    dependency: "direct main"
    description:
      name: easy_localization
      sha256: c145aeb6584aedc7c862ab8c737c3277788f47488bfdf9bae0fe112bd0a4789c
      url: "https://pub.dev"
    source: hosted
    version: "3.0.5"
  easy_localization_loader:
    dependency: "direct main"
    description:
      name: easy_localization_loader
      sha256: "89e70b2516123d639e8e68b8dd08fc07c8b83b0cfee17c642165e7e8807ebc86"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1+1"
  easy_logger:
    dependency: transitive
    description:
      name: easy_logger
      sha256: c764a6e024846f33405a2342caf91c62e357c24b02c04dbc712ef232bf30ffb7
      url: "https://pub.dev"
    source: hosted
    version: "0.0.2"
  encrypt:
    dependency: transitive
    description:
      name: encrypt
      sha256: "62d9aa4670cc2a8798bab89b39fc71b6dfbacf615de6cf5001fb39f7e4a996a2"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.3"
  epubx:
    dependency: "direct main"
    description:
      name: epubx
      sha256: "0ab9354efa177c4be52c46f857bc15bf83f83a92667fb673465c8f89fca26db3"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  equatable:
    dependency: "direct main"
    description:
      name: equatable
      sha256: c2b87cb7756efdf69892005af546c56c0b5037f54d2a88269b4f347a505e3ca2
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  external_path:
    dependency: "direct main"
    description:
      name: external_path
      sha256: "68a18a2aa51ec012d7013ea2a80305dc5372f3577a2bbcc7dcc5550b25a5a73b"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  facesdk_plugin:
    dependency: "direct dev"
    description:
      path: facesdk_plugin
      relative: true
    source: path
    version: "0.0.1"
  fading_edge_scrollview:
    dependency: "direct main"
    description:
      name: fading_edge_scrollview
      sha256: "1f84fe3ea8e251d00d5735e27502a6a250e4aa3d3b330d3fdcb475af741464ef"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.1"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "5368f224a74523e8d2e7399ea1638b37aecfca824a3cc4dfdf77bf1fa905ac44"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.3"
  fancy_indicator:
    dependency: "direct main"
    description:
      name: fancy_indicator
      sha256: "84dcd8568e1ba3ab9db031f4e00f1fb8b26b5a168656fe0743ba68593ef76cd7"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.4"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: "16ed7b077ef01ad6170a3d0c57caa4a112a38d7a2ed5602e0aca9ca6f3d98da6"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  ffmpeg_kit_flutter_android:
    dependency: transitive
    description:
      name: ffmpeg_kit_flutter_android
      sha256: "6fe3ece6a8c16b04f695dab9f58b39fc35e617fce8221d4183dd027e59f71c01"
      url: "https://pub.dev"
    source: hosted
    version: "1.7.0"
  ffmpeg_kit_flutter_new:
    dependency: "direct main"
    description:
      name: ffmpeg_kit_flutter_new
      sha256: dbaf0f4963b08a034a4f787276c1e12efc36a56670693f1721e46f95554f0979
      url: "https://pub.dev"
    source: hosted
    version: "1.6.1"
  ffmpeg_kit_flutter_platform_interface:
    dependency: transitive
    description:
      name: ffmpeg_kit_flutter_platform_interface
      sha256: addf046ae44e190ad0101b2fde2ad909a3cd08a2a109f6106d2f7048b7abedee
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1"
  file:
    dependency: transitive
    description:
      name: file
      sha256: "5fc22d7c25582e38ad9a8515372cd9a93834027aacf1801cf01164dac0ffa08c"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  file_picker:
    dependency: "direct main"
    description:
      name: file_picker
      sha256: "09b474c0c8117484b80cbebc043801ff91e05cfbd2874d512825c899e1754694"
      url: "https://pub.dev"
    source: hosted
    version: "9.2.3"
  file_saver:
    dependency: "direct main"
    description:
      name: file_saver
      sha256: "017a127de686af2d2fbbd64afea97052d95f2a0f87d19d25b87e097407bf9c1e"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.14"
  file_selector_linux:
    dependency: transitive
    description:
      name: file_selector_linux
      sha256: "54cbbd957e1156d29548c7d9b9ec0c0ebb6de0a90452198683a7d23aed617a33"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3+2"
  file_selector_macos:
    dependency: transitive
    description:
      name: file_selector_macos
      sha256: "271ab9986df0c135d45c3cdb6bd0faa5db6f4976d3e4b437cf7d0f258d941bfc"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.4+2"
  file_selector_platform_interface:
    dependency: transitive
    description:
      name: file_selector_platform_interface
      sha256: a3994c26f10378a039faa11de174d7b78eb8f79e4dd0af2a451410c1a5c3f66b
      url: "https://pub.dev"
    source: hosted
    version: "2.6.2"
  file_selector_windows:
    dependency: transitive
    description:
      name: file_selector_windows
      sha256: "320fcfb6f33caa90f0b58380489fc5ac05d99ee94b61aa96ec2bff0ba81d3c2b"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3+4"
  firebase_core:
    dependency: "direct main"
    description:
      name: firebase_core
      sha256: "96607c0e829a581c2a483c658f04e8b159964c3bae2730f73297070bc85d40bb"
      url: "https://pub.dev"
    source: hosted
    version: "2.24.2"
  firebase_core_platform_interface:
    dependency: transitive
    description:
      name: firebase_core_platform_interface
      sha256: c437ae5d17e6b5cc7981cf6fd458a5db4d12979905f9aafd1fea930428a9fe63
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      sha256: d585bdf3c656c3f7821ba1bd44da5f13365d22fcecaf5eb75c4295246aaa83c0
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  firebase_crashlytics:
    dependency: "direct main"
    description:
      name: firebase_crashlytics
      sha256: "5125b7f3fcef2bfdd7e071afe7edcefd9597968003e44e073456c773d91694ee"
      url: "https://pub.dev"
    source: hosted
    version: "3.4.9"
  firebase_crashlytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_crashlytics_platform_interface
      sha256: "359197344def001589c84f8d1d57c05f6e2e773f559205610ce58c25e2045a57"
      url: "https://pub.dev"
    source: hosted
    version: "3.6.16"
  firebase_messaging:
    dependency: "direct main"
    description:
      name: firebase_messaging
      sha256: "980259425fa5e2afc03e533f33723335731d21a56fd255611083bceebf4373a8"
      url: "https://pub.dev"
    source: hosted
    version: "14.7.10"
  firebase_messaging_platform_interface:
    dependency: transitive
    description:
      name: firebase_messaging_platform_interface
      sha256: "54e283a0e41d81d854636ad0dad73066adc53407a60a7c3189c9656e2f1b6107"
      url: "https://pub.dev"
    source: hosted
    version: "4.5.18"
  firebase_messaging_web:
    dependency: transitive
    description:
      name: firebase_messaging_web
      sha256: "90dc7ed885e90a24bb0e56d661d4d2b5f84429697fd2cbb9e5890a0ca370e6f4"
      url: "https://pub.dev"
    source: hosted
    version: "3.5.18"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: "25517a4deb0c03aa0f32fd12db525856438902d9c16536311e76cdc57b31d7d1"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_alice:
    dependency: "direct main"
    description:
      name: flutter_alice
      sha256: "4d701d0db1cf3fb9a53e5d411ed5fef60a3563ea8b2d2c231614910644a3ca21"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  flutter_animate:
    dependency: "direct main"
    description:
      name: flutter_animate
      sha256: "3cb5eb80827abb48e362a9f29f7ba4e1b0edba6b2e2f35b1d350b6ef069bb547"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  flutter_awesome_select:
    dependency: "direct main"
    description:
      name: flutter_awesome_select
      sha256: f7beb2704bd3128e4bcd6931e741971cdebf5e13ef01e8e090349f89699e1d12
      url: "https://pub.dev"
    source: hosted
    version: "6.5.0"
  flutter_background_service:
    dependency: "direct main"
    description:
      name: flutter_background_service
      sha256: "94d9a143852729140e17254a53769383b03738cd92b6e588a8762003e6cd9dd9"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.5"
  flutter_background_service_android:
    dependency: "direct main"
    description:
      name: flutter_background_service_android
      sha256: "30863ebafd8214b8e76d5e5c9f27887dc5cc303fcf3e89f71534f621fc486782"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.2"
  flutter_background_service_ios:
    dependency: "direct main"
    description:
      name: flutter_background_service_ios
      sha256: ab73657535876e16abc89e40f924df3e92ad3dee83f64d187081417e824709ed
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  flutter_background_service_platform_interface:
    dependency: transitive
    description:
      name: flutter_background_service_platform_interface
      sha256: cd5720ff5b051d551a4734fae16683aace779bd0425e8d3f15d84a0cdcc2d8d9
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  flutter_bloc:
    dependency: "direct main"
    description:
      name: flutter_bloc
      sha256: "87325da1ac757fcc4813e6b34ed5dd61169973871fdf181d6c2109dd6935ece1"
      url: "https://pub.dev"
    source: hosted
    version: "8.1.4"
  flutter_cache_manager:
    dependency: transitive
    description:
      name: flutter_cache_manager
      sha256: "8207f27539deb83732fdda03e259349046a39a4c767269285f449ade355d54ba"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.1"
  flutter_gen_core:
    dependency: "direct overridden"
    description:
      name: flutter_gen_core
      sha256: "3eaa2d3d8be58267ac4cd5e215ac965dd23cae0410dc073de2e82e227be32bfc"
      url: "https://pub.dev"
    source: hosted
    version: "5.10.0"
  flutter_gen_runner:
    dependency: "direct dev"
    description:
      name: flutter_gen_runner
      sha256: e74b4ead01df3e8f02e73a26ca856759dbbe8cb3fd60941ba9f4005cd0cd19c9
      url: "https://pub.dev"
    source: hosted
    version: "5.10.0"
  flutter_gradients_reborn:
    dependency: "direct main"
    description:
      name: flutter_gradients_reborn
      sha256: b1e85754c31d88f9dd7dd98eca8da9b9d2c75bfff197ac4e918f6ef4e1d22bb5
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0+7"
  flutter_html:
    dependency: "direct main"
    description:
      name: flutter_html
      sha256: "02ad69e813ecfc0728a455e4bf892b9379983e050722b1dce00192ee2e41d1ee"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0-beta.2"
  flutter_image_compress:
    dependency: "direct main"
    description:
      name: flutter_image_compress
      sha256: "51d23be39efc2185e72e290042a0da41aed70b14ef97db362a6b5368d0523b27"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  flutter_image_compress_common:
    dependency: transitive
    description:
      name: flutter_image_compress_common
      sha256: c5c5d50c15e97dd7dc72ff96bd7077b9f791932f2076c5c5b6c43f2c88607bfb
      url: "https://pub.dev"
    source: hosted
    version: "1.0.6"
  flutter_image_compress_macos:
    dependency: transitive
    description:
      name: flutter_image_compress_macos
      sha256: "20019719b71b743aba0ef874ed29c50747461e5e8438980dfa5c2031898f7337"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  flutter_image_compress_ohos:
    dependency: transitive
    description:
      name: flutter_image_compress_ohos
      sha256: e76b92bbc830ee08f5b05962fc78a532011fcd2041f620b5400a593e96da3f51
      url: "https://pub.dev"
    source: hosted
    version: "0.0.3"
  flutter_image_compress_platform_interface:
    dependency: transitive
    description:
      name: flutter_image_compress_platform_interface
      sha256: "579cb3947fd4309103afe6442a01ca01e1e6f93dc53bb4cbd090e8ce34a41889"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.5"
  flutter_image_compress_web:
    dependency: transitive
    description:
      name: flutter_image_compress_web
      sha256: b9b141ac7c686a2ce7bb9a98176321e1182c9074650e47bb140741a44b6f5a96
      url: "https://pub.dev"
    source: hosted
    version: "0.1.5"
  flutter_inappwebview:
    dependency: "direct overridden"
    description:
      name: flutter_inappwebview
      sha256: "80092d13d3e29b6227e25b67973c67c7210bd5e35c4b747ca908e31eb71a46d5"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.5"
  flutter_inappwebview_android:
    dependency: transitive
    description:
      name: flutter_inappwebview_android
      sha256: "62557c15a5c2db5d195cb3892aab74fcaec266d7b86d59a6f0027abd672cddba"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.3"
  flutter_inappwebview_internal_annotations:
    dependency: transitive
    description:
      name: flutter_inappwebview_internal_annotations
      sha256: "5f80fd30e208ddded7dbbcd0d569e7995f9f63d45ea3f548d8dd4c0b473fb4c8"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  flutter_inappwebview_ios:
    dependency: transitive
    description:
      name: flutter_inappwebview_ios
      sha256: "5818cf9b26cf0cbb0f62ff50772217d41ea8d3d9cc00279c45f8aabaa1b4025d"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  flutter_inappwebview_macos:
    dependency: transitive
    description:
      name: flutter_inappwebview_macos
      sha256: c1fbb86af1a3738e3541364d7d1866315ffb0468a1a77e34198c9be571287da1
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  flutter_inappwebview_platform_interface:
    dependency: transitive
    description:
      name: flutter_inappwebview_platform_interface
      sha256: cf5323e194096b6ede7a1ca808c3e0a078e4b33cc3f6338977d75b4024ba2500
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0+1"
  flutter_inappwebview_web:
    dependency: transitive
    description:
      name: flutter_inappwebview_web
      sha256: "55f89c83b0a0d3b7893306b3bb545ba4770a4df018204917148ebb42dc14a598"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  flutter_inappwebview_windows:
    dependency: transitive
    description:
      name: flutter_inappwebview_windows
      sha256: "8b4d3a46078a2cdc636c4a3d10d10f2a16882f6be607962dbfff8874d1642055"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.0"
  flutter_launcher_icons:
    dependency: "direct dev"
    description:
      name: flutter_launcher_icons
      sha256: ce0e501cfc258907842238e4ca605e74b7fd1cdf04b3b43e86c43f3e40a1592c
      url: "https://pub.dev"
    source: hosted
    version: "0.11.0"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      sha256: e2a421b7e59244faef694ba7b30562e489c2b489866e505074eb005cd7060db7
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  flutter_local_notifications:
    dependency: "direct main"
    description:
      name: flutter_local_notifications
      sha256: "674173fd3c9eda9d4c8528da2ce0ea69f161577495a9cc835a2a4ecd7eadeb35"
      url: "https://pub.dev"
    source: hosted
    version: "17.2.4"
  flutter_local_notifications_linux:
    dependency: transitive
    description:
      name: flutter_local_notifications_linux
      sha256: c49bd06165cad9beeb79090b18cd1eb0296f4bf4b23b84426e37dd7c027fc3af
      url: "https://pub.dev"
    source: hosted
    version: "4.0.1"
  flutter_local_notifications_platform_interface:
    dependency: transitive
    description:
      name: flutter_local_notifications_platform_interface
      sha256: "85f8d07fe708c1bdcf45037f2c0109753b26ae077e9d9e899d55971711a4ea66"
      url: "https://pub.dev"
    source: hosted
    version: "7.2.0"
  flutter_localizations:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_mask_view:
    dependency: "direct main"
    description:
      name: flutter_mask_view
      sha256: d9cc42ebe048a433213f4438a5f26e4d84a349a5f419ebd6c1e0bfeb0bd222ac
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  flutter_native_image:
    dependency: "direct main"
    description:
      name: flutter_native_image
      sha256: "0ff23d6222064259df8f85ea56925627ea1ec8658814672c5b6c23fc9174c65e"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.6+1"
  flutter_native_splash:
    dependency: "direct dev"
    description:
      name: flutter_native_splash
      sha256: "6777a3abb974021a39b5fdd2d46a03ca390e03903b6351f21d10e7ecc969f12d"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.16"
  flutter_network_connectivity:
    dependency: "direct main"
    description:
      name: flutter_network_connectivity
      sha256: "3dddee42e06f8479b021cc17b60ea891cf71b124c03896d4fedec366671cb623"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.6"
  flutter_pdfview:
    dependency: "direct main"
    description:
      name: flutter_pdfview
      sha256: a9055bf920c7095bf08c2781db431ba23577aa5da5a056a7152dc89a18fbec6f
      url: "https://pub.dev"
    source: hosted
    version: "1.3.2"
  flutter_phoenix:
    dependency: "direct main"
    description:
      name: flutter_phoenix
      sha256: "39589dac934ea476d0e43fb60c1ddfba58f14960743640c8250dea11c4333378"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      sha256: f948e346c12f8d5480d2825e03de228d0eb8c3a737e4cdaa122267b89c022b5e
      url: "https://pub.dev"
    source: hosted
    version: "2.0.28"
  flutter_screenutil:
    dependency: "direct main"
    description:
      name: flutter_screenutil
      sha256: "8cf100b8e4973dc570b6415a2090b0bfaa8756ad333db46939efc3e774ee100d"
      url: "https://pub.dev"
    source: hosted
    version: "5.9.0"
  flutter_session_manager:
    dependency: "direct main"
    description:
      name: flutter_session_manager
      sha256: "5483f1c245ee7a3bc294d93b4d73d6a068d5c0df292ccd4b48bf8b4460f0cd19"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  flutter_slidable:
    dependency: "direct main"
    description:
      name: flutter_slidable
      sha256: "19ed4813003a6ff4e9c6bcce37e792a2a358919d7603b2b31ff200229191e44c"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  flutter_staggered_grid_view:
    dependency: transitive
    description:
      name: flutter_staggered_grid_view
      sha256: "19e7abb550c96fbfeb546b23f3ff356ee7c59a019a651f8f102a4ba9b7349395"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0"
  flutter_svg:
    dependency: "direct main"
    description:
      name: flutter_svg
      sha256: d39e7f95621fc84376bc0f7d504f05c3a41488c562f4a8ad410569127507402c
      url: "https://pub.dev"
    source: hosted
    version: "2.0.9"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_timezone:
    dependency: "direct main"
    description:
      name: flutter_timezone
      sha256: "06b35132c98fa188db3c4b654b7e1af7ccd01dfe12a004d58be423357605fb24"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.8"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  fluttertoast:
    dependency: "direct main"
    description:
      name: fluttertoast
      sha256: dfdde255317af381bfc1c486ed968d5a43a2ded9c931e87cbecd88767d6a71c1
      url: "https://pub.dev"
    source: hosted
    version: "8.2.4"
  freezed_annotation:
    dependency: transitive
    description:
      name: freezed_annotation
      sha256: c2e2d632dd9b8a2b7751117abcfc2b4888ecfe181bd9fca7170d9ef02e595fe2
      url: "https://pub.dev"
    source: hosted
    version: "2.4.4"
  frontend_server_client:
    dependency: transitive
    description:
      name: frontend_server_client
      sha256: "408e3ca148b31c20282ad6f37ebfa6f4bdc8fede5b74bc2f08d9d92b55db3612"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  gal:
    dependency: "direct main"
    description:
      name: gal
      sha256: "2771519c8b29f784d5e27f4efc2667667eef51c6c47cccaa0435a8fe8aa208e4"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  geolocator:
    dependency: "direct main"
    description:
      name: geolocator
      sha256: f4efb8d3c4cdcad2e226af9661eb1a0dd38c71a9494b22526f9da80ab79520e5
      url: "https://pub.dev"
    source: hosted
    version: "10.1.1"
  geolocator_android:
    dependency: transitive
    description:
      name: geolocator_android
      sha256: "93906636752ea4d4e778afa981fdfe7409f545b3147046300df194330044d349"
      url: "https://pub.dev"
    source: hosted
    version: "4.3.1"
  geolocator_apple:
    dependency: transitive
    description:
      name: geolocator_apple
      sha256: "2f2d4ee16c4df269e93c0e382be075cc01d5db6703c3196e4af20a634fe49ef4"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.6"
  geolocator_platform_interface:
    dependency: transitive
    description:
      name: geolocator_platform_interface
      sha256: "009a21c4bc2761e58dccf07c24f219adaebe0ff707abdfd40b0a763d4003fab9"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.2"
  geolocator_web:
    dependency: transitive
    description:
      name: geolocator_web
      sha256: "102e7da05b48ca6bf0a5bda0010f886b171d1a08059f01bfe02addd0175ebece"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  geolocator_windows:
    dependency: transitive
    description:
      name: geolocator_windows
      sha256: a92fae29779d5c6dc60e8411302f5221ade464968fe80a36d330e80a71f087af
      url: "https://pub.dev"
    source: hosted
    version: "0.2.2"
  get:
    dependency: "direct main"
    description:
      name: get
      sha256: e4e7335ede17452b391ed3b2ede016545706c01a02292a6c97619705e7d2a85e
      url: "https://pub.dev"
    source: hosted
    version: "4.6.6"
  get_it:
    dependency: "direct main"
    description:
      name: get_it
      sha256: e6017ce7fdeaf218dc51a100344d8cb70134b80e28b760f8bb23c242437bafd7
      url: "https://pub.dev"
    source: hosted
    version: "7.6.7"
  get_storage:
    dependency: "direct main"
    description:
      name: get_storage
      sha256: "39db1fffe779d0c22b3a744376e86febe4ade43bf65e06eab5af707dc84185a2"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  glob:
    dependency: transitive
    description:
      name: glob
      sha256: "0e7014b3b7d4dac1ca4d6114f82bf1782ee86745b9b42a92c9289c23d8a0ab63"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  google_fonts:
    dependency: "direct main"
    description:
      name: google_fonts
      sha256: "6b6f10f0ce3c42f6552d1c70d2c28d764cf22bb487f50f66cca31dcd5194f4d6"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.4"
  google_maps:
    dependency: transitive
    description:
      name: google_maps
      sha256: "555d5d736339b0478e821167ac521c810d7b51c3b2734e6802a9f046b64ea37a"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.0"
  google_maps_flutter:
    dependency: "direct main"
    description:
      name: google_maps_flutter
      sha256: ae66fef3e71261d7df2eff29b2a119e190b2884325ecaa55321b1e17b5504066
      url: "https://pub.dev"
    source: hosted
    version: "2.5.3"
  google_maps_flutter_android:
    dependency: transitive
    description:
      name: google_maps_flutter_android
      sha256: "256b3c974e415bd17555ceff76a5d0badd2cbfd29febfc23070993358f639550"
      url: "https://pub.dev"
    source: hosted
    version: "2.7.0"
  google_maps_flutter_ios:
    dependency: transitive
    description:
      name: google_maps_flutter_ios
      sha256: c89556ed0338fcb4b843c9fcdafac7ad2df601c8b41286d82f0727f7f66434e4
      url: "https://pub.dev"
    source: hosted
    version: "2.3.6"
  google_maps_flutter_platform_interface:
    dependency: transitive
    description:
      name: google_maps_flutter_platform_interface
      sha256: "167af879da4d004cd58771f1469b91dcc3b9b0a2c5334cc6bf71fd41d4b35403"
      url: "https://pub.dev"
    source: hosted
    version: "2.6.0"
  google_maps_flutter_web:
    dependency: transitive
    description:
      name: google_maps_flutter_web
      sha256: "6245721c160d6f531c1ef568cf9bef8d660cd585a982aa75121269030163785a"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.4+3"
  google_nav_bar:
    dependency: "direct main"
    description:
      name: google_nav_bar
      sha256: "1c8e3882fa66ee7b74c24320668276ca23affbd58f0b14a24c1e5590f4d07ab0"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.6"
  graphs:
    dependency: transitive
    description:
      name: graphs
      sha256: aedc5a15e78fc65a6e23bcd927f24c64dd995062bcd1ca6eda65a3cff92a4d19
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  group_radio_button:
    dependency: "direct main"
    description:
      name: group_radio_button
      sha256: "204de8d16b224be7fc72dade0c3afd410ff5a34417d89f74f0fd8be7a8c2b4d6"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  grouped_list:
    dependency: "direct main"
    description:
      name: grouped_list
      sha256: fef106470186081c32636aa055492eee7fc7fe8bf0921a48d31ded24821af19f
      url: "https://pub.dev"
    source: hosted
    version: "5.1.2"
  hashcodes:
    dependency: transitive
    description:
      name: hashcodes
      sha256: "80f9410a5b3c8e110c4b7604546034749259f5d6dcca63e0d3c17c9258f1a651"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  html:
    dependency: transitive
    description:
      name: html
      sha256: "3a7812d5bcd2894edf53dfaf8cd640876cf6cef50a8f238745c8b8120ea74d3a"
      url: "https://pub.dev"
    source: hosted
    version: "0.15.4"
  http:
    dependency: transitive
    description:
      name: http
      sha256: "5895291c13fa8a3bd82e76d5627f69e0d85ca6a30dcac95c4ea19a5d555879c2"
      url: "https://pub.dev"
    source: hosted
    version: "0.13.6"
  http_interceptor:
    dependency: "direct main"
    description:
      name: http_interceptor
      sha256: "5f3dde028e67789339c250252c09510a74aff21ce16b06d07d9096bda6582bab"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  http_multi_server:
    dependency: transitive
    description:
      name: http_multi_server
      sha256: "97486f20f9c2f7be8f514851703d0119c3596d14ea63227af6f7a481ef2b2f8b"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  http_parser:
    dependency: "direct main"
    description:
      name: http_parser
      sha256: "2aa08ce0341cc9b354a498388e30986515406668dbcc4f7c950c3e715496693b"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.2"
  image:
    dependency: transitive
    description:
      name: image
      sha256: "8e9d133755c3e84c73288363e6343157c383a0c6c56fc51afcc5d4d7180306d6"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.0"
  image_editor:
    dependency: "direct main"
    description:
      name: image_editor
      sha256: "0fe70befea0dbaf24a7cacc32c28311a65118f66637997ad072e9063f59efdd8"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.1"
  image_editor_common:
    dependency: transitive
    description:
      name: image_editor_common
      sha256: d141c0847148a7da573a5be5ca02e70d381e61cb6484ebef52a230ca1d6c56ab
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  image_editor_ohos:
    dependency: transitive
    description:
      name: image_editor_ohos
      sha256: "06756859586d5acefec6e3b4f356f9b1ce05ef09213bcb9a0ce1680ecea2d054"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.9"
  image_editor_platform_interface:
    dependency: transitive
    description:
      name: image_editor_platform_interface
      sha256: "474517efc770464f7d99942472d8cfb369a3c378e95466ec17f74d2b80bd40de"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  image_picker:
    dependency: "direct main"
    description:
      name: image_picker
      sha256: "021834d9c0c3de46bf0fe40341fa07168407f694d9b2bb18d532dc1261867f7a"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  image_picker_android:
    dependency: transitive
    description:
      name: image_picker_android
      sha256: "317a5d961cec5b34e777b9252393f2afbd23084aa6e60fcf601dcf6341b9ebeb"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.12+23"
  image_picker_for_web:
    dependency: transitive
    description:
      name: image_picker_for_web
      sha256: "717eb042ab08c40767684327be06a5d8dbb341fe791d514e4b92c7bbe1b7bb83"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.6"
  image_picker_ios:
    dependency: transitive
    description:
      name: image_picker_ios
      sha256: "05da758e67bc7839e886b3959848aa6b44ff123ab4b28f67891008afe8ef9100"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.12+2"
  image_picker_linux:
    dependency: transitive
    description:
      name: image_picker_linux
      sha256: "34a65f6740df08bbbeb0a1abd8e6d32107941fd4868f67a507b25601651022c9"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+2"
  image_picker_macos:
    dependency: transitive
    description:
      name: image_picker_macos
      sha256: "1b90ebbd9dcf98fb6c1d01427e49a55bd96b5d67b8c67cf955d60a5de74207c1"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+2"
  image_picker_platform_interface:
    dependency: transitive
    description:
      name: image_picker_platform_interface
      sha256: "886d57f0be73c4b140004e78b9f28a8914a09e50c2d816bdd0520051a71236a0"
      url: "https://pub.dev"
    source: hosted
    version: "2.10.1"
  image_picker_windows:
    dependency: transitive
    description:
      name: image_picker_windows
      sha256: "6ad07afc4eb1bc25f3a01084d28520496c4a3bb0cb13685435838167c9dcedeb"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  image_size_getter:
    dependency: transitive
    description:
      name: image_size_getter
      sha256: "9a299e3af2ebbcfd1baf21456c3c884037ff524316c97d8e56035ea8fdf35653"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  in_app_update:
    dependency: "direct main"
    description:
      name: in_app_update
      sha256: b6ccb757281a96a4b18536f68fe2567aeca865134218719364212da8fe94615c
      url: "https://pub.dev"
    source: hosted
    version: "4.2.2"
  infinite_scroll_pagination:
    dependency: "direct main"
    description:
      name: infinite_scroll_pagination
      sha256: b68bce20752fcf36c7739e60de4175494f74e99e9a69b4dd2fe3a1dd07a7f16a
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  internet_connection_checker:
    dependency: "direct main"
    description:
      name: internet_connection_checker
      sha256: "1c683e63e89c9ac66a40748b1b20889fd9804980da732bf2b58d6d5456c8e876"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0+1"
  intl:
    dependency: "direct main"
    description:
      name: intl
      sha256: "3df61194eb431efc39c4ceba583b95633a403f46c9fd341e550ce0bfa50e9aa5"
      url: "https://pub.dev"
    source: hosted
    version: "0.20.2"
  io:
    dependency: transitive
    description:
      name: io
      sha256: "2ec25704aba361659e10e3e5f5d672068d332fc8ac516421d483a11e5cbd061e"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  isar:
    dependency: "direct main"
    description:
      name: isar
      sha256: "99165dadb2cf2329d3140198363a7e7bff9bbd441871898a87e26914d25cf1ea"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0+1"
  isar_flutter_libs:
    dependency: "direct main"
    description:
      name: isar_flutter_libs
      sha256: bc6768cc4b9c61aabff77152e7f33b4b17d2fc93134f7af1c3dd51500fe8d5e8
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0+1"
  isar_generator:
    dependency: "direct dev"
    description:
      name: isar_generator
      sha256: "76c121e1295a30423604f2f819bc255bc79f852f3bc8743a24017df6068ad133"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0+1"
  js:
    dependency: transitive
    description:
      name: js
      sha256: f2c445dce49627136094980615a031419f7f3eb393237e4ecd97ac15dea343f3
      url: "https://pub.dev"
    source: hosted
    version: "0.6.7"
  js_wrapping:
    dependency: transitive
    description:
      name: js_wrapping
      sha256: e385980f7c76a8c1c9a560dfb623b890975841542471eade630b2871d243851c
      url: "https://pub.dev"
    source: hosted
    version: "0.7.4"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      sha256: "1ce844379ca14835a50d2f019a3099f419082cfdd231cd86a142af94dd5c6bb1"
      url: "https://pub.dev"
    source: hosted
    version: "4.9.0"
  jwt_decoder:
    dependency: "direct main"
    description:
      name: jwt_decoder
      sha256: "54774aebf83f2923b99e6416b4ea915d47af3bde56884eb622de85feabbc559f"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  keyboard_dismisser:
    dependency: "direct main"
    description:
      name: keyboard_dismisser
      sha256: f67e032581fc3dd1f77e1cb54c421b089e015d122aeba2490ba001cfcc42a181
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  kplayer:
    dependency: "direct main"
    description:
      name: kplayer
      sha256: "1c960922f9eff054ab75e65786bc79bc7c1b18347f80138e9bbee3a47e823d8f"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.2"
  kplayer_platform_interface:
    dependency: transitive
    description:
      name: kplayer_platform_interface
      sha256: "60ec85428976e58b8d27612f1adaffb35097eebb0451c31fc6625a5f34cd5e25"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.2"
  kplayer_with_audioplayers:
    dependency: transitive
    description:
      name: kplayer_with_audioplayers
      sha256: dd9c13281da52caa9f4ea2c79382e2e4931a7b4f26f5eed1b240a2be56cedb4f
      url: "https://pub.dev"
    source: hosted
    version: "0.4.2"
  leak_tracker:
    dependency: transitive
    description:
      name: leak_tracker
      sha256: "6bb818ecbdffe216e81182c2f0714a2e62b593f4a4f13098713ff1685dfb6ab0"
      url: "https://pub.dev"
    source: hosted
    version: "10.0.9"
  leak_tracker_flutter_testing:
    dependency: transitive
    description:
      name: leak_tracker_flutter_testing
      sha256: f8b613e7e6a13ec79cfdc0e97638fddb3ab848452eff057653abd3edba760573
      url: "https://pub.dev"
    source: hosted
    version: "3.0.9"
  leak_tracker_testing:
    dependency: transitive
    description:
      name: leak_tracker_testing
      sha256: "6ba465d5d76e67ddf503e1161d1f4a6bc42306f9d66ca1e8f079a47290fb06d3"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  lints:
    dependency: transitive
    description:
      name: lints
      sha256: cbf8d4b858bb0134ef3ef87841abdf8d63bfc255c266b7bf6b39daa1085c4290
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  list_counter:
    dependency: transitive
    description:
      name: list_counter
      sha256: c447ae3dfcd1c55f0152867090e67e219d42fe6d4f2807db4bbe8b8d69912237
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  lite_rolling_switch:
    dependency: "direct main"
    description:
      name: lite_rolling_switch
      sha256: f5685911d249534ac6ac9862622f18846d31799c90fa4600b475d902d132b090
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  local_auth:
    dependency: "direct main"
    description:
      name: local_auth
      sha256: "27679ed8e0d7daab2357db6bb7076359e083a56b295c0c59723845301da6aed9"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.8"
  local_auth_android:
    dependency: transitive
    description:
      name: local_auth_android
      sha256: "3bcd732dda7c75fcb7ddaef12e131230f53dcc8c00790d0d6efb3aa0fbbeda57"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.37"
  local_auth_ios:
    dependency: transitive
    description:
      name: local_auth_ios
      sha256: eb283b530029b334698918f1e282d4483737cbca972ff21b9193be3d6de8e2b8
      url: "https://pub.dev"
    source: hosted
    version: "1.1.6"
  local_auth_platform_interface:
    dependency: transitive
    description:
      name: local_auth_platform_interface
      sha256: "1b842ff177a7068442eae093b64abe3592f816afd2a533c0ebcdbe40f9d2075a"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.10"
  local_auth_windows:
    dependency: transitive
    description:
      name: local_auth_windows
      sha256: "505ba3367ca781efb1c50d3132e44a2446bccc4163427bc203b9b4d8994d97ea"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.10"
  logger:
    dependency: "direct main"
    description:
      name: logger
      sha256: b3ff55aeb08d9d8901b767650285872cb1bb8f508373b3e348d60268b0c7f770
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  logging:
    dependency: transitive
    description:
      name: logging
      sha256: "623a88c9594aa774443aa3eb2d41807a48486b5613e67599fb4c41c0ad47c340"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  lottie:
    dependency: "direct main"
    description:
      name: lottie
      sha256: a93542cc2d60a7057255405f62252533f8e8956e7e06754955669fd32fb4b216
      url: "https://pub.dev"
    source: hosted
    version: "2.7.0"
  macros:
    dependency: transitive
    description:
      name: macros
      sha256: "1d9e801cd66f7ea3663c45fc708450db1fa57f988142c64289142c9b7ee80656"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3-main.0"
  mask_text_input_formatter:
    dependency: "direct main"
    description:
      name: mask_text_input_formatter
      sha256: "978c58ec721c25621ceb468e633f4eef64b64d45424ac4540e0565d4f7c800cd"
      url: "https://pub.dev"
    source: hosted
    version: "2.9.0"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: dc58c723c3c24bf8d3e2d3ad3f2f9d7bd9cf43ec6feaa64181775e60190153f2
      url: "https://pub.dev"
    source: hosted
    version: "0.12.17"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: f7142bb1154231d7ea5f96bc7bde4bda2a0945d2806bb11670e30b850d56bdec
      url: "https://pub.dev"
    source: hosted
    version: "0.11.1"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: e3641ec5d63ebf0d9b41bd43201a66e3fc79a65db5f61fc181f04cd27aab950c
      url: "https://pub.dev"
    source: hosted
    version: "1.16.0"
  mime:
    dependency: "direct main"
    description:
      name: mime
      sha256: e4ff8e8564c03f255408decd16e7899da1733852a9110a58fe6d1b817684a63e
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  mno_zoom_widget:
    dependency: "direct main"
    description:
      name: mno_zoom_widget
      sha256: "2bfb0702a0b7a28d34975f03900a4fa56938545beb6caa7fa8599810a6f94f6f"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.0"
  native_device_orientation:
    dependency: "direct main"
    description:
      name: native_device_orientation
      sha256: "0c330c068575e4be72cce5968ca479a3f8d5d1e5dfce7d89d5c13a1e943b338c"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  nested:
    dependency: transitive
    description:
      name: nested
      sha256: "03bac4c528c64c95c722ec99280375a6f2fc708eec17c7b3f07253b626cd2a20"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  nm:
    dependency: transitive
    description:
      name: nm
      sha256: "2c9aae4127bdc8993206464fcc063611e0e36e72018696cd9631023a31b24254"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.0"
  ntp:
    dependency: "direct main"
    description:
      name: ntp
      sha256: "198db73e5059b334b50dbe8c626011c26576778ee9fc53f4c55c1d89d08ed2d2"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  octo_image:
    dependency: transitive
    description:
      name: octo_image
      sha256: "45b40f99622f11901238e18d48f5f12ea36426d8eced9f4cbf58479c7aa2430d"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  open_filex:
    dependency: "direct main"
    description:
      name: open_filex
      sha256: "9976da61b6a72302cf3b1efbce259200cd40232643a467aac7370addf94d6900"
      url: "https://pub.dev"
    source: hosted
    version: "4.7.0"
  overlay_support:
    dependency: "direct main"
    description:
      name: overlay_support
      sha256: fc39389bfd94e6985e1e13b2a88a125fc4027608485d2d4e2847afe1b2bb339c
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  package_config:
    dependency: transitive
    description:
      name: package_config
      sha256: "1c5b77ccc91e4823a5af61ee74e6b972db1ef98c2ff5a18d3161c982a55448bd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  package_info_plus:
    dependency: "direct main"
    description:
      name: package_info_plus
      sha256: "7e76fad405b3e4016cd39d08f455a4eb5199723cf594cd1b8916d47140d93017"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.0"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      sha256: "9bc8ba46813a4cc42c66ab781470711781940780fd8beddd0c3da62506d3a6c6"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  page_transition:
    dependency: "direct main"
    description:
      name: page_transition
      sha256: dee976b1f23de9bbef5cd512fe567e9f6278caee11f5eaca9a2115c19dc49ef6
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  path:
    dependency: transitive
    description:
      name: path
      sha256: "75cca69d1490965be98c73ceaea117e8a04dd21217b37b292c9ddbec0d955bc5"
      url: "https://pub.dev"
    source: hosted
    version: "1.9.1"
  path_drawing:
    dependency: transitive
    description:
      name: path_drawing
      sha256: bbb1934c0cbb03091af082a6389ca2080345291ef07a5fa6d6e078ba8682f977
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: e3e67b1629e6f7e8100b367d3db6ba6af4b1f0bb80f64db18ef1fbabd2fa9ccf
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  path_provider:
    dependency: "direct main"
    description:
      name: path_provider
      sha256: "50c5dd5b6e1aaf6fb3a78b33f6aa3afca52bf903a8a5298f53101fdaee55bbcd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.5"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: d0d310befe2c8ab9e7f393288ccbb11b60c019c6b5afc21973eeee4dda2b35e9
      url: "https://pub.dev"
    source: hosted
    version: "2.2.17"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: "5a7999be66e000916500be4f15a3633ebceb8302719b47b9cc49ce924125350f"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "88f5779f72ba699763fa3a3b06aa4bf6de76c8e5de842cf6f29e2e06476c2334"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: bd6f00dbd873bfb70d0761682da2b3a2c2fccc2b9e84c495821639601d81afe7
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  pausable_timer:
    dependency: "direct main"
    description:
      name: pausable_timer
      sha256: "6ef1a95441ec3439de6fb63f39a011b67e693198e7dae14e20675c3c00e86074"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0+3"
  permission_handler:
    dependency: "direct main"
    description:
      name: permission_handler
      sha256: "284a66179cabdf942f838543e10413246f06424d960c92ba95c84439154fcac8"
      url: "https://pub.dev"
    source: hosted
    version: "11.0.1"
  permission_handler_android:
    dependency: transitive
    description:
      name: permission_handler_android
      sha256: f9fddd3b46109bd69ff3f9efa5006d2d309b7aec0f3c1c5637a60a2d5659e76e
      url: "https://pub.dev"
    source: hosted
    version: "11.1.0"
  permission_handler_apple:
    dependency: transitive
    description:
      name: permission_handler_apple
      sha256: "99e220bce3f8877c78e4ace901082fb29fa1b4ebde529ad0932d8d664b34f3f5"
      url: "https://pub.dev"
    source: hosted
    version: "9.1.4"
  permission_handler_platform_interface:
    dependency: transitive
    description:
      name: permission_handler_platform_interface
      sha256: "6760eb5ef34589224771010805bea6054ad28453906936f843a8cc4d3a55c4a4"
      url: "https://pub.dev"
    source: hosted
    version: "3.12.0"
  permission_handler_windows:
    dependency: transitive
    description:
      name: permission_handler_windows
      sha256: cc074aace208760f1eee6aa4fae766b45d947df85bc831cde77009cdb4720098
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: cb3798bef7fc021ac45b308f4b51208a152792445cce0448c9a4ba5879dd8750
      url: "https://pub.dev"
    source: hosted
    version: "5.4.0"
  pin_code_fields:
    dependency: "direct main"
    description:
      name: pin_code_fields
      sha256: "4c0db7fbc889e622e7c71ea54b9ee624bb70c7365b532abea0271b17ea75b729"
      url: "https://pub.dev"
    source: hosted
    version: "8.0.1"
  pinput:
    dependency: "direct main"
    description:
      name: pinput
      sha256: a92b55ecf9c25d1b9e100af45905385d5bc34fc9b6b04177a9e82cb88fe4d805
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "12220bb4b65720483f8fa9450b4332347737cf8213dd2840d8b2c823e47243ec"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.4"
  platform_device_id_linux:
    dependency: transitive
    description:
      name: platform_device_id_linux
      sha256: "994b1608593e527a629af2d5aeb241c60d308d3434bc78b0f6fcb3c1a02dff43"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  platform_device_id_macos:
    dependency: transitive
    description:
      name: platform_device_id_macos
      sha256: "968db2a504c611294b12a031b3734432d6df10553a0d3ae3b33ed21abfdbaba0"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  platform_device_id_platform_interface:
    dependency: transitive
    description:
      name: platform_device_id_platform_interface
      sha256: c61607594252aaddacf3e4c4371ab08f2ef85ff427817fa6e48a169429610c46
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  platform_device_id_plus:
    dependency: "direct main"
    description:
      name: platform_device_id_plus
      sha256: "14309170383ba2aaab4503bda6d668f7fee9bfe26077b69bc48f786c657d80c6"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.7"
  platform_device_id_web:
    dependency: transitive
    description:
      name: platform_device_id_web
      sha256: "58e124594e1165db7f108395a780b1d1e1cd403021978e5228cf4289fbe736d5"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  platform_device_id_windows:
    dependency: transitive
    description:
      name: platform_device_id_windows
      sha256: dbf8dcf03ad8555320ebae2403a3081b79f137f37661874e161fe2de0a84eeeb
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: "4820fbfdb9478b1ebae27888254d445073732dae3d6ea81f0b7e06d5dedc3f02"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.8"
  point_in_polygon:
    dependency: "direct main"
    description:
      path: "."
      ref: feature-fixes
      resolved-ref: a064ee4c81de0b71d442f88c6f6ec92d70865473
      url: "https://github.com/aa-cee/point_in_polygon.git"
    source: git
    version: "1.0.0"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      sha256: "43ac87de6e10afabc85c445745a7b799e04de84cebaa4fd7bf55a5e1e9604d29"
      url: "https://pub.dev"
    source: hosted
    version: "3.7.4"
  pool:
    dependency: transitive
    description:
      name: pool
      sha256: "20fe868b6314b322ea036ba325e6fc0711a22948856475e2c2b6306e8ab39c2a"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.1"
  provider:
    dependency: transitive
    description:
      name: provider
      sha256: c8a055ee5ce3fd98d6fc872478b03823ffdb448699c6ebdbbc71d59b596fd48c
      url: "https://pub.dev"
    source: hosted
    version: "6.1.2"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      sha256: "40d3ab1bbd474c4c2328c91e3a7df8c6dd629b79ece4c4bd04bee496a224fb0c"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  pubspec_parse:
    dependency: transitive
    description:
      name: pubspec_parse
      sha256: c63b2876e58e194e4b0828fcb080ad0e06d051cb607a6be51a9e084f47cb9367
      url: "https://pub.dev"
    source: hosted
    version: "1.2.3"
  quiver:
    dependency: transitive
    description:
      name: quiver
      sha256: ea0b925899e64ecdfbf9c7becb60d5b50e706ade44a85b2363be2a22d88117d2
      url: "https://pub.dev"
    source: hosted
    version: "3.2.2"
  record:
    dependency: transitive
    description:
      name: record
      sha256: "2e3d56d196abcd69f1046339b75e5f3855b2406fc087e5991f6703f188aa03a6"
      url: "https://pub.dev"
    source: hosted
    version: "5.2.1"
  record_android:
    dependency: transitive
    description:
      name: record_android
      sha256: "36e009c3b83e034321a44a7683d95dd055162a231f95600f7da579dcc79701f9"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  record_darwin:
    dependency: transitive
    description:
      name: record_darwin
      sha256: e487eccb19d82a9a39cd0126945cfc47b9986e0df211734e2788c95e3f63c82c
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  record_linux:
    dependency: transitive
    description:
      name: record_linux
      sha256: "74d41a9ebb1eb498a38e9a813dd524e8f0b4fdd627270bda9756f437b110a3e3"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.2"
  record_platform_interface:
    dependency: transitive
    description:
      name: record_platform_interface
      sha256: "8a575828733d4c3cb5983c914696f40db8667eab3538d4c41c50cbb79e722ef4"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  record_web:
    dependency: transitive
    description:
      name: record_web
      sha256: "654c08113961051dcb5427e63f56315ba47c0752781ba990dac9313d0ec23c70"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.6"
  record_windows:
    dependency: transitive
    description:
      name: record_windows
      sha256: "26bfebc8899f4fa5b6b044089887dc42115820cd6a907bdf40c16e909e87de0a"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.5"
  rename:
    dependency: "direct dev"
    description:
      name: rename
      sha256: "6ef5daf4b11130e71d93630cfb70725e5a35b19039739cfcd2b272c834ba25fe"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  rive:
    dependency: "direct main"
    description:
      name: rive
      sha256: fb18e38f9de28261e6f4a2d097dff419b4b741b13999fc6c2fd459481a0bbc6f
      url: "https://pub.dev"
    source: hosted
    version: "0.13.18"
  rive_common:
    dependency: transitive
    description:
      name: rive_common
      sha256: "031b29f7b1fc2732d06f60d053e0d7fbe0358fa19e991a91fa3991aa2760b240"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.13"
  rocket_singleton:
    dependency: "direct main"
    description:
      name: rocket_singleton
      sha256: "2c8a1c205a7193ff66a1952bd13a51ff17109ff8544f870bb9c7c58b1c3b5420"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.1"
  rxdart:
    dependency: "direct main"
    description:
      name: rxdart
      sha256: "0c7c0cedd93788d996e33041ffecda924cc54389199cde4e6a34b440f50044cb"
      url: "https://pub.dev"
    source: hosted
    version: "0.27.7"
  sanitize_html:
    dependency: transitive
    description:
      name: sanitize_html
      sha256: "12669c4a913688a26555323fb9cec373d8f9fbe091f2d01c40c723b33caa8989"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  screen_brightness:
    dependency: "direct main"
    description:
      name: screen_brightness
      sha256: "7d4ac84ae26b37c01d6f5db7123a72db7933e1f2a2a8c369a51e08f81b3178d8"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  screen_brightness_android:
    dependency: transitive
    description:
      name: screen_brightness_android
      sha256: "8c69d3ac475e4d625e7fa682a3a51a69ff59abe5b4a9e57f6ec7d830a6c69bd6"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  screen_brightness_ios:
    dependency: transitive
    description:
      name: screen_brightness_ios
      sha256: f08f70ca1ac3e30719764b5cfb8b3fe1e28163065018a41b3e6f243ab146c2f1
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  screen_brightness_macos:
    dependency: transitive
    description:
      name: screen_brightness_macos
      sha256: "70c2efa4534e22b927e82693488f127dd4a0f008469fccf4f0eefe9061bbdd6a"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  screen_brightness_platform_interface:
    dependency: transitive
    description:
      name: screen_brightness_platform_interface
      sha256: "9f3ebf7f22d5487e7676fe9ddaf3fc55b6ff8057707cf6dc0121c7dfda346a16"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  screen_brightness_windows:
    dependency: transitive
    description:
      name: screen_brightness_windows
      sha256: c8e12a91cf6dd912a48bd41fcf749282a51afa17f536c3460d8d05702fb89ffa
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  settings_ui:
    dependency: "direct main"
    description:
      name: settings_ui
      sha256: d9838037cb554b24b4218b2d07666fbada3478882edefae375ee892b6c820ef3
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  share:
    dependency: transitive
    description:
      name: share
      sha256: "97e6403f564ed1051a01534c2fc919cb6e40ea55e60a18ec23cee6e0ce19f4be"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.4"
  shared_preferences:
    dependency: "direct main"
    description:
      name: shared_preferences
      sha256: "746e5369a43170c25816cc472ee016d3a66bc13fcf430c0bc41ad7b4b2922051"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      sha256: "480ba4345773f56acda9abf5f50bd966f581dac5d514e5fc4a18c62976bbba7e"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  shared_preferences_foundation:
    dependency: transitive
    description:
      name: shared_preferences_foundation
      sha256: c4b35f6cb8f63c147312c054ce7c2254c8066745125264f0c88739c417fc9d9f
      url: "https://pub.dev"
    source: hosted
    version: "2.5.2"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      sha256: "580abfd40f415611503cae30adf626e6656dfb2f0cee8f465ece7b6defb40f2f"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      sha256: "57cbf196c486bc2cf1f02b85784932c6094376284b3ad5779d1b1c6c6a816b80"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      sha256: d2ca4132d3946fec2184261726b355836a82c33d7d5b67af32692aff18a4684e
      url: "https://pub.dev"
    source: hosted
    version: "2.4.2"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      sha256: "94ef0f72b2d71bc3e700e025db3710911bd51a71cefb65cc609dd0d9a982e3c1"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shelf:
    dependency: transitive
    description:
      name: shelf
      sha256: ad29c505aee705f41a4d8963641f91ac4cee3c8fad5947e033390a7bd8180fa4
      url: "https://pub.dev"
    source: hosted
    version: "1.4.1"
  shelf_web_socket:
    dependency: transitive
    description:
      name: shelf_web_socket
      sha256: "9ca081be41c60190ebcb4766b2486a7d50261db7bd0f5d9615f2d653637a84c1"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  slide_action:
    dependency: "direct main"
    description:
      name: slide_action
      sha256: "6eabc637d767eaa71905f01a24dbf037c120dd596157e52b7d5c8af82cedcf1c"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.2"
  sliver_tools:
    dependency: transitive
    description:
      name: sliver_tools
      sha256: eae28220badfb9d0559207badcbbc9ad5331aac829a88cb0964d330d2a4636a6
      url: "https://pub.dev"
    source: hosted
    version: "0.2.12"
  smart_auth:
    dependency: transitive
    description:
      name: smart_auth
      sha256: a25229b38c02f733d0a4e98d941b42bed91a976cb589e934895e60ccfa674cf6
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  sn_progress_dialog:
    dependency: "direct main"
    description:
      name: sn_progress_dialog
      sha256: b739d4b91c0bf575bfe1116b0a12ec2f3345d90c4ec3e7bc88b486daed6dd3e6
      url: "https://pub.dev"
    source: hosted
    version: "1.1.3"
  social_media_recorder:
    dependency: "direct main"
    description:
      name: social_media_recorder
      sha256: c92f4a8ab4aef77fb501391cd9dfad2e01f73e6e3f120469dc362186db36e485
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  source_gen:
    dependency: transitive
    description:
      name: source_gen
      sha256: "14658ba5f669685cd3d63701d01b31ea748310f7ab854e471962670abcf57832"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.0"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "254ee5351d6cb365c859e20ee823c3bb479bf4a293c22d17a9f1bf144ce86f7c"
      url: "https://pub.dev"
    source: hosted
    version: "1.10.1"
  sqflite:
    dependency: "direct main"
    description:
      name: sqflite
      sha256: a9016f495c927cb90557c909ff26a6d92d9bd54fc42ba92e19d4e79d61e798c6
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  sqflite_common:
    dependency: transitive
    description:
      name: sqflite_common
      sha256: "28d8c66baee4968519fb8bd6cdbedad982d6e53359091f0b74544a9f32ec72d5"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.3"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: "8b27215b45d22309b5cddda1aa2b19bdfec9df0e765f2de506401c071d38d1b1"
      url: "https://pub.dev"
    source: hosted
    version: "1.12.1"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: "969e04c80b8bcdf826f8f16579c7b14d780458bd97f56d107d3950fdbeef059d"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      sha256: "14a00e794c7c11aa145a170587321aedce29769c08d7f58b1d141da75e3b1c6f"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "921cd31725b72fe181906c6a94d987c78e3b98c2e205b397ea399d4054872b43"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.1"
  swipeable_button_view:
    dependency: "direct main"
    description:
      name: swipeable_button_view
      sha256: "6fbe3fe9084d849828fa49bf1f5d48a107f7797e3c7eff41aa61541c0b6d97f2"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.2"
  syncfusion_flutter_core:
    dependency: transitive
    description:
      name: syncfusion_flutter_core
      sha256: "6e67726b85812afc7105725a23620b876ab7f6b04b8410e211330ffb8c2cdbe8"
      url: "https://pub.dev"
    source: hosted
    version: "26.2.14"
  syncfusion_flutter_datepicker:
    dependency: "direct main"
    description:
      name: syncfusion_flutter_datepicker
      sha256: "9d0e1db2f49cef3fde28c5a42c7ef9b64fc9a49c52557eb6961cd41af3cf8adc"
      url: "https://pub.dev"
    source: hosted
    version: "26.2.14"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      sha256: "539ef412b170d65ecdafd780f924e5be3f60032a1128df156adad6c5b373d558"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0+1"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: "7f554798625ea768a7518313e58f83891c7f5024f88e46e7182a4558850a4b8e"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: fb31f383e2ee25fbbfe06b40fe21e1e458d14080e3c67e7ba0acfde4df4e0bbd
      url: "https://pub.dev"
    source: hosted
    version: "0.7.4"
  theme_mode_handler:
    dependency: "direct main"
    description:
      name: theme_mode_handler
      sha256: "06b08e58e66f80b89c4945a5883a85f7ceeb040056e8c559e1ccc0f2509309d5"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  time:
    dependency: transitive
    description:
      name: time
      sha256: ad8e018a6c9db36cb917a031853a1aae49467a93e0d464683e029537d848c221
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  time_change_detector:
    dependency: "direct main"
    description:
      name: time_change_detector
      sha256: "8f4b883a15f5ccd50c122ec18fcb87b8a9c3cf1e392055fbea808d6b64643ddb"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.3"
  timezone:
    dependency: transitive
    description:
      name: timezone
      sha256: "1cfd8ddc2d1cfd836bc93e67b9be88c3adaeca6f40a00ca999104c30693cdca0"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.2"
  timing:
    dependency: transitive
    description:
      name: timing
      sha256: "70a3b636575d4163c477e6de42f247a23b315ae20e86442bebe32d3cabf61c32"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  top_snackbar_flutter:
    dependency: "direct main"
    description:
      name: top_snackbar_flutter
      sha256: "22d14664a13db6ac714934c3382bd8d4daa57fb888a672f922df71981c5a5cb2"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  transparent_image:
    dependency: transitive
    description:
      name: transparent_image
      sha256: e8991d955a2094e197ca24c645efec2faf4285772a4746126ca12875e54ca02f
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  tuple:
    dependency: transitive
    description:
      name: tuple
      sha256: a97ce2013f240b2f3807bcbaf218765b6f301c3eff91092bcfa23a039e7dd151
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: facc8d6582f16042dd49f2463ff1bd6e2c9ef9f3d5da3d9b087e244a7b564b3c
      url: "https://pub.dev"
    source: hosted
    version: "1.3.2"
  typewritertext:
    dependency: "direct main"
    description:
      name: typewritertext
      sha256: c6b90cdc9f7fd7ae3235d9c926012000f7a1765cd49d67b78370dc61123212b2
      url: "https://pub.dev"
    source: hosted
    version: "3.0.9"
  universal_io:
    dependency: transitive
    description:
      name: universal_io
      sha256: "1722b2dcc462b4b2f3ee7d188dad008b6eb4c40bbd03a3de451d82c78bba9aad"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.2"
  universal_platform:
    dependency: transitive
    description:
      name: universal_platform
      sha256: d315be0f6641898b280ffa34e2ddb14f3d12b1a37882557869646e0cc363d0cc
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0+1"
  url_launcher:
    dependency: "direct main"
    description:
      name: url_launcher
      sha256: c512655380d241a337521703af62d2c122bf7b77a46ff7dd750092aa9433499c
      url: "https://pub.dev"
    source: hosted
    version: "6.2.4"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: d4ed0711849dd8e33eb2dd69c25db0d0d3fdc37e0a62e629fe32f57a22db2745
      url: "https://pub.dev"
    source: hosted
    version: "6.3.0"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      sha256: "75bb6fe3f60070407704282a2d295630cab232991eb52542b18347a8a941df03"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.4"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: ab360eb661f8879369acac07b6bb3ff09d9471155357da8443fd5d3cf7363811
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: b7244901ea3cf489c5335bdacda07264a6e960b1c1b1a9f91e4bc371d9e68234
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "552f8a1e663569be95a8190206a38187b531910283c3e982193e4f2733f01029"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: "7fd2f55fe86cea2897b963e864dc01a7eb0719ecc65fcef4c1cc3d686d718bb2"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: ecf9725510600aa2bb6d7ddabe16357691b6d2805f66216a97d1b881e21beff7
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: "648e103079f7c64a36dc7d39369cabb358d377078a051d6ae2ad3aa539519313"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.7"
  vector_graphics:
    dependency: transitive
    description:
      name: vector_graphics
      sha256: "4ac59808bbfca6da38c99f415ff2d3a5d7ca0a6b4809c71d9cf30fba5daf9752"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.10+1"
  vector_graphics_codec:
    dependency: transitive
    description:
      name: vector_graphics_codec
      sha256: f3247e7ab0ec77dc759263e68394990edc608fb2b480b80db8aa86ed09279e33
      url: "https://pub.dev"
    source: hosted
    version: "1.1.10+1"
  vector_graphics_compiler:
    dependency: transitive
    description:
      name: vector_graphics_compiler
      sha256: "18489bdd8850de3dd7ca8a34e0c446f719ec63e2bab2e7a8cc66a9028dd76c5a"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.10+1"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  vibration:
    dependency: "direct main"
    description:
      name: vibration
      sha256: "804ee8f9628f31ee71fbe6137a2bc6206a64e101ec22cd9dd6d3a7dc0272591b"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.3"
  vibration_platform_interface:
    dependency: transitive
    description:
      name: vibration_platform_interface
      sha256: "03e9deaa4df48a1a6212e281bfee5f610d62e9247929dd2f26f4efd4fa5e225c"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.0"
  video_compress:
    dependency: "direct main"
    description:
      name: video_compress
      sha256: "31bc5cdb9a02ba666456e5e1907393c28e6e0e972980d7d8d619a7beda0d4f20"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.4"
  video_editor:
    dependency: "direct main"
    description:
      name: video_editor
      sha256: "263be52e052118389f372f055f59c2fda5c7beecfdb706b899d2e05be8740c22"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  video_player:
    dependency: "direct main"
    description:
      name: video_player
      sha256: "7d78f0cfaddc8c19d4cb2d3bebe1bfef11f2103b0a03e5398b303a1bf65eeb14"
      url: "https://pub.dev"
    source: hosted
    version: "2.9.5"
  video_player_android:
    dependency: transitive
    description:
      name: video_player_android
      sha256: "4dd9b8b86d70d65eecf3dcabfcdfbb9c9115d244d022654aba49a00336d540c2"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.12"
  video_player_avfoundation:
    dependency: transitive
    description:
      name: video_player_avfoundation
      sha256: "8a4e73a3faf2b13512978a43cf1cdda66feeeb900a0527f1fbfd7b19cf3458d3"
      url: "https://pub.dev"
    source: hosted
    version: "2.6.7"
  video_player_platform_interface:
    dependency: transitive
    description:
      name: video_player_platform_interface
      sha256: "236454725fafcacf98f0f39af0d7c7ab2ce84762e3b63f2cbb3ef9a7e0550bc6"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.2"
  video_player_web:
    dependency: transitive
    description:
      name: video_player_web
      sha256: "34beb3a07d4331a24f7e7b2f75b8e2b103289038e07e65529699a671b6a6e2cb"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  video_thumbnail:
    dependency: transitive
    description:
      name: video_thumbnail
      sha256: "3455c189d3f0bb4e3fc2236475aa84fe598b9b2d0e08f43b9761f5bc44210016"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.3"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      sha256: ddfa8d30d89985b96407efce8acbdd124701f96741f2d981ca860662f1c0dc02
      url: "https://pub.dev"
    source: hosted
    version: "15.0.0"
  watcher:
    dependency: transitive
    description:
      name: watcher
      sha256: "3d2ad6751b3c16cf07c7fca317a1413b3f26530319181b37e3b9039b84fc01d8"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  web:
    dependency: transitive
    description:
      name: web
      sha256: "868d88a33d8a87b18ffc05f9f030ba328ffefba92d6c127917a2ba740f9cfe4a"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  web_socket_channel:
    dependency: transitive
    description:
      name: web_socket_channel
      sha256: d88238e5eac9a42bb43ca4e721edba3c08c6354d4a53063afaa568516217621b
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  win32:
    dependency: "direct main"
    description:
      name: win32
      sha256: daf97c9d80197ed7b619040e86c8ab9a9dad285e7671ee7390f9180cc828a51e
      url: "https://pub.dev"
    source: hosted
    version: "5.10.1"
  win32_registry:
    dependency: transitive
    description:
      name: win32_registry
      sha256: "41fd8a189940d8696b1b810efb9abcf60827b6cbfab90b0c43e8439e3a39d85a"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: faea9dee56b520b55a566385b84f2e8de55e7496104adada9962e0bd11bcff1d
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: "5bc72e1e45e941d825fd7468b9b4cc3b9327942649aeb6fc5cdbf135f0a86e84"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.0"
  xxh3:
    dependency: transitive
    description:
      name: xxh3
      sha256: a92b30944a9aeb4e3d4f3c3d4ddb3c7816ca73475cd603682c4f8149690f56d7
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  yaml:
    dependency: transitive
    description:
      name: yaml
      sha256: "75769501ea3489fca56601ff33454fe45507ea3bfb014161abc3b43ae25989d5"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  youtube_player_flutter:
    dependency: "direct main"
    description:
      name: youtube_player_flutter
      sha256: "72d487e1a1b9155a2dc9d448c137380791101a0ff623723195275ac275ac6942"
      url: "https://pub.dev"
    source: hosted
    version: "8.1.2"
  zoom_tap_animation:
    dependency: "direct main"
    description:
      name: zoom_tap_animation
      sha256: d9f7a73cab65aa1546ba6886b5e21d3c8ccccb34e4e5f770301c306d4868bee0
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
sdks:
  dart: ">=3.7.0-0 <4.0.0"
  flutter: ">=3.27.0"
