class PostLocation {
  PostLocation({
      this.docs,});

  PostLocation.fromJson(dynamic json) {
    if (json['docs'] != null) {
      docs = [];
      json['docs'].forEach((v) {
        docs?.add(Docs.fromJson(v));
      });
    }
  }
  List<Docs>? docs;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (docs != null) {
      map['docs'] = docs?.map((v) => v.toJson()).toList();
    }
    return map;
  }

}

class Docs {
  Docs({
      this.data, 
      this.date,});

  Docs.fromJson(dynamic json) {
    if (json['data'] != null) {
      data = [];
      json['data'].forEach((v) {
        data?.add(Data.fromJson(v));
      });
    }
    date = json['date'];
  }
  List<Data>? data;
  String? date;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (data != null) {
      map['data'] = data?.map((v) => v.toJson()).toList();
    }
    map['date'] = date;
    return map;
  }

}

class Data {
  Data({
      this.date, 
      this.lat,
      this.lng, 
      this.offside,});

  Data.fromJson(dynamic json) {
    date = json['date'];
    lat = json['lan'];
    lng = json['lng'];
    offside = json['offside'];
  }
  String? date;
  double? lat;
  double? lng;
  bool? offside;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['date'] = date;
    map['lan'] = lat;
    map['lng'] = lng;
    map['offside'] = offside;
    return map;
  }

}