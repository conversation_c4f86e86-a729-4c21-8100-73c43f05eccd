import 'dart:async';
import 'dart:io';
import 'dart:ui';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:flutter_background_service_android/flutter_background_service_android.dart';
import 'package:flutter_network_connectivity/flutter_network_connectivity.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:geolocator/geolocator.dart';
import 'package:govassist/back_service/model/location.dart';
import 'package:govassist/core/database/embedded_models.dart';
import 'package:govassist/core/database/isar_service.dart';
import 'package:govassist/core/location/location_service.dart';
import 'package:govassist/core/utils/api_path.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/features/auth/data/model/calendar.dart';
import 'package:govassist/features/auth/data/model/user_model.dart';
import 'package:intl/intl.dart';
import 'package:isar/isar.dart';
import 'package:point_in_polygon/point_in_polygon.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

import 'model/tracked_location.dart';

late IsarService isar;
late FlutterNetworkConnectivity networkConnectivity;
int interval = BACK_OUTSIDE_SERVICE_TIME;
Timer? timer;
late String start;

Future<void> initialiseService() async {
  print("initialiseService");
  final service = FlutterBackgroundService();

  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  const AndroidNotificationChannel channel = AndroidNotificationChannel(
    'my_foreground', // id
    'MY FOREGROUND SERVICE', // title
    playSound: false,
    description:
        'This channel is used for important notifications.', // description
    importance: Importance.low, // importance must be at low or higher level
  );

  if (Platform.isIOS || Platform.isAndroid) {
    await flutterLocalNotificationsPlugin.initialize(
      const InitializationSettings(
        iOS: DarwinInitializationSettings(),
        android: AndroidInitializationSettings('ic_bg_service_small'),
      ),
    );
  }

  await flutterLocalNotificationsPlugin
      .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin>()
      ?.createNotificationChannel(channel);

  await service.configure(
    androidConfiguration: AndroidConfiguration(
      onStart: onStart,
      autoStart: true,
      isForegroundMode: true,
      notificationChannelId: 'my_foreground',
      initialNotificationTitle: 'Hokim Yordamchisi',
      initialNotificationContent: 'Hokim Yordamchisi service 🛠️',
      foregroundServiceNotificationId: 888,
      autoStartOnBoot: true,
    ),
    iosConfiguration: IosConfiguration(
      // auto start service
      autoStart: true,

      // this will be executed when app is in foreground in separated isolate
      onForeground: onStart,

      // you have to enable background fetch capability on xcode project
      onBackground: onIosBackground,
    ),
  );

  flutterLocalNotificationsPlugin.show(
    888,
    'Hokim Yordamchisi',
    '⚙ ️Xizmat ishlamoqda..',
    NotificationDetails(
      android: AndroidNotificationDetails(
        'my_foreground',
        'MY FOREGROUND SERVICE',
        icon: 'ic_bg_service_small',
        playSound: false,
        ongoing: true,
        color: cFirstColor.withAlpha(50),
        colorized: true,
      ),
    ),
  );
}

@pragma("vm:entry-point")
Future<bool> onIosBackground(ServiceInstance serviceInstance) async {
  WidgetsFlutterBinding.ensureInitialized();
  DartPluginRegistrant.ensureInitialized();
  return false;
}

@pragma("vm:entry-point")
void onStart(ServiceInstance service) async {
  Calendar? calendar;
  UserModel? userModel;
  isar = await IsarService.buildIsarService();
  calendar = await isar.isar.calendars.where().findFirst();
  userModel = await isar.isar.userModels.where().findFirst();
  networkConnectivity = FlutterNetworkConnectivity(
    isContinousLookUp: true,
    lookUpDuration: const Duration(seconds: 5),
    lookUpUrl: 'google.com',
  );

  DartPluginRegistrant.ensureInitialized();
  if (service is AndroidServiceInstance) {
    service.on("setAsForeground").listen((event) {
      service.setAsForegroundService();
    });

    service.on("setAsBackground").listen((event) {
      service.setAsBackgroundService();
    });
  }
  service.on("stopService").listen((event) {
    service.stopSelf();
  });
  startTimer(calendar, userModel, service);
}

//TODO TESTING
startTimer(Calendar? calendar, UserModel? userModel, ServiceInstance service) {
  timer = Timer.periodic(Duration(seconds: interval), (timer) async {
    print("Service running...");
    print(await FlutterTimezone.getLocalTimezone());
    DateTime now = DateTime.now();
    DateFormat dateFormat1 = DateFormat("EEEE");
    DateFormat dateFormat2 = DateFormat("yyyy-MM-dd");
    start = calendar?.start ?? "09:00";
    String end = calendar?.end ?? "18:00";
    List holidays = calendar?.holidays ?? [];
    List<int> workDays = calendar?.days ??
        [
          1,
          2,
          3,
          4,
          5,
          6,
        ];
    List<Point> points = [];
    InActive? inActive;
    userModel?.district?.coordinates?.forEach((element) {
      points.add(Point(
          x: element.latitude ?? 0.00000, y: element.longitude ?? 0.00000));
    });
    inActive = userModel?.inActive;
    DateTime startDateTime = _convertStringToDateTime(start);
    DateTime endDateTime = _convertStringToDateTime(end);
    DateTime today = DateTime(
      now.year,
      now.month,
      now.day,
    );

    final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
        FlutterLocalNotificationsPlugin();

    if (service is AndroidServiceInstance) {
      if (await service.isForegroundService()) {
        flutterLocalNotificationsPlugin.show(
          888,
          'Hokim Yordamchisi',
          '⚙ ️Xizmat ishlamoqda...',
          NotificationDetails(
            android: AndroidNotificationDetails(
              'my_foreground',
              'MY FOREGROUND SERVICE',
              icon: 'ic_bg_service_small',
              playSound: false,
              ongoing: true,
              color: cFirstColor.withAlpha(50),
              colorized: true,
            ),
          ),
        );
      }
    }
    if (userModel != null) {
      if (((now.isBefore(endDateTime) && now.isAfter(startDateTime)) &&
          workDays
              .contains(_dayOfWeekStringToNumber(dateFormat1.format(now))) &&
          !holidays.contains(dateFormat2.format(now)))) {
        if (inActive != null) {
          if (now.isBefore(DateTime.parse(inActive.start ?? "")) ||
              now.isAfter(DateTime.parse(inActive.end ?? ""))) {
            trackAndSave(
                points: points,
                now: now,
                calendar: calendar,
                userModel: userModel,
                service: service,
                startDateTime: startDateTime);
          }
        } else {
          trackAndSave(
              points: points,
              now: now,
              calendar: calendar,
              userModel: userModel,
              service: service,
              startDateTime: startDateTime);
        }
      }
    }
    send(endDateTime);
  });
}

CustomReturnObject getFilteredLocation(
    List<TrackedLocation?> list, DateTime endDateTime) {
  DateTime now = DateTime.now();
  DateFormat dateFormat = DateFormat("yyyy-MM-dd");
  List<int> ids = [];
  Map<String, List<Map<String, dynamic>>> filteredData = {};
  for (var obj in list) {
    if ((obj?.status == true || obj!.locations.length < 35) &&
        (dateFormat.format(now) !=
                dateFormat.format(DateTime.parse(obj?.date ?? "")) ||
            now.isAfter(endDateTime))) {
      ids.add(obj?.localId ?? -1);
      if (!filteredData.containsKey(obj?.date)) {
        filteredData[obj!.date] = [];
      }
      for (int i = 0; i < obj!.locations.length; i++) {
        filteredData[obj.date]!.add({
          "date": obj.locations[i].dateTime,
          "lat": obj.locations[i].lat,
          "lng": obj.locations[i].lng,
          "offside": obj.locations[i].offside,
          "locChange": obj.locations[i].locChange,
          "timeChange": obj.locations[i].timeChange,
          "deviceOff": obj.locations[i].deviceOff
        });
      }
    }
  }
  List<Map<String, dynamic>> result = [];

  filteredData.forEach((date, data) {
    result.add({"data": data, "date": date});
  });

  return CustomReturnObject(datas: result, ids: ids ?? []);
}

void updateTimerInterval(Calendar? calendar, UserModel? userModel,
    ServiceInstance service, bool offside) {
  timer?.cancel(); // Cancel the existing Timer
  interval = offside == true ? BACK_OUTSIDE_SERVICE_TIME : BACK_SERVICE_TIME;
  startTimer(calendar, userModel,
      service); // Reschedule the Timer with the new interval
}

trackAndSave(
    {required List<Point> points,
    required DateTime now,
    Calendar? calendar,
    UserModel? userModel,
    required ServiceInstance service,
    required DateTime startDateTime}) async {
  // print("TrackAndSave");
  SharedPreferences prefs = await SharedPreferences.getInstance();
  bool locChange = false;
  bool deviceOff = false;
  prefs.reload().then((value) async {
    try {
      var location;
      try {
        location = await determinePosition(true);
        locChange = false;
      } catch (e, s) {
        if (e.toString() == "MOCKED") {
          locChange = true;
          var lat = s.toString().split(",").first;
          var lng = s.toString().split(",").last;
          location = Position(
            latitude: double.parse(lat),
            longitude: double.parse(lng),
            timestamp: DateTime.now(),
            accuracy: 0,
            altitude: 0,
            altitudeAccuracy: 0,
            heading: 0,
            headingAccuracy: 0,
            speed: 0,
            speedAccuracy: 0,
          );
        } else {
          print(e);
        }
      }

      bool isTimeCorrect = prefs.getBool(is_time_correct) ?? false;
      DateFormat dateFormat = DateFormat("yyyy-MM-dd");
      final DateFormat hourFormat = DateFormat('yyyy-MM-dd HH:mm');
      Location templocation;
      print(Poly(vertices: points).isPointInPolygon(
          Point(x: location.latitude, y: location.longitude)));

      if (!Poly(vertices: points).isPointInPolygon(
        Point(x: location.latitude, y: location.longitude),
      )) {
        templocation = Location(
            lat: location.latitude,
            lng: location.longitude,
            dateTime: hourFormat.format(now),
            date: dateFormat.format(now),
            offside: true,
            timeChange: !isTimeCorrect,
            locChange: locChange);
        updateTimerInterval(calendar, userModel, service, true);
      } else {
        templocation = Location(
            lat: location.latitude,
            lng: location.longitude,
            dateTime: hourFormat.format(now),
            date: dateFormat.format(now),
            offside: false,
            timeChange: !isTimeCorrect,
            locChange: locChange);
        updateTimerInterval(calendar, userModel, service, false);
      }
      try {
        TrackedLocation? trackedLocation = await isar.isar.trackedLocations
            .filter()
            .dateEqualTo(dateFormat.format(DateTime.now()))
            .findFirst();

        if (trackedLocation?.locations.isEmpty == null ||
            trackedLocation?.locations.isEmpty == true) {
          TrackedLocation trackedLocation = TrackedLocation(
              locations: [], date: dateFormat.format(DateTime.now()));
          // print("trackedLocation?.locations.isEmpty=true");
          DateTime now = DateTime.now();
          int differenceInMinutes =
              now.difference(startDateTime).inMinutes.abs();
          // print("StartDate:${startDateTime}");
          // print("Difference:${differenceInMinutes}");
          if (differenceInMinutes > 16) {
            deviceOff = true;
            trackedLocation.locations
                .add(templocation.copyWith(deviceOff: deviceOff));

            if (templocation.locChange == true ||
                templocation.offside == true ||
                templocation.timeChange == true ||
                templocation.locChange == true ||
                deviceOff == true) {
              trackedLocation.status = true;
            }
            trackedLocation.date = dateFormat.format(now);
            await isar.isar.writeTxn(() async {
              isar.isar.trackedLocations.put(trackedLocation);
            });
          } else {
            deviceOff = false;
            trackedLocation.locations
                .add(templocation.copyWith(deviceOff: deviceOff));
            trackedLocation.date = dateFormat.format(now);
            await isar.isar.writeTxn(() async {
              isar.isar.trackedLocations.put(trackedLocation);
            });
          }
        } else {
          // print("trackedLocation?.locations.isEmpty=false");
          Location? location = trackedLocation?.locations.last;
          int differenceInMinutes = now
              .difference(DateTime.parse(
                  location?.dateTime ?? startDateTime.toString()))
              .inMinutes
              .abs();
          // print("StartDate:${startDateTime.toString()}");
          // print("hello${location?.dateTime}");
          // print("he:${differenceInMinutes}");

          if (differenceInMinutes > 16) {
            deviceOff = true;
            List<Location> changedLocation =
                List<Location>.from(trackedLocation?.locations ?? []);
            changedLocation.add(templocation.copyWith(deviceOff: deviceOff));
            trackedLocation?.locations = changedLocation ?? [];
            if (templocation.locChange == true ||
                templocation.offside == true ||
                templocation.timeChange == true ||
                templocation.locChange == true ||
                deviceOff == true) {
              trackedLocation?.status = true;
            }
            trackedLocation?.date = dateFormat.format(now);
            await isar.isar.writeTxn(() async {
              isar.isar.trackedLocations.put(trackedLocation!);
            });
          } else {
            deviceOff = false;
            try {
              List<Location> changedLocation =
                  List<Location>.from(trackedLocation?.locations ?? []);
              changedLocation.add(templocation.copyWith(deviceOff: deviceOff));
              trackedLocation?.locations = changedLocation ?? [];
              trackedLocation?.date = dateFormat.format(now);
              await isar.isar.writeTxn(() async {
                isar.isar.trackedLocations.put(trackedLocation!);
              });
            } catch (e) {
              print(e);
            }
          }
        }
      } catch (e) {
        print(e);
      }
    } catch (e) {
      print(e);
    }
  });
}

int _dayOfWeekStringToNumber(String dayOfWeek) {
  switch (dayOfWeek) {
    case 'Monday':
      return 1;
    case 'Tuesday':
      return 2;
    case 'Wednesday':
      return 3;
    case 'Thursday':
      return 4;
    case 'Friday':
      return 5;
    case 'Saturday':
      return 6;
    case 'Sunday':
      return 7;
    default:
      return 0; // Invalid day
  }
}

DateTime _convertStringToDateTime(String timeString) {
  DateTime currentDate = DateTime.now();
  String combinedDateTimeString =
      "${currentDate.year}-${currentDate.month.toString().padLeft(2, '0')}-${currentDate.day.toString().padLeft(2, '0')} $timeString";
  DateTime dateTime = DateTime.parse(combinedDateTimeString);
  return dateTime;
}

deleteLocationData(List<int> ids) {
  isar.isar.writeTxn(() async {
    isar.isar.trackedLocations.deleteAll(ids);
  });
}

class CustomReturnObject {
  List<Map<String, dynamic>> datas;
  List<int> ids;

  CustomReturnObject({required this.datas, required this.ids});
}

send(DateTime endDateTime) async {
  // print("sending...");
  List<TrackedLocation?> trackedLocations =
      await isar.isar.trackedLocations.where().findAllSync();
  CustomReturnObject customReturnObject =
      getFilteredLocation(trackedLocations, endDateTime);
  Dio dio = Dio();
  dio.httpClientAdapter = IOHttpClientAdapter(
    createHttpClient: () {
      final client = HttpClient();
      client.badCertificateCallback = (X509Certificate cert, String host, int port) => true;
      return client;
    },
  );
  SharedPreferences prefs = await SharedPreferences.getInstance();
  String baseApiUrl = prefs.getString(baseUrlPref) ?? prodUrl;

  if (customReturnObject.datas.isNotEmpty) {
    if (networkConnectivity.isInternetAvailable) {
      try {
        var response = await dio.post(baseApiUrl + areaPath,
            options: Options(headers: {
              "Authorization": "Bearer ${prefs.getString("token")}"
            }),
            data: {"docs": customReturnObject.datas});

        if (response.statusCode == 200) {
          print('isNotEmpty');
          deleteLocationData(customReturnObject.ids);
        }
      }
      on DioException catch(e){
        print("Error:${e}");
      }
      catch (e) {
        print("ERROR: $e");
      }
    }
  }
}
