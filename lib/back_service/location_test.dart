//
// import 'package:easy_localization/easy_localization.dart';
// import 'package:govassist/back_service/model/tracked_location.dart';
//
// void main() {
//   List<TrackedLocation> list = [
//     TrackedLocation(
//         lat: 40.71,
//         lng: 70.34,
//         dateTime: '2024-02-03 09:00',
//         date: '2024-02-03',
//         offside: false,
//         timeChange: false,
//         locChange: false),
//     TrackedLocation(
//         lat: 40.71,
//         lng: 70.34,
//         dateTime: '2024-02-03 09:18',
//         date: '2024-02-03',
//         offside: false,
//         timeChange: false,
//         locChange: false),
//     TrackedLocation(
//         lat: 40.71,
//         lng: 70.34,
//         dateTime: '2024-02-03 11:30',
//         date: '2024-02-03',
//         offside: false,
//         timeChange: false,
//         locChange: false),
//     TrackedLocation(
//         lat: 40.71,
//         lng: 70.34,
//         dateTime: '2024-02-03 11:55',
//         date: '2024-02-03',
//         offside: false,
//         timeChange: false,
//         locChange: false),
//     TrackedLocation(
//         lat: 40.71,
//         lng: 70.34,
//         dateTime: '2024-02-03 13:10',
//         date: '2024-02-03',
//         offside: true,
//         timeChange: false,
//         locChange: false),
//   ];
//   // print(getFilteredLocation(list));
//   List<Map<String, dynamic>> filteredList = getFilteredLocation(list);
//   List<Map<String, dynamic>> sendList = [];
//   DateFormat dateFormat2 = DateFormat("yyyy-MM-dd");
//
//   // for (int j = 0; j < filteredList.length; j++) {
//   //   for (int i = 1; i < filteredList[j]['data'].length; i++) {
//   //     // print(i);
//   //     DateTime currentDateTime =
//   //         DateTime.parse(filteredList[j]['data'][i]['date']);
//   //     DateTime previousDateTime =
//   //         DateTime.parse(filteredList[j]['data'][i - 1]['date']);
//   //     int differenceInMinutes =
//   //         currentDateTime.difference(previousDateTime).inMinutes;
//   //     print(differenceInMinutes);
//   //
//   //     DateTime lunchEnd = DateTime.parse(
//   //         "${dateFormat2.format(DateTime.parse(filteredList[j]['data'][i]['date']))} 13:00");
//   //     DateTime lunchStart = DateTime.parse(
//   //         "${dateFormat2.format(DateTime.parse(filteredList[j]['data'][i]['date']))} 12:00");
//   //
//   //     //print(lunchEnd.difference(lunchStart).inMinutes);
//   //     // print(lunchStart.difference(lunchEnd).inMinutes);
//   //
//   //     // print(currentDateTime.isAfter(lunchEnd) &&
//   //     //     currentDateTime.difference(lunchEnd).inMinutes <= 20);
//   //     // print("sasas:${currentDateTime.isBefore(lunchStart)}");
//   //     //
//   //     // print((currentDateTime.isBefore(lunchStart) ||
//   //     //         currentDateTime.minute == lunchStart.minute) &&
//   //     //     currentDateTime.difference(lunchStart).inMinutes > 20);
//   //
//   //     if ((currentDateTime.isAfter(lunchEnd) ||
//   //             currentDateTime.compareTo(lunchEnd) == 0) &&
//   //         currentDateTime.difference(lunchEnd).inMinutes <= 20) {
//   //       print("a:${currentDateTime}");
//   //       filteredList[j]['data'][i]['deviceOff'] = false;
//   //     }
//   //     // else if ((currentDateTime.isBefore(lunchStart) ||
//   //     //         currentDateTime.compareTo(lunchEnd) == 0) &&
//   //     //     currentDateTime.difference(lunchStart).inMinutes >=-20 &&
//   //     //     currentDateTime.difference(lunchStart).inMinutes <= 0) {
//   //     //   print("b:${currentDateTime}");
//   //     //   print("b:${(currentDateTime.isBefore(lunchStart))}");
//   //     //   print("b:${currentDateTime.compareTo(lunchEnd) == 0}");
//   //     //   print("b:${currentDateTime.difference(lunchStart).inMinutes}");
//   //     //   filteredList[j]['data'][i]['deviceOff'] = false;
//   //     // }
//   //     else {
//   //       if (differenceInMinutes > 20) {
//   //         filteredList[j]['data'][i]['deviceOff'] = true;
//   //         DateTime previousDate =
//   //             DateTime.parse(filteredList[j]['data'][i - 1]['date']);
//   //         if (currentDateTime.isAfter(lunchEnd)) {
//   //           if (previousDate.isAfter(lunchEnd) ||
//   //               previousDate.difference(lunchEnd) == 0) {
//   //             filteredList[j]['data'][i - 1]['deviceOff'] = true;
//   //           }
//   //         } else {
//   //           filteredList[j]['data'][i - 1]['deviceOff'] = true;
//   //         }
//   //       } else {
//   //         print("d:${currentDateTime}");
//   //         filteredList[j]['data'][i]['deviceOff'] = false;
//   //       }
//   //     }
//   //   }
//   // }
//   // // String jsonString = json.encode(filteredList);
//   // // print(jsonString);
//   // for (int j = 0; j < filteredList.length; j++) {
//   //   bool needRemove = true;
//   //   for (int i = 0; i < filteredList[j]['data'].length; i++) {
//   //     if (filteredList[j]['data'][i]['offside'] == true ||
//   //         filteredList[j]['data'][i]['deviceOff'] == true ||
//   //         filteredList[j]['data'][i]['timeChange'] == true ||
//   //         filteredList[j]['data'][i]['locChange'] == true) {
//   //       needRemove = false;
//   //       break;
//   //     }
//   //   }
//   //   // print(needRemove);
//   //   if (needRemove == false) {
//   //     sendList.add(filteredList[j]);
//   //   }
//   //   //print(needRemove);
//   // }
//   // print("\n");
//   // // String jsonString1 = json.encode(sendList);
//   // // print(jsonString1);
//   // //print(sendList[0]['data'].length.toString());
//   // print(sendList);
// }
//
// List<Map<String, dynamic>> getFilteredLocation(List<TrackedLocation?> list) {
//   Map<String, List<Map<String, dynamic>>> filteredData = {};
//   for (var obj in list) {
//     if (!filteredData.containsKey(obj?.date)) {
//       filteredData[obj!.date] = [];
//     }
//
//     filteredData[obj?.date]!.add({
//       "date": obj?.dateTime,
//       "lat": obj?.lat,
//       "lng": obj?.lng,
//       "offside": obj?.offside,
//       "locChange": obj?.locChange,
//       "timeChange": obj?.timeChange,
//       "deviceOff": false
//     });
//   }
//   List<Map<String, dynamic>> result = [];
//
//   filteredData.forEach((date, data) {
//     result.add({"data": data, "date": date});
//   });
//   // print("uzunligi: ${result[0]['data'].length}");
//   return result;
//  }
//
//   ///
//   ///09:00 10:00->10:15->11:56->11:59->12:00-13:00)..->13:10
//   ///true true->true -   true                false
