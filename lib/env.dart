import 'package:govassist/core/utils/api_path.dart';

import 'core/utils/app_constants.dart';

enum Environment { dev, prod }

abstract class AppEnvironment {
  static late Environment _environment;
  static late String baseApiUrl;

  static Environment get environment => _environment;

  static setupEnv(Environment env) {
    _environment = env;
    switch (env) {
      case Environment.dev:
        {
          baseApiUrl = testUrl;
          EMULATOR = true;
          break;
        }
      case Environment.prod:
        {
          baseApiUrl = prodUrl;
          EMULATOR = false;
          break;
        }
    }
  }
}
