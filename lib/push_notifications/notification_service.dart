// flutter local notification

import 'dart:convert';

import 'package:context_holder/context_holder.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:govassist/core/network/network_info.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/core/widgets/custom_toast.dart';
import 'package:govassist/features/tasks/presentation/pages/task_main_page.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;

import 'awesome_notification_controller.dart';

const AndroidNotificationChannel channel = AndroidNotificationChannel(
    'high_importance_channel', // id
    'High Importance Notifications', // title
    description: 'This channel is used for important notifications.',
    // description
    importance: Importance.max,
    playSound: true,
    enableVibration: true);

final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();

///Listen message from FCM (Background message)
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();

  Map<String, dynamic> data = message.data;

  print('A Background message just showed up :  ${message.messageId}');
  print(data);

  showNotification(data);
}

///Method for creating a notification channel (+Foreground message listener)
Future<String> createChannel(NetworkInfo networkInfo) async {
  await Firebase.initializeApp();
  SharedPreferences prefs = await SharedPreferences.getInstance();

  String? token = "";

  ///iOS section for notification (Asking permission)
  FirebaseMessaging messaging = FirebaseMessaging.instance;

  NotificationSettings settings = await messaging.requestPermission(
    alert: true,
    announcement: true,
    badge: true,
    carPlay: false,
    criticalAlert: true,
    provisional: false,
    sound: true,
  );

  print('User granted permission: ${settings.authorizationStatus}');

  messaging.setForegroundNotificationPresentationOptions(
    alert: true,
    badge: true,
    sound: true,
  );

  ///-------------------ends iOS specific section

  ///----------------- Sets TOKEN (Important)
  try {
    if (await networkInfo.isConnected) {
      token = await FirebaseMessaging.instance.getToken();
    }
    if ((token != null && token.isEmpty) || token == null) {
      token = prefs.getString(firebaseTokenKEY);
    }
  } catch (e) {
    print(e.toString());
  }
  ///-------------------------------------------

  ///Configure time zone
  _configureLocalTimeZone();

  /// Create channel for Android
  await flutterLocalNotificationsPlugin
      .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin>()
      ?.createNotificationChannel(channel);

  ///Initializing local plugin settings for both platforms
  await flutterLocalNotificationsPlugin.initialize(
    InitializationSettings(
        android: AndroidInitializationSettings(
          "@mipmap/ic_launcher",
        ),
        iOS: DarwinInitializationSettings(
          defaultPresentSound: true,
        )),
    onDidReceiveNotificationResponse:
        (NotificationResponse notificationResponse) async {
      print('justTappedThis');

      Map<String, dynamic> data =
          jsonDecode(notificationResponse.payload ?? "{}");
      String id = data['id'];
      String type = data['type'];

      ///Do something
      print("Foreground action: ${notificationResponse.actionId}");
    },
  );

  /// Handle background message
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  ///Notification tap from Firebase automatic notification which triggers 'notification' key
  FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
    print('A new message open app event was published');
    RemoteNotification? notification = message.notification;
    AndroidNotification? android = message.notification?.android;

    if (notification != null && android != null) {
      ///
      print("Tapped firebase controlled message:"
          "\n=> To show this 'notification' key must be written to the payload");
    }
  });

  ///When token is changed, callback triggers and function saves new token to the prefs
  FirebaseMessaging.instance.onTokenRefresh.listen((token) {
    print('Token refreshed: $token');
    // Do something with the refreshed token
    if (token.isNotEmpty) prefs.setString(firebaseTokenKEY, token);
  });

  ///Listen message from FCM (Foreground message)
  FirebaseMessaging.onMessage.listen((RemoteMessage message) async {
    Map<String, dynamic> data = message.data;

    print('A Foreground message just showed up :  ${message.messageId}');
    print(data);

    showNotification(data);
  });

  return Future<String>.value(token);
}

Future<void> _configureLocalTimeZone() async {
  tz.initializeTimeZones();
  final String timeZone = await FlutterTimezone.getLocalTimezone();
  tz.setLocalLocation(tz.getLocation(timeZone));
}

///Method for showing the local notification
showNotification(data) async {
  if (data != null) {
    ///Decide to use which one

    // SharedPreferences prefs = await SharedPreferences.getInstance();
    // String lang = prefs.getString(language_pref) ?? 'uz';

    // flutterLocalNotificationsPlugin.show(
    //   data.hashCode,
    //   titleMaker(data['id'].toString(), data['type']),
    //   data['title'],
    //   NotificationDetails(
    //       android: AndroidNotificationDetails(
    //         channel.id,
    //         channel.name,
    //         channelDescription: channel.description,
    //         color: Colors.blue,
    //         icon: "@mipmap/ic_launcher",
    //         enableVibration: true,
    //         actions: <AndroidNotificationAction>[
    //           AndroidNotificationAction('id_1', lang=="uz"?"Qabul qilish":"Принятие",
    //               showsUserInterface: true),
    //           AndroidNotificationAction('id_2', lang=="uz"?"Ochish":"Открыть",
    //               showsUserInterface: true),
    //           // AndroidNotificationAction('id_3', 'Action 3',
    //           //     showsUserInterface: true),
    //         ],
    //       ),
    //       iOS: DarwinNotificationDetails()),
    //   payload: "{\"id\": \"${data['id']}\", \"type\": \"${data['type']}\"}",);

    NotificationController.createNewNotification(data);
  } else {
    CustomToast.showToast('=== Notification display error! ===');
  }
}

/// Action function when notification clicked

open() async {
  ///Check for the null even IDE says it's not
  if (ContextHolder.currentContext != null) {
    Navigator.push(ContextHolder.currentContext,
        MaterialPageRoute(builder: (context) => TaskMainPage()));
  } else {
    print('Context is null :(');
  }
}

getActiveNotifications() async {
  var a = await flutterLocalNotificationsPlugin.getActiveNotifications();
  for (var element in a) {
    print("Active: ${element.id}");
  }
}

extension StringExtension on String {
  String addExtraSpacesBefore(int count) {
    String spaces = ' ' * count;
    return spaces + '№' + this;
  }
}
