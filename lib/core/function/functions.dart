import 'dart:convert';
import 'dart:io';

import 'package:aescryptojs/aescryptojs.dart';
import 'package:auto_start_flutter/auto_start_flutter.dart';
import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:flutter_phoenix/flutter_phoenix.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart' hide Trans;
import 'package:get_storage/get_storage.dart';
import 'package:govassist/env.dart';
import 'package:govassist/features/bandlik/data/model/employee.dart';
import 'package:govassist/features/bandlik/data/model/employment_type.dart';
import 'package:govassist/features/tasks/data/model/special_new_task.dart';
import 'package:isar/isar.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:platform_device_id_plus/platform_device_id.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite/sqflite.dart';
import 'package:govassist/core/database/embedded_models.dart';
import 'package:govassist/core/location/location_service.dart';
import 'package:govassist/core/network/network_info.dart';
import 'package:govassist/core/utils/api_path.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/core/utils/get_android_name.dart';
import 'package:govassist/core/widgets/dialog_frame.dart';
import 'package:govassist/di/dependency_injection.dart';
import 'package:govassist/features/auth/data/model/user_model.dart';
import 'package:govassist/features/lock/definitions.dart';
import 'dart:async';
import 'package:path/path.dart' hide Context;
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:govassist/core/widgets/custom_toast.dart';
import 'package:govassist/features/payments/payment_res.dart';
import 'package:govassist/features/sent_page/data/model/sent_model.dart';
import 'package:govassist/features/task_send/data/model/task_img_model.dart';
import 'package:govassist/generated/assets.dart';
import 'package:govassist/translations/locale_keys.g.dart';

Future<Map<Permission, PermissionStatus>> requestPermissionsForFile() async {
  AndroidDeviceInfo androidInfo = di();
  if (androidInfo.version.sdkInt <= 33) {
    Map<Permission, PermissionStatus> statuses = await [
      Permission.storage,
    ].request();
    return statuses;
  } else {
    return {};
  }
}

void setLanguage(String lang) async {
  SharedPreferences prefs = di();
  await prefs.setString(language_pref, lang);
}

bool lazyTasks(BuildContext context) {
  ///Set user settings and reset languages
  context.setLocale(Locale('uz'));
  setLanguage('uz');
  if (Platform.isAndroid || Platform.isIOS) {
    FlutterBackgroundService().invoke("stopService");
  }

  Phoenix.rebirth(context);
  return false;
}

Future<void> postInfo(
    {required SharedPreferences prefs, required Dio client}) async {
  try {
    DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
    var iOSVer;
    var androidVer;
    var deviceName;
    String userID = prefs.getString('id') ?? '';

    if (Platform.isIOS) {
      final iOSInfo = await deviceInfoPlugin.iosInfo;
      iOSVer = iOSInfo.systemVersion;
    } else if (Platform.isAndroid) {
      final androidInfo = await deviceInfoPlugin.androidInfo;
      androidVer = androidInfo.version.sdkInt;
      deviceName = "${androidInfo.brand.toUpperCase()} ${androidInfo.model}";
      print("Device info: ${deviceName}");
    }

    bool isSameVer =
        (prefs.getString('ver') ?? '0') == APP_VERSION ? true : false;

    var data = {
      "info": {
        "platform": Platform.isAndroid ? 'Android' : 'iOS',
        "version": Platform.isAndroid ? getAndroidName(androidVer) : iOSVer,
        "appVersion": APP_VERSION,
        "deviceName": deviceName
      }
    };

    if (!isSameVer) {
      final responseInfo = await client.put(infoPostPath + userID, data: data);

      if (responseInfo.statusCode == 200) {
        print('Info posted: ${data}');
        prefs.setString('ver', APP_VERSION);
      } else {
        print('Info post failed: ${responseInfo.data}');
      }
    }
  } on DioException catch (e) {
    if (e.type == DioExceptionType.badResponse) {
      if (e.response != null) {
        print('*** Failed to post Device info! ***');
        print('*** Reason: ' + e.response.toString());
      }
      return;
    }
    if (e.type == DioExceptionType.connectionTimeout) {
      print('check your connection');
    }

    if (e.type == DioExceptionType.receiveTimeout) {
      print('unable to connect to the server');
    }

    if (e.type == DioExceptionType.unknown) {
      print('Something went wrong');
    }
  } catch (e) {
    print('*** Failed to post DeviceInfo with error ${e}');
  }
}

Future<void> postMac(
    {required SharedPreferences prefs,
    required Dio client,
    required String token,
    required String userId}) async {
  var body = {'user': userId, 'macAddress': await getDeviceId() ?? 'error'};

  try {
    var response = await client.post(macPostPath, data: body);

    print(response);

    if (response.statusCode == 200) {
      print('*** Logout MAC is posted! ***');
    } else {
      print('*** Failed to post MacAddress! ***');
      print('*** Reason: ' + response.data);
      CustomToast.showToast('Failed to post MacAddress!');
    }
  } on DioException catch (e) {
    if (e.type == DioExceptionType.badResponse) {
      if (e.response != null) {
        print('*** Failed to post MacAddress! ***');
        print('*** Reason: ' + e.response.toString());
        CustomToast.showToast('Failed to post MacAddress!');
      }
      return;
    }
    if (e.type == DioExceptionType.connectionTimeout) {
      CustomToast.showToast("Connection timeout");
      print('check your connection');
    }

    if (e.type == DioExceptionType.receiveTimeout) {
      CustomToast.showToast("Receive timeout");
      print('unable to connect to the server');
    }

    if (e.type == DioExceptionType.unknown) {
      CustomToast.showToast("Something went wrong");
      print('Something went wrong');
    }
  } catch (e) {
    CustomToast.showToast("*** Failed to post MacAddress with error ${e}");
  }
}

Future<void> deleteDatabase(String path) =>
    databaseFactory.deleteDatabase(path);

clearAndLogout(BuildContext context) async {
  SharedPreferences prefs = di();
  GetStorage gs = di();

  try {
    ///Clear and get token again
    // final token = await prefs.getString('token') ?? '';
    // final userId = await prefs.getString('id') ?? '';
    // await postMac(prefs: di(), client: di(), token: token, userId: userId);

    ///Clear all dbs
    var isar = await Isar.getInstance();
    if (isar != null) {
      isar.writeTxnSync(() => isar.clearSync());
    }

    String databasesPath = await getDatabasesPath();
    String path = join(databasesPath, 'person.db');

    await deleteDatabase(path); // just for testing
    await prefs.clear();
    await gs.erase();
    lazyTasks(context);
  } catch (e) {
    print(e);
  }
}

String fullName(UserModel? userModel) {
  if (userModel?.firstName == null && userModel?.lastName == null) {
    return "Loading...";
  } else {
    return "${userModel?.firstName} ${userModel?.lastName}";
  }
}

String fullAddress(UserModel? userModel) {
  String address = "";
  if (userModel?.province != null) {
    address = address + (userModel?.province?.titleUZ ?? "");
  }
  if (userModel?.region != null) {
    address = address + " " + (userModel?.region?.titleUZ ?? "");
  }
  if (userModel?.district != null) {
    address = address + " " + (userModel?.district?.titleUZ ?? "");
  }
  if (address.isEmpty) {
    address = "Loading...";
  }
  return address;
}

String getProvince({Province? province}) {
  final SharedPreferences prefs = di();
  String lan = prefs.getString(language_pref) ?? "uz";

  switch (lan) {
    case 'uz':
      {
        return province?.titleUZ ?? ':(';
      }
    case 'kk':
      {
        return province?.titleQQ ?? ':(';
      }
    case 'ru':
      {
        return province?.titleRU ?? ':(';
      }
    default:
      {
        return province?.titleUZ ?? ':(';
      }
  }
}

String getRegion({Region? region}) {
  final SharedPreferences prefs = di();
  String lan = prefs.getString(language_pref) ?? "uz";

  switch (lan) {
    case 'uz':
      {
        return region?.titleUZ ?? ':(';
      }
    case 'kk':
      {
        return region?.titleQQ ?? ':(';
      }
    case 'ru':
      {
        return region?.titleRU ?? ':(';
      }
    default:
      {
        return region?.titleUZ ?? ':(';
      }
  }
}

launchCaller() async {
  const url = TEL;

  try {
    var uri = Uri(scheme: 'tel', path: url);

    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      throw 'Could not launch $url';
    }
  } catch (e) {
    print(e);
  }
}

launchCustomUrl(String url) async {
  var uri = Uri.parse(url);

  if (await canLaunchUrl(uri)) {
    await launchUrl(uri, mode: LaunchMode.externalApplication);
  } else {
    throw 'Could not launch $url';
  }
}

Future<String?> getDeviceId() async {
  String? deviceId;
  try {
    deviceId = await PlatformDeviceId.getDeviceId;
    return deviceId;
  } on PlatformException {
    deviceId = 'Failed to get deviceId.';
    CustomToast.showToast(deviceId);
    return deviceId;
  }
}

void showSnack(String text, BuildContext context, Function() onTap) {
  ScaffoldMessenger.of(context).showSnackBar(SnackBar(
      duration: Duration(seconds: 10),
      backgroundColor: cRedColor,
      showCloseIcon: true,
      behavior: SnackBarBehavior.floating,
      content: InkWell(
        onTap: onTap,
        child: Container(
          child: Text(
            text,
            textAlign: TextAlign.center,
          ),
        ),
      )));
}

void showDanger(String text) {
  Fluttertoast.showToast(
    msg: text,
    toastLength: Toast.LENGTH_SHORT,
    gravity: ToastGravity.TOP,
    timeInSecForIosWeb: 1,
    backgroundColor: Colors.redAccent,
    textColor: Colors.white,
    fontSize: 20.0,
  );
}

Future<dynamic> compareGPS(
    {required double lat,
    required double lng,
    required int allowedDistance}) async {
  try {
    var currentLat = (await determinePosition()).latitude;
    var currentLng = (await determinePosition()).longitude;

    var howMuch = await Geolocator.distanceBetween(
        lat,

        ///target latitude,
        lng,

        ///target longitude,
        currentLat,
        currentLng);

    print("Current lat: $currentLat\nCurrent lng: $currentLng ");

    if (howMuch < allowedDistance) {
      print('You are in field!');
      return true;
    } else {
      print("You are away: ${howMuch.round()}");
      return false;
    }
  } on LocationServiceDisabledException catch (e) {
    return LocaleKeys.give_accurate_location_permission.tr();
  } catch (e) {
    print(e);
    return e.toString();
  }
}

bool isDark() {
  final SharedPreferences prefs = di();
  return prefs.getString(theme_pref) == "ThemeMode.dark";
}

showCustomDialog(BuildContext context,
    [bool? isDismissible, bool? isIbratApp]) {
  WidgetsBinding.instance.addPostFrameCallback((time) {
    showDialog(
        context: context,
        barrierDismissible: isDismissible ?? true,
        builder: (context) => WillPopScope(
              onWillPop: () async => isDismissible ?? true,
              child: AllDialogSkeleton(
                color: Theme.of(context).cardTheme.color,
                isCloseVisible: isDismissible ?? true,
                child: Center(
                  child: Column(children: [
                    SizedBox(
                      height: 20.h,
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: context.isTablet ? 10.w : 0.w),
                      child: Text(
                        isIbratApp == true
                            ? LocaleKeys.permission_instruct_app.tr()
                            : LocaleKeys.permission_instruct.tr(),
                        style: TextStyle(
                            fontSize: context.isTablet ? 16.sp : 20.sp),
                      ),
                    ),
                    SizedBox(
                      height: 20.h,
                    ),
                    Icon(Icons.warning, size: 100.h, color: cRedColor),
                    SizedBox(
                      height: 20.h,
                    ),
                    MaterialButton(
                        padding: EdgeInsets.symmetric(
                            vertical: 8.h, horizontal: 10.w),
                        child: Text(
                          LocaleKeys.continue_word.tr(),
                          style: TextStyle(color: cWhiteColor, fontSize: 16.sp),
                        ),
                        height: 30.h,
                        minWidth: 150.w,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(15.r),
                        ),
                        color: cFirstColor,
                        onPressed: () async {
                          var switcher = isDismissible ?? true;
                          if (!switcher) {
                            Navigator.pop(context);
                            // await PackageUsageStats.openAppUsageSettings();
                          } else {
                            Navigator.pop(context);
                            await openAppSettings();
                          }
                        }),
                    SizedBox(
                      height: 10.h,
                    ),
                  ]),
                ),
                title: LocaleKeys.choose.tr(),
                icon: Assets.iconsTickCircle,
              ),
            ));
  });
}

String categoryTitle({Category? category}) {
  final SharedPreferences prefs = di();
  String lan = prefs.getString(language_pref) ?? "uz";

  switch (lan) {
    case 'uz':
      {
        return category?.titleUZ ?? ':(';
      }
    case 'kk':
      {
        return category?.titleQQ ?? ':(';
      }
    case 'ru':
      {
        return category?.titleRU ?? ':(';
      }
    default:
      {
        return category?.titleUZ ?? ':(';
      }
  }
}

String subCategoryTitle({SubCategory? subCategory}) {
  final SharedPreferences prefs = di();
  String lan = prefs.getString(language_pref) ?? "uz";

  switch (lan) {
    case 'uz':
      {
        return subCategory?.titleUZ ?? ':(';
      }
    case 'kk':
      {
        return subCategory?.titleQQ ?? ':(';
      }
    case 'ru':
      {
        return subCategory?.titleRU ?? ':(';
      }
    default:
      {
        return subCategory?.titleUZ ?? ':(';
      }
  }
}

String subCategoryWorkTitle({WorkSubCategory? subCategory}) {
  final SharedPreferences prefs = di();
  String lan = prefs.getString(language_pref) ?? "uz";

  switch (lan) {
    case 'uz':
      {
        return subCategory?.titleUZ ?? ':(';
      }
    case 'kk':
      {
        return subCategory?.titleQQ ?? ':(';
      }
    case 'ru':
      {
        return subCategory?.titleRU ?? ':(';
      }
    default:
      {
        return subCategory?.titleUZ ?? ':(';
      }
  }
}

String SpecialNewTaskTitle({SpecialNewTaskItem? specialNewTaskItem}) {
  final SharedPreferences prefs = di();
  String lan = prefs.getString(language_pref) ?? "uz";

  switch (lan) {
    case 'uz':
      {
        return specialNewTaskItem?.titleUZ ?? ':(';
      }
    case 'kk':
      {
        return specialNewTaskItem?.titleQQ ?? ':(';
      }
    case 'ru':
      {
        return specialNewTaskItem?.titleRU ?? ':(';
      }
    default:
      {
        return specialNewTaskItem?.titleUZ ?? ':(';
      }
  }
}

String taskImgModelTitle({TaskImgModel? taskImgModel}) {
  final SharedPreferences prefs = di();
  String lan = prefs.getString(language_pref) ?? "uz";

  switch (lan) {
    case 'uz':
      {
        return taskImgModel?.titleUZ ?? ':(';
      }
    case 'kk':
      {
        return taskImgModel?.titleQQ ?? ':(';
      }
    case 'ru':
      {
        return taskImgModel?.titleRU ?? ':(';
      }
    default:
      {
        return taskImgModel?.titleUZ ?? ':(';
      }
  }
}

String payResModelTitle({PaymentRes? paymentRes}) {
  final SharedPreferences prefs = di();
  String lan = prefs.getString(language_pref) ?? "uz";

  switch (lan) {
    case 'uz':
      {
        return paymentRes?.titleUZ ?? ':(';
      }
    case 'kk':
      {
        return paymentRes?.titleQQ ?? ':(';
      }
    case 'ru':
      {
        return paymentRes?.titleRU ?? ':(';
      }
    default:
      {
        return paymentRes?.titleUZ ?? ':(';
      }
  }
}

String employmentTypeTitle({required EmploymentType employmentType}) {
  final SharedPreferences prefs = di();
  String lan = prefs.getString(language_pref) ?? "uz";

  switch (lan) {
    case 'uz':
      {
        return employmentType.titleUZ ?? ':(';
      }
    case 'kk':
      {
        return employmentType.titleQQ ?? ':(';
      }
    case 'ru':
      {
        return employmentType.titleRU ?? ':(';
      }
    default:
      {
        return employmentType.titleUZ ?? ':(';
      }
  }
}

String employmentTypeTitleInCategory(
    {required EmploymentTypeInCategory employmentTypeInCategory}) {
  final SharedPreferences prefs = di();
  String lan = prefs.getString(language_pref) ?? "uz";

  switch (lan) {
    case 'uz':
      {
        return employmentTypeInCategory.titleUZ ?? ':(';
      }
    case 'kk':
      {
        return employmentTypeInCategory.titleQQ ?? ':(';
      }
    case 'ru':
      {
        return employmentTypeInCategory.titleRU ?? ':(';
      }
    default:
      {
        return employmentTypeInCategory.titleUZ ?? ':(';
      }
  }
}

Future<void> updateFirebaseToken() async {
  SharedPreferences prefs = di();
  prefs.reload();
  var token = await prefs.getString(firebaseTokenKEY) ?? '';
  final Dio client = di();
  if (token != "") {
    var body = {'token': token};
    try {
      var response = await client.post(updateFirebaseTokenPath, data: body);
      print("Update firebase token ====> " + response.data.toString());
    } on DioException catch (e, s) {
      // CustomToast.showToast("Can't update FT :(");
      print(e.toString() + s.toString());
    } catch (e, s) {
      print(e.toString() + s.toString());
    }
  } else {
    print("Post token failed: Empty token");
  }
}

//
// Future<void> checkIsAutoStartEnabled() async {
//   final _flutterAutostartPlugin = FlutterAutostart();
//   String isAutoStartEnabled;
//   try {
//     isAutoStartEnabled = await _flutterAutostartPlugin.checkIsAutoStartEnabled() == true ? "Yes" : "No";
//     print("isAutoStartEnabled: $isAutoStartEnabled");
//   } on PlatformException {
//     isAutoStartEnabled = 'Failed to check isAutoStartEnabled.';
//   }
// }
//
// Future<void> openAutoStartPermissionSettings() async {
//   String autoStartPermission;
//   final _flutterAutostartPlugin = FlutterAutostart();
//   try {
//     autoStartPermission =
//         await _flutterAutostartPlugin.showAutoStartPermissionSettings() ?? 'Unknown autoStartPermission';
//   } on PlatformException {
//     autoStartPermission = 'Failed to show autoStartPermission.';
//   }
// }

void autoStartWall(BuildContext context, bool shouldShowToast) async {
  //check auto-start availability.
  SharedPreferences prefs = di();
  var isAvailable =
      await (isAutoStartAvailable) ?? false; //For debugging make this true
  var isAutostartShown = prefs.getBool('isAutostartShown') ??
      false; //For debugging make this false
  print('=============== Is autostart available: $isAvailable');
  //if available then navigate to auto-start setting page.

  shouldShowToast ? isAutostartShown = false : isAutostartShown;

  if (isAvailable) {
    if (!isAutostartShown) {
      if (!shouldShowToast) {
        WidgetsBinding.instance.addPostFrameCallback((time) {
          showDialog(
              context: context,
              builder: (context) {
                return AllDialogSkeleton(
                    color: Theme.of(context).cardTheme.color,
                    child: Center(
                      child: Column(children: [
                        SizedBox(
                          height: 20.h,
                        ),
                        Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: context.isTablet ? 10.w : 0.w),
                          child: Text(
                            LocaleKeys.autoWallDesc.tr(),
                            textAlign: TextAlign.start,
                            style: TextStyle(
                                fontSize: context.isTablet ? 16.sp : 20.sp),
                          ),
                        ),
                        SizedBox(
                          height: 20.h,
                        ),
                        MaterialButton(
                            padding: EdgeInsets.all(8.w),
                            child: Text(
                              LocaleKeys.open_settings.tr(),
                              style: TextStyle(
                                  color: cWhiteColor, fontSize: 16.sp),
                            ),
                            color: cFirstColor,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(15.r),
                            ),
                            onPressed: () async {
                              Navigator.of(context).pop();
                              await getAutoStartPermission();
                            })
                      ]),
                    ),
                    title: LocaleKeys.warning1.tr(),
                    icon: Assets.iconsInfoCircle,
                    iconColor: cFirstColor);
              });
          prefs.setBool('isAutostartShown', true);
        });
      } else {
        await getAutoStartPermission();
      }
    }
  } else {
    if (shouldShowToast) {
      CustomToast.showToast(LocaleKeys.autostart_not_exist.tr());
    }
  }
}

String languageText() {
  SharedPreferences prefs = di();
  String? lang = prefs.getString(language_pref);
  switch (lang) {
    case 'uz':
      {
        return 'O\'zbekcha';
      }
    case 'ru':
      {
        return 'Русский';
      }
    case 'kk':
      {
        return 'Qaraqalpaqsha';
      }
    default:
      {
        return 'O\'zbekcha';
      }
  }
}

createNotification(String title) => AwesomeNotifications().createNotification(
    content: NotificationContent(
        id: 0,
        backgroundColor: cFirstColor,
        color: cFirstColor,
        channelKey: CHANNEL_KEY,
        title: title));

///Check payment function

Future<dynamic> checkUserPayment() async {
  final NetworkInfo networkInfo = di();
  final Dio dio = di();
  SharedPreferences prefs = di();

  if (await networkInfo.isConnected) {
    var id = prefs.getString('id') ?? '';
    var isDemo = prefs.getBool(is_demo) ?? false;

    ///

    try {
      final response = await dio.post(isDemo ? payCheckTestPath : payCheckPath,
          options: Options(
              headers: <String, String>{'Content-Type': 'application/json'}),
          data: {"user": id});
      final data = response.data['data'];
      print(data.toString());
      if (response.statusCode == 200) {
        String privateKey = DateTime.now().toIso8601String().substring(0, 10);
        var map;
        try {
          final des = decryptAESCryptoJS(data, privateKey);
          map = jsonDecode(des);
        } catch (e) {
          map = PaymentRes(
                  status: false,
                  titleUZ: 'Server xatoligi, keyinroq urining!',
                  titleRU: 'Ошибка сервера, повторите попытку позже!',
                  titleQQ: 'Server qateligi, keyin urınıń!',
                  date: 123456789)
              .toJson();
        }

        PaymentRes response = PaymentRes.fromJson(map);
        return response;
      } else {
        CustomToast.showToast(data.toString());
        print(data.toString());
        return null;
      }
    } on DioException catch (e) {
      if (e.type == DioExceptionType.badResponse) {
        if (e.response != null) {
          CustomToast.showToast('Server error: ' + "${e.response!.data}");
          print('Server error: ' + "${e.response!.data}");
        }
      }
      return null;
    }
  } else
    return false;
}

///Bug on some Android 11 devices, better save files to internal storage
Future<String> saveFileFromCacheToPath(
    String cacheFilePath, String destinationPath) async {
  try {
    // Read the file from cache
    File file = File(cacheFilePath);
    Uint8List bytes = await file.readAsBytes();

    // Write the file to the intended destination
    File destinationFile = File(destinationPath);
    await destinationFile.create(recursive: true);
    await destinationFile.writeAsBytes(bytes);
    print('File saved successfully to: $destinationPath');
    return destinationPath;
  } catch (e) {
    print('Error saving file: $e');
    CustomToast.showToast('Error saving file: $e');
    return destinationPath;
  }
}

Future<String> saveFileToInternalStorage(String filePath) async {
  // Get the directory for storing files
  Directory directory = await getApplicationDocumentsDirectory();
  String fileName = filePath.split('/').last;
  String newFilePath = '${directory.path}/$fileName';

  // Copy the image file
  File originalFile = File(filePath);
  File newFile = await originalFile.copy(newFilePath);

  print('File saved to: ${newFile.path}');

  return newFile.path;
}

Future<void> deleteFileFromInternalStorage(String fileName,
    {bool withPath = true}) async {
  try {
    // Get the application documents directory
    Directory appDocumentsDirectory = await getApplicationDocumentsDirectory();

    // Create a file path
    String filePath;
    if (!withPath) {
      filePath = '${appDocumentsDirectory.path}/$fileName';
    } else {
      filePath = fileName;
    }

    // Check if the file exists before attempting to delete
    if (await File(filePath).exists()) {
      // Delete the file
      await File(filePath).delete(recursive: true);
      print('File deleted successfully: $filePath');
    } else {
      print('File not found: $filePath');
      // CustomToast.showToast('File not found: $filePath');
    }
  } catch (e) {
    print('Error deleting file: $e');
    CustomToast.showToast('Error deleting file: $e');
  }
}

///Save and delete file to internal storage
Future<String?> saveByteFileToInternalStorage(
    String fileName, List<int> content) async {
  try {
    // Get the application documents directory
    Directory appDocumentsDirectory = await getApplicationDocumentsDirectory();

    // Create a file inside the documents directory
    File file = File('${appDocumentsDirectory.path}/$fileName');

    // Write the content to the file
    await file.writeAsBytes(content);

    print('File saved successfully: ${file.path}');
    return file.path;
  } catch (e) {
    print('Error saving file: $e');
    return null;
  }
}

///Device orientations

setUp() {
  SystemChrome.setPreferredOrientations(
    [DeviceOrientation.portraitUp],
  );
}

setDown() {
  SystemChrome.setPreferredOrientations(
    [DeviceOrientation.portraitDown],
  );
}

setVertical() {
  SystemChrome.setPreferredOrientations(
    [
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ],
  );
}

setHorizontal() {
  SystemChrome.setPreferredOrientations(
    [
      DeviceOrientation.landscapeRight,
      DeviceOrientation.landscapeLeft,
    ],
  );
}

setFullOrientation() {
  SystemChrome.setPreferredOrientations(
    [
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeRight,
      DeviceOrientation.landscapeLeft,
    ],
  );
}
