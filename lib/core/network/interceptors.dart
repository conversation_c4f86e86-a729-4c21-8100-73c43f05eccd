import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:get/get.dart' hide Response, Trans;
import 'package:govassist/env.dart';
import 'package:http_interceptor/http_interceptor.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:govassist/core/function/functions.dart';
import 'package:govassist/core/network/network_info.dart';
import 'package:govassist/core/utils/api_path.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/translations/locale_keys.g.dart';

///TODO: Fix interceptors (dio) and add to the bloc cases / Test here
class AppInterceptor extends Interceptor {
  final SharedPreferences prefs;
  final NetworkInfo networkInfo;

  AppInterceptor({required this.prefs, required this.networkInfo});

  @override
  void onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    if (prefs.getString("token") != null) {
      options.headers['Authorization'] = 'Bearer ${prefs.getString("token")}';
      options.headers["Accept"] = "application/json";
    }

    var isDemo = prefs.getBool(is_demo) ?? false;

    if (isDemo) {
      options.baseUrl = demoUrl;
    } else {
      options.baseUrl = AppEnvironment.baseApiUrl;
    }

    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    var id = prefs.getString('id');

    var isSpecialTask = err.requestOptions.path == specialTaskPath;

    if ((err.response?.statusCode == 403 || err.response?.statusCode == 404) &&
        !isSpecialTask) {
      if (id != null) {
        showDanger(LocaleKeys.user_not_found.tr());
        clearAndLogout(Get.context!);
      } else {
        // CustomToast.showToast('Error code: ${err.response?.statusCode ?? 'Invalid response'} Is ID null: ${id == null}');
      }
    }

    super.onError(err, handler);
  }
}

class ErrorInterceptor implements InterceptorContract {
  final SharedPreferences prefs;

  ErrorInterceptor({required this.prefs});

  @override
  Future<RequestData> interceptRequest({required RequestData data}) async {
    if (data.params['demo']) {
      data.baseUrl = demoUrl;
    } else {
      data.baseUrl = AppEnvironment.baseApiUrl;
    }
    return data;
  }

  @override
  Future<ResponseData> interceptResponse({required ResponseData data}) async {
    try {
      var id = prefs.getString('id');

      if (data.statusCode == 200) {
        print('== Interceptors are working ==');
        // Handle unauthorized request
      }

      if (id != null &&
          ((data.statusCode == 403) || (data.statusCode == 404))) {
        showDanger(LocaleKeys.user_not_found.tr());
        clearAndLogout(Get.context!);
      } else {
        // CustomToast.showToast('Error code: ${err.response?.statusCode ?? 'Invalid response'} Is ID null: ${id == null}');
      }
    } on FormatException catch (e) {
      print('Error in format in interceptors: $e');
    } catch (e) {
      print("Interceptors: $e");
    }
    return data;
  }
}
