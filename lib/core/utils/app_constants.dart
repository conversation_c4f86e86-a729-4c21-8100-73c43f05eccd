import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

///DEBUG
bool EMULATOR = false;

///These are app-specific colors
const cFirstColor = Color(0xFF1A8BFF);
const cFirstColorDark = Color(0xFF0E1150);
const cSecondColor = Color(0xFF006BD5);
const cThirdColor = Color(0xFF8E54E9);
const cFourthColor = Color(0xFFF8F9FD);

///Dark
const cFourthColorDark = Color(0xFF101322);
const cPrimaryTextDark = Color(0xFFA3A9C8);
const cCardDarkColor = Color(0xFF313551);

///These are classic colors
const cTextColor = Color(0xFF475E6A);
const cBlackColor = Color(0xFF000000);
const cWhiteColor = Color(0xFFFFFFFF);
const cRedColor = Color(0xFFFF3030);
const cBlueColor = Color(0xFF0088cc);
const cPurpleColor = Color(0xFFB000AB);
const cGrayColor0 = Color(0xFFE1E2E5);
const cGrayColor1 = Color(0xFF949494);
const cGrayColor2 = Color(0xFF4F4F4F);
const cGrayColor3 = Color(0xFF333333);
const cYellowColor = Color(0xFFFFC92F);
const cDarkYellowColor = Color(0xFFFAB93A);
const cOrangeColor = Color(0xFFFF9800);
const cFirstTextColor = Color(0xFF080936);
const cSecondTextColor = Color(0xFF080936);
const cLightBlue = Color(0xFF4C4DDC);
const cPinkLight = Color(0xFFFFEAEA);
const cRedTextColor = Color(0xFFF44747);
const cGreenColor = Color(0xFF13BB42);
const cCarrotColor = Color(0xFFFC6666);
const cIbratColor = Color(0xFFFF8500);
const cMintColor = Color(0xFF019875);

// All gradient
const cFirstGradient = LinearGradient(
  colors: [cSecondColor, cFirstColor],
  begin: Alignment.topCenter,
  end: Alignment.bottomCenter,
);

const cSecondGradient = LinearGradient(
  colors: [cSecondColor, cThirdColor],
  begin: Alignment.centerLeft,
  end: Alignment.centerRight,
);

const cMonoFakeGradient = LinearGradient(
  colors: [cFirstColor, cFirstColor],
  begin: Alignment.centerLeft,
  end: Alignment.centerRight,
);

var boxShadow5 = BoxShadow(
  color: cFirstColor.withOpacity(0.09),
  spreadRadius: 1.r,
  blurRadius: 5.r,
);

var boxShadow10 = BoxShadow(
  color: cFirstColor.withOpacity(0.09),
  spreadRadius: 1.r,
  blurRadius: 5.r,
  offset: Offset(0, 10.w), // changes position of shadow
);

var boxShadow20 = BoxShadow(
  color: cFirstColor.withOpacity(0.09),
  spreadRadius: 1.r,
  blurRadius: 20.r,
  offset: Offset(0, 10.w), // changes position of shadow
);

var boxShadow60 = BoxShadow(
  color: cFirstColor.withOpacity(0.2),
  spreadRadius: 1.r,
  blurRadius: 60.r,
  offset: Offset(0, 10.w), // changes position of shadow
);

// All keys for local caches
const String user_data = 'user_data';
const String pin_code = "pin_code";
const String local_approved = "local_approved";
const String server_approved = "server_approved";
const String on_work = "on_work";
const String show_work_slider = "show_on_work";
const String on_setting = "setting";
const String functional_live = 'functional_live';
const String is_demo = "is_demo";

const int ALLOWED_DISTANCE = 150;
const int ALLOWED_MINUTE = 3;
const String MAX_IDENTIFY = "0.8";
const String MAX_LIVENESS = "0.7";
const int BACK_SERVICE_TIME = 900;
const int BACK_OUTSIDE_SERVICE_TIME = 60;
const int TIME_TEST = 60;

const String is_time_correct = "is_time_correct";
const String is_gps_active = "is_gps_active";
const String is_not_mocked = "is_not_mocked";

// All table names for local databases
const String categoryBox = 'category_box';
const String subCategoryBox = 'sub_category_box';
const String objectBox = 'object_box';
const String newHistoryBox = 'new_history_box';
const String oldHistoryBox = 'old_history_box';
const String profileBox = 'profile_box';
const String workSendBox = 'work_send_box';
const String taskSendBox = 'task_send_box';

// All sizes
const double cRadius8 = 8.0;
const double cRadius10 = 10.0;
const double cRadius12 = 12.0;
const double cRadius16 = 16.0;
const double cRadius22 = 22.0;
const double cRadius36 = 36.0;
const double cRadius40 = 30.0;

const double cNumberLockW90 = 95.0;
const double cNumberLockH90 = 90.0;
const double cNumberLockW70 = 75.0;
const double cNumberLockH70 = 70.0;
const double cNumberLockText42 = 42.0;
const double cNumberLockText32 = 32.0;

const double HEADER_SIZE = 120;
const double DESIGN_WIDTH = 375;
const double DESIGN_HEIGHT = 812;
const double HEADER_SIZE_SUB = 40;

// Variables
String APP_VERSION = "app_version";
String NOTIFICATION_COUNT = "notification_count";

//Contacts
const String CLICK_SERVICE_ID = '32899';
const String CLICK_MERCHANT_ID = '24892';
const String CLICK_MERCHANT_USER_ID = '39885';
const String PAYME_MERCHANT_ID = '660e504b9f11a75ee8e7ab80';
const String SUPPORT_TG = 't.me/hokimyordamchisi_support';
const String TEL = '732443525';

const String SUPPORT_TEL1 = "+998732443525";
const String SUPPORT_TEL2 = "+998732443505";
const String EMAIL = "<EMAIL>";
const String TELEGRAM = "https://t.me/hokimyordamchisi_support";
const String WEBSITE = "https://premiumsoft.uz";
const String MODERATOR = "https://t.me/fvhmoderator";
const String theme_pref = "theme_pref";
const String language_pref = "language_pref";
const String TELEGRAM_PACKAGE_NAME = "org.telegram.messenger";
const String USAGE_DURATION = "usageDuration";
const String USAGE_DATE = "usageDate";

const String firebaseTokenKEY = "firebase_token";
const String baseUrlPref='baseUrlPref';

const String APP_LINK =
    "https://play.google.com/store/apps/details?id=uz.premiumsoft.govassist";
const String androidDownloadPath = "/storage/emulated/0/Download/HokimYordamchisi/";
const IMAGE = "image";
const VIDEO = "video";
const TEXT = "text";
const String EMPTY = "Empty...";

// lock number style

const numStyle =
TextStyle(fontSize: 30, fontWeight: FontWeight.w400, color: cWhiteColor);

class AppStrings {
  static const strNoRouteFound = "no_route_found";
  static const strAppName = "app_name";

  static const String success = "success";

  // error handler
  static const String strBadRequestError = "bad_request_error";
  static const String strNoContent = "no_content";
  static const String strForbiddenError = "forbidden_error";
  static const String strUnauthorizedError = "unauthorized_error";
  static const String strNotFoundError = "not_found_error";
  static const String strConflictError = "conflict_error";
  static const String strInternalServerError = "internal_server_error";
  static const String strUnknownError = "unknown_error";
  static const String strTimeoutError = "timeout_error";
  static const String strDefaultError = "default_error";
  static const String strCacheError = "cache_error";
  static const String strNoInternetError = "no_internet_error";

  ///FaceTokens

  static const String androidFaceToken =
      "DeJx2tsSxyvyGj4V1I/r46SEXRPg4KltTZ0i8bnpY7LZem13F4n254Q0lW+wbUNv80JwUKIwaxTa"
      "cnboLsQZnZJ2K8SooutNecuWIEXv60+X4BTvttud3T616kt920+LdbaXF+qRkVvA2H84KxxwifT6"
      "1ce3KGQ1edm67v9xh7ZhTQsjEdT5cGafRpVtJpDzJuy37MmuaLL8koX/jlCAo1eRUZSrR+VLn+Tm"
      "YYxHY8ZimsLgWaxey+LbG7PCVW4ciBjM2HRON73Ugg0EH3c8KtPVwyxZcT8T7cjoDKYS5hj/zm3D"
      "QENhY3htq4VEDx0Cowk594iKelt6PQ070HEkpA==";

  static const String iOSFaceToken =
      "nWsdDhTp12Ay5yAm4cHGqx2rfEv0U+Wyq/tDPopH2yz6RqyKmRU+eovPeDcAp3T3IJJYm2LbPSEz"
      "+e+YlQ4hz+1n8BNlh2gHo+UTVll40OEWkZ0VyxkhszsKN+3UIdNXGaQ6QL0lQunTwfamWuDNx7Ss"
      "efK/3IojqJAF0Bv7spdll3sfhE1IO/m7OyDcrbl5hkT9pFhFA/iCGARcCuCLk4A6r3mLkK57be4r"
      "T52DKtyutnu0PDTzPeaOVZRJdF0eifYXNvhE41CLGiAWwfjqOQOHfKdunXMDqF17s+LFLWwkeNAD"
      "PKMT+F/kRCjnTcC8WPX3bgNzyUBGsFw9fcneKA==";
}
