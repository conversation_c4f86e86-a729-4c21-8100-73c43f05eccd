import 'package:camera/camera.dart';
import 'package:flutter/cupertino.dart';
import 'package:govassist/core/widgets/custom_toast.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:govassist/di/dependency_injection.dart';
import 'package:govassist/features/send_data/camera_page.dart';

abstract class ImagePickerUtils {
  Future<String> selectImageFromCamera(BuildContext context,
      {bool isVideo, bool isHD, bool isFront, String? previewMessage});
}

class ImagePickerUtilsImpl extends ImagePickerUtils {
  List<CameraDescription> cameras = di();
  SharedPreferences? prefs = di();

  @override
  Future<String> selectImageFromCamera(BuildContext context,
      {isVideo = false, isHD = false, isFront = false, previewMessage}) async {
    try {
      String filePath = await Navigator.push(
          context,
          CupertinoPageRoute(
              builder: (context) => TakePictureScreen(
                  cameras: cameras,
                  isVideo: isVideo,
                  previewMessage: previewMessage,
                  isHD: isHD,
                  isFront: isFront)));

      if (filePath.isNotEmpty) {
        ///Compression preserved for later usage
        // File compressedFile = await FlutterNativeImage.compressImage(
        //   files[0],
        //   // quality: 30,
        // );
        return filePath;
      } else {
        CustomToast.showToast('File path is empty');
        return '';
      }
    } catch (ex, s) {
      // CustomToast.showToast(ex.toString() + s.toString());
      return '';
    }
  }
}
