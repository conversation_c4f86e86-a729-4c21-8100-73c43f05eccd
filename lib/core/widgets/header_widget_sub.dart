import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:govassist/core/function/functions.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/generated/assets.dart';

PreferredSize AppHeaderWidgetSub(
    {required String title,
    required VoidCallback onBackTap,
    required String actionImage,
    required VoidCallback onActionTap,
    bool? isActionImageVisibility}) {
  return PreferredSize(
    preferredSize: Size.fromHeight(HEADER_SIZE.h),
    child: HeaderWidgetSub(
        title: title,
        onBackTap: onBackTap,
        actionImage: actionImage,
        onActionTap: onActionTap,
        isActionImageVisibility: isActionImageVisibility), // Set this height
  );
}

class HeaderWidgetSub extends StatelessWidget {
  final String title;
  final String actionImage;
  final VoidCallback onBackTap;
  final VoidCallback onActionTap;
  final bool? isActionImageVisibility;

  const HeaderWidgetSub(
      {super.key,
      required this.title,
      required this.actionImage,
      required this.onBackTap,
      required this.onActionTap,
      this.isActionImageVisibility});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: ClipRRect(
        borderRadius: BorderRadius.only(
            bottomRight: Radius.circular(20.r),
            bottomLeft: Radius.circular(20.r)),
        child: Container(
          margin: EdgeInsets.symmetric(vertical: 25.h),
          padding: EdgeInsets.symmetric(horizontal: 15.w),
          width: MediaQuery.of(context).size.width,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ClipOval(
                child: Material(
                  color: isDark() ? cCardDarkColor : cFirstColor.withAlpha(20),
                  child: IconButton(
                    iconSize: 30.r,
                    onPressed: onBackTap,
                    icon: SvgPicture.asset(
                      Assets.iconsArrowLeft,
                      width: 20.h,
                      height: 20.h,
                      colorFilter: ColorFilter.mode(
                          isDark() ? cPrimaryTextDark : cFirstColor,
                          BlendMode.srcIn),
                    ),
                  ),
                ),
              ),
              Text(
                title,
                style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.w500),
              ),
              isActionImageVisibility == true
                  ? SvgPicture.asset(
                      actionImage,
                      width: 45.h,
                      height: 45.h,
                    )
                  : SizedBox(
                      width: 45.h,
                      height: 45.h,
                    ),
            ],
          ),
        ),
      ),
    );
  }
}
