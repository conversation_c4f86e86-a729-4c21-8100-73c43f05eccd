import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:govassist/core/utils/app_constants.dart';

class GradientMaterialButton extends StatelessWidget {
  final String? title;
  final Widget? child;
  final VoidCallback? onTap;
  final double? width;
  final double? height;

  const GradientMaterialButton(
      {super.key,
      this.title,
      required this.onTap,
      this.width,
      this.height,
      this.child});

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(cRadius10.r),
      child: Container(
          alignment: Alignment.center,
          width: width,
          height: height,
          decoration: BoxDecoration(
              gradient: LinearGradient(
                // begin: Alignment.topCenter,
                // end: Alignment.bottomCenter,
                colors: [
                  onTap != null ? cFirstColor : cGrayColor1,
                  onTap != null ? cSecondColor : cGrayColor1,
                ],
              ),
              borderRadius: BorderRadius.circular(cRadius10.r)),
          child: MaterialButton(
            minWidth: MediaQuery.of(context).size.width,
            height: 56.h,
            onPressed: onTap,
            child: child != null
                ? this.child
                : Text(
                    title ?? 'empty',
                    style: TextStyle(color: cWhiteColor, fontSize: 17.sp),
                  ),
          )),
    );
  }
}
