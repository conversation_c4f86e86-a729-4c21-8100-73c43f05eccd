import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart' hide Trans;
import 'package:google_nav_bar/google_nav_bar.dart';
import 'package:badges/badges.dart' as badges;
import 'package:isar/isar.dart';
import 'package:govassist/core/database/isar_service.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/core/widgets/custom_toast.dart';
import 'package:govassist/di/dependency_injection.dart';
import 'package:govassist/features/send_data/models/not_send_model.dart';
import 'package:govassist/features/task_send/data/model/task_img_model.dart';
import 'package:govassist/generated/assets.dart';
import 'package:govassist/translations/locale_keys.g.dart';
import 'package:zoom_tap_animation/zoom_tap_animation.dart';

class RoundedBottomNavigationBar extends StatefulWidget {
  final Function(int index) onSelected;
  final int? number;
  final int? pageIndex;

  const RoundedBottomNavigationBar(
      {super.key, required this.onSelected, this.number, this.pageIndex});

  @override
  State<RoundedBottomNavigationBar> createState() =>
      _RoundedBottomNavigationBarState();
}

class _RoundedBottomNavigationBarState
    extends State<RoundedBottomNavigationBar> {
  int _selectedIndex = 0;
  final IsarService isarService = di();
  int notSendCount = 0;

  count() {
    int task = isarService.isar.taskImgModels.where().countSync();
    int work = isarService.isar.notSendModels.where().countSync();
    notSendCount = task + work;
  }

  @override
  void initState() {
    super.initState();
    _selectedIndex = widget.pageIndex ?? 0;
  }

  @override
  Widget build(BuildContext context) {
    count();
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h), // Reduced padding
      margin: EdgeInsets.only(bottom: 10.h, top: 2.h, left: 8.w, right: 8.w), // Reduced margin
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(cRadius10.r),
        gradient: cMonoFakeGradient,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(cRadius10.r),
        child: GNav(
          rippleColor: cWhiteColor,
          hoverColor: cWhiteColor,
          haptic: true,
          tabBorderRadius: cRadius10.r,
          gap: 4.w, // Reduced gap
          color: cWhiteColor,
          activeColor: cFirstColor,
          iconSize: 22, // Slightly smaller icon size
          tabBackgroundColor: cWhiteColor,
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h), // Reduced padding
          tabs: [
            GButton(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h), // Reduced padding
              leading: SizedBox(
                width: 22.h, // Slightly smaller
                height: 22.h,
                child: SvgPicture.asset(Assets.iconsHome,
                    colorFilter: ColorFilter.mode(
                        _selectedIndex == 0
                            ? cFirstColor
                            : cWhiteColor.withOpacity(0.5),
                        BlendMode.srcIn)),
              ),
              text: LocaleKeys.main_menu.tr(),
              icon: Icons.start,
              textStyle: TextStyle(fontSize: 12.sp, color: cFirstColor), // Smaller font
            ),
            GButton(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h), // Added consistent padding
              icon: Icons.search,
              leading: badges.Badge(
                position: badges.BadgePosition.custom(start: 8.w, bottom: 4.h), // Adjusted position
                badgeStyle: badges.BadgeStyle(padding: EdgeInsets.all(3.w)), // Smaller badge
                showBadge: notSendCount != 0 ? true : false,
                badgeContent: Text(
                  notSendCount.toString(),
                  style: TextStyle(color: cWhiteColor, fontSize: 10.sp), // Smaller badge text
                ),
                child: SizedBox(
                  width: 22.h, // Consistent size
                  height: 22.h,
                  child: SvgPicture.asset(Assets.iconsAward,
                      colorFilter: ColorFilter.mode(
                          _selectedIndex == 1
                              ? cFirstColor
                              : cWhiteColor.withOpacity(0.5),
                          BlendMode.srcIn)),
                ),
              ),
              text: LocaleKeys.not_send_menu.tr(),
              textStyle: TextStyle(fontSize: 12.sp, color: cFirstColor), // Smaller font
            ),
            GButton(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h), // Consistent padding
              icon: Icons.person,
              leading: SizedBox(
                width: 22.h, // Consistent size
                height: 22.h,
                child: SvgPicture.asset(Assets.iconsTickCircle,
                    colorFilter: ColorFilter.mode(
                        _selectedIndex == 2
                            ? cFirstColor
                            : cWhiteColor.withOpacity(0.5),
                        BlendMode.srcIn)),
              ),
              text: LocaleKeys.sent_menu.tr(),
              textStyle: TextStyle(fontSize: 12.sp, color: cFirstColor), // Smaller font
            ),
            GButton(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h), // Consistent padding
              icon: Icons.person,
              leading: SizedBox(
                width: 22.h, // Consistent size
                height: 22.h,
                child: SvgPicture.asset(Assets.iconsPersonalcard,
                    colorFilter: ColorFilter.mode(
                        _selectedIndex == 3
                            ? cFirstColor
                            : cWhiteColor.withOpacity(0.5),
                        BlendMode.srcIn)),
              ),
              text: "Bandlik",
              textStyle: TextStyle(fontSize: 12.sp, color: cFirstColor), // Smaller font
            ),
          ],
          selectedIndex: _selectedIndex,
          onTabChange: (index) {
            setState(() {
              _selectedIndex = index;
              widget.onSelected(_selectedIndex);
            });
          },
        ),
      ),
    );
  }
}

class RoundedBookReadingWidget extends StatelessWidget {
  const RoundedBookReadingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return ZoomTapAnimation(
      onTap: () {
        CustomToast.showToast("${LocaleKeys.soon_add_feature.tr()}...");
      },
      child: Stack(
        alignment: Alignment.center,
        children: [
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
            margin: EdgeInsets.only(
                bottom: 10.h, top: 2.h, left: 20.w, right: 20.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(cRadius10.r),
              gradient: LinearGradient(
                colors: [
                  cFirstColor,
                  cThirdColor,
                ],
              ),
            ),
            child: Row(
              children: [
                Spacer(
                  flex: 3,
                ),
                Expanded(
                  flex: 10,
                  child: Padding(
                    padding: EdgeInsets.only(top: 5.h, bottom: 5.h),
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(
                        LocaleKeys.digital_library.tr(),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            fontSize: context.isTablet ? 16.sp : 18.sp,
                            fontWeight: FontWeight.w600,
                            color: cWhiteColor),
                      ),
                    ),
                  ),
                ),
                Spacer(
                  flex: 1,
                )
              ],
            ),
          ),
          SizedBox(
            height: 130.h,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Padding(
                    padding: EdgeInsets.only(left: 35.w, bottom: 40.h),
                    child: Image.asset(Assets.iconsBookPng,
                        height: 60.h, width: 60.w, scale: 0.1)),
              ],
            ),
          )
        ],
      ),
    );
  }
}