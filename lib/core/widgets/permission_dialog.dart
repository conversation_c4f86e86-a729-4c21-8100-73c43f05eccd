import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:govassist/core/utils/app_constants.dart';

class PermissionDialog extends StatelessWidget {
  final IconData icon;
  final String text;

  const PermissionDialog({super.key, required this.icon, required this.text});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.r)),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8.r),
        child: Container(
          width: MediaQuery.of(context).size.width - 20.w,
          height: MediaQuery.of(context).size.width - 100.w,
          child: Column(
            children: [
              Expanded(
                  flex: 2,
                  child: Container(
                    alignment: Alignment.center,
                    color: Colors.blue,
                    child: Icon(icon,size: 40.w,color: cWhiteColor,),
                  )),
              Expanded(
                  flex: 3,
                  child: Container(
                    padding: EdgeInsets.only(top: 20.h,left: 20.w,right: 20.w),
                    color: Colors.white,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(text,style: TextStyle(fontSize: 18.sp),),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            TextButton(onPressed: (){
                              Navigator.pop(context);
                            }, child: Text("Not now",style: TextStyle(fontSize: 16.sp),)),
                            TextButton(onPressed: (){
                              openAppSettings();
                              Navigator.pop(context);
                            }, child: Text("Settings",style: TextStyle(fontSize: 16.sp))),
                          ],
                        )
                      ],
                    ),
                  )),
            ],
          ),
        ),
      ),
    );
  }
}
