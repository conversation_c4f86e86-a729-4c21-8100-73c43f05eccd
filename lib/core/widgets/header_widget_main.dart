import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get_storage/get_storage.dart';
import 'package:govassist/core/database/isar_service.dart';
import 'package:govassist/core/function/functions.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/di/dependency_injection.dart';
import 'package:govassist/generated/assets.dart';
import 'package:badges/badges.dart' as badges;

PreferredSize AppHeaderWidgetMain(
    {required VoidCallback onHamburgerTap, required VoidCallback onActionTap}) {
  return PreferredSize(
    preferredSize: Size.fromHeight(HEADER_SIZE.h),
    child: HeaderWidgetMain(
      onHamburgerTap: onHamburgerTap,
      onActionTap: onActionTap,
    ), // Set this height
  );
}

class HeaderWidgetMain extends StatefulWidget {
  final VoidCallback onHamburgerTap;
  final VoidCallback onActionTap;

  HeaderWidgetMain(
      {super.key, required this.onHamburgerTap, required this.onActionTap});

  @override
  State<HeaderWidgetMain> createState() => _HeaderWidgetMainState();
}

class _HeaderWidgetMainState extends State<HeaderWidgetMain> {
  final IsarService isarService = di();
  final GetStorage gs = di();
  int taskCount = 0;

  @override
  void initState() {
    super.initState();

    taskCount = gs.read(NOTIFICATION_COUNT) ?? 0;

    gs.listenKey(NOTIFICATION_COUNT, (value) {
      print('new key is $value');
      if (!mounted) {
        taskCount = value;
        return;
      } else {
        setState(() {
          taskCount = value;
        });
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.only(
          bottomRight: Radius.circular(20.r),
          bottomLeft: Radius.circular(20.r)),
      child: Container(
        width: MediaQuery.of(context).size.width,
        decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDark()
                  ? [cCardDarkColor, cCardDarkColor]
                  : [
                      cSecondColor,
                      cFirstColor,
                    ],
            ),
            borderRadius: BorderRadius.only(
                bottomRight: Radius.circular(20.r),
                bottomLeft: Radius.circular(20.r))),
        child: Stack(
          alignment: Alignment.center,
          children: [
            SvgPicture.asset(
              Assets.iconsWeb,
              width: MediaQuery.of(context).size.width,
              fit: BoxFit.fill,
            ),
            Padding(
              padding: EdgeInsets.only(
                  left: 5.w, right: 5.w, top: 40.h, bottom: 10.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Material(
                      color: Colors.transparent,
                      shape: CircleBorder(),
                      clipBehavior: Clip.hardEdge,
                      child: IconButton(
                        onPressed: widget.onHamburgerTap,
                        iconSize: 40.h,
                        icon: SvgPicture.asset(
                          Assets.iconsHamburger,
                          width: 40.h,
                          height: 40.h,
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: SvgPicture.asset(Assets.imagesYiaVector,
                        height: 40.h, color: cWhiteColor),
                  ),
                  Expanded(
                    child: Material(
                      color: Colors.transparent,
                      shape: CircleBorder(),
                      clipBehavior: Clip.hardEdge,
                      child: Container(
                        height: 60.h,
                        width: 60.h,
                        child: IconButton(
                          onPressed: widget.onActionTap,
                          iconSize: 40.h,
                          icon: Container(
                            height: 40.h,
                            width: 40.h,
                            decoration: BoxDecoration(
                                border: Border.all(
                                  width: 1.w,
                                  color: cGrayColor0.withAlpha(100),
                                ),
                                borderRadius:
                                    BorderRadius.all(Radius.circular(60.r))),
                            child: Center(
                                child: badges.Badge(
                              badgeStyle: badges.BadgeStyle(
                                  padding: EdgeInsets.all(4.w)),
                              position: badges.BadgePosition.custom(
                                  start: 10.w, bottom: 6.h),
                              showBadge: taskCount != 0 ? true : false,
                              badgeContent: Text(
                                taskCount.toString(),
                                style: TextStyle(
                                    fontSize: 10.sp, color: cWhiteColor),
                              ),
                              child: SizedBox(
                                width: 20.w,
                                height: 20.h,
                                child: SvgPicture.asset(
                                  Assets.iconsBell,
                                ),
                              ),
                            )),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
