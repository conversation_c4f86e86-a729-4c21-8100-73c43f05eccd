import 'dart:math';

import 'package:action_slider/action_slider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:slide_action/slide_action.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/generated/assets.dart';

class CustomGradientButtonAction extends StatelessWidget {
  final BorderRadiusGeometry? borderRadius;
  final double? width;
  final double height;
  final Gradient gradient;
  final VoidCallback? onPressed;
  final VoidCallback? onAction;
  final Widget child;

  const CustomGradientButtonAction({
    Key? key,
    this.onPressed,
    required this.child,
    this.borderRadius,
    this.width,
    this.height = 55.0,
    this.gradient = const LinearGradient(colors: [Colors.cyan, Colors.indigo]),
    this.onAction,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final borderRadius =
        this.borderRadius ?? BorderRadius.all(Radius.circular(40.r));
    return Container(
      width: width,
      height: height.h,
      decoration: BoxDecoration(
        gradient: gradient,
        borderRadius: borderRadius,
      ),
      child: Container(
          padding: EdgeInsets.only(left: 5.w, right: 15.w),
          decoration: BoxDecoration(
            borderRadius: borderRadius,
          ),
          child: SlideAction(
            trackHeight: 45.h,
            trackBuilder: (context, state) {
              return Row(
                children: [
                  Spacer(
                    flex: 5,
                  ),
                  AnimatedOpacity(
                    opacity: 1 -
                        subtractWithGeometricProgression(
                            state.thumbFractionalPosition + 0.8, 10, 0.8),
                    duration: Duration(milliseconds: 100),
                    child: child,
                  ),
                  Spacer(
                    flex: 1,
                  ),
                ],
              );
            },
            thumbBuilder: (context, state) {
              return CircleAvatar(
                  backgroundColor: cWhiteColor,
                  child: SvgPicture.asset(
                    Assets.iconsSwipe,
                    height: 8.h,
                  ));
            },
            action: onAction,
          )),
    );
  }

  double subtractWithGeometricProgression(
      double initialPosition, int numTerms, double commonRatio) {
    double sum = 0.0; // initialize the sum to 0

    // Start the loop from 1 instead of 0 to account for the initial position
    for (int i = 1; i <= numTerms; i++) {
      double term = (1 - initialPosition) *
          pow(commonRatio, i - 1); // calculate the ith term
      sum += term; // add the ith term to the sum
    }

    double newPosition =
        initialPosition - sum; // subtract the sum from the initial position
    newPosition = max(
        0.0,
        min(newPosition,
            1.0)); // clamp the resulting value to the range of 0 to 1

    return newPosition;
  }
}

class CustomGradientButtonActionUniversal extends StatelessWidget {
  final BorderRadiusGeometry? borderRadius;
  final double? width;
  final double height;
  final Gradient gradient;
  final VoidCallback? onPressed;
  final Function(ActionSliderController)? onAction;
  final Widget child;

  const CustomGradientButtonActionUniversal({
    Key? key,
    this.onPressed,
    required this.child,
    this.borderRadius,
    this.width,
    this.height = 70.0,
    this.gradient = const LinearGradient(colors: [Colors.cyan, Colors.indigo]),
    this.onAction,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final borderRadius =
        this.borderRadius ?? BorderRadius.all(Radius.circular(40.r));
    return ActionSlider.standard(
      sliderBehavior: SliderBehavior.stretch,
      toggleColor: cWhiteColor,
      //Always use with .toInt().toDouble()
      borderWidth: 5.w.toInt().toDouble(),
      customOuterBackgroundBuilder: (c, s, w) {
        return Container(
          width: width,
          height: height.h,
          decoration: BoxDecoration(
            gradient: gradient,
            borderRadius: borderRadius,
          ),
        );
      },
      customBackgroundBuilder: (c, state, w) {
        print(state.position);
        return Container(
          width: width,
          height: height.h,
          decoration: BoxDecoration(
            gradient: gradient,
            borderRadius: borderRadius,
          ),
          child: Row(
            children: [
              Spacer(
                flex: 5,
              ),
              Flexible(
                flex: 8,
                child: AnimatedOpacity(
                  opacity: 1 -
                      subtractWithGeometricProgression(
                          state.position + 0.8, 10, 0.8),
                  duration: Duration(milliseconds: 100),
                  child:
                      Visibility(visible: state.position < 0.4, child: child),
                ),
              ),
              Spacer(
                flex: 1,
              ),
            ],
          ),
        );
      },
      icon: SvgPicture.asset(
        Assets.iconsSwipe,
        height: 14.h,
      ),
      //Always use with .toInt().toDouble()
      height: (height - 10).h.toInt().toDouble(),
      failureIcon: Icon(
        Icons.close_rounded,
        size: 22.h,
      ),
      loadingIcon: CircularProgressIndicator(
        strokeWidth: 2.w,
        color: cSecondColor,
      ),
      successIcon: Icon(
        Icons.check_rounded,
        size: 22.h,
      ),
      action: onAction,
    );
  }

  double subtractWithGeometricProgression(
      double initialPosition, int numTerms, double commonRatio) {
    double sum = 0.0; // initialize the sum to 0

    // Start the loop from 1 instead of 0 to account for the initial position
    for (int i = 1; i <= numTerms; i++) {
      double term = (1 - initialPosition) *
          pow(commonRatio, i - 1); // calculate the ith term
      sum += term; // add the ith term to the sum
    }

    double newPosition =
        initialPosition - sum; // subtract the sum from the initial position
    newPosition = max(
        0.0,
        min(newPosition,
            1.0)); // clamp the resulting value to the range of 0 to 1

    return newPosition;
  }
}

class RectangularActionButton extends StatelessWidget {
  final BorderRadiusGeometry? borderRadius;
  final double? width;
  final double height;
  final Gradient gradient;
  final VoidCallback? onPressed;
  final Function(ActionSliderController)? onAction;
  final Widget child;

  const RectangularActionButton({
    Key? key,
    this.onPressed,
    required this.child,
    this.borderRadius,
    this.width,
    this.height = 70.0,
    this.gradient = const LinearGradient(colors: [Colors.cyan, Colors.indigo]),
    this.onAction,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final borderRadius =
        this.borderRadius ?? BorderRadius.all(Radius.circular(10.r));
    final _controller = ActionSliderController();
    var position = 0.0;

    return ActionSlider.custom(
        sliderBehavior: SliderBehavior.stretch,
        controller: _controller,
        height: (height - 10).h.toInt().toDouble(),
        toggleWidth: 45.w.toInt().toDouble(),
        action: onAction,
        toggleMargin: EdgeInsets.all(4.w.toInt().toDouble()),
        foregroundBuilder: (context, state, child) {

      print(state.sliderMode.key.key);

      switch (state.sliderMode.key.key as String) {
        case 'standard':
          return DecoratedBox(
              decoration: BoxDecoration(
                  color: cWhiteColor, borderRadius: BorderRadius.circular(8.r)),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 8.w),
                child: SvgPicture.asset(
                  Assets.iconsSwipe,
                  color: cFirstColor,
                  height: 10.h,
                  fit: BoxFit.fitWidth,
                ),
              ));
        case 'loading':
          return DecoratedBox(
              decoration: BoxDecoration(
                  color: cWhiteColor, borderRadius: BorderRadius.circular(8.r)),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 8.w),
                child: CupertinoActivityIndicator(radius: 10.r, color: cFirstColor,),
              ));
      }
      return DecoratedBox(
          decoration: BoxDecoration(
              color: cWhiteColor, borderRadius: BorderRadius.circular(8.r)),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 8.w),
            child: SvgPicture.asset(
              Assets.iconsSwipe,
              color: cFirstColor,
              height: 10.h,
              fit: BoxFit.fitWidth,
            ),
          ));
        },
        outerBackgroundBuilder: (context, state, child) {
          print(position);
          return Container(
            width: width,
            height: height.h,
            decoration: BoxDecoration(
              gradient: gradient,
              borderRadius: borderRadius,
            ),
            child: Row(
              children: [
                Spacer(
                  flex: 5,
                ),
                Flexible(
                  flex: 8,
                  child: AnimatedOpacity(
                    opacity: 1 -
                        subtractWithGeometricProgression(
                            state.position + 0.8, 10, 0.8),
                    duration: Duration(milliseconds: 100),
                    child: Visibility(
                        visible: state.position < 0.4, child: this.child),
                  ),
                ),
                Spacer(
                  flex: 1,
                ),
              ],
            ),
          );
        });
  }

  double subtractWithGeometricProgression(
      double initialPosition, int numTerms, double commonRatio) {
    double sum = 0.0; // initialize the sum to 0

    // Start the loop from 1 instead of 0 to account for the initial position
    for (int i = 1; i <= numTerms; i++) {
      double term = (1 - initialPosition) *
          pow(commonRatio, i - 1); // calculate the ith term
      sum += term; // add the ith term to the sum
    }

    double newPosition =
        initialPosition - sum; // subtract the sum from the initial position
    newPosition = max(
        0.0,
        min(newPosition,
            1.0)); // clamp the resulting value to the range of 0 to 1

    return newPosition;
  }

  String? extractKey(String input) {
    RegExp regex = RegExp(r'key: (\$\w+)');
    RegExpMatch? match = regex.firstMatch(input);
    if (match != null) {
      return match.group(1);
    }
    return null;
  }
}
