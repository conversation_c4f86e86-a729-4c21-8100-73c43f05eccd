import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:govassist/core/function/functions.dart';
import 'package:govassist/translations/locale_keys.g.dart';

Future<bool?> showAlert(BuildContext context) async {
  return await showCupertinoDialog<bool>(
      context: context,
      builder: (BuildContext ctx) {
        return Theme(
          data: isDark()?ThemeData.dark():ThemeData.light(),
          child: CupertinoAlertDialog(
            title:  Text(LocaleKeys.do_confirm.tr()),
            content:  Text(LocaleKeys.sure_to_delete.tr()),
            actions: [
              // The "Yes" button
              CupertinoDialogAction(
                  onPressed: () {
                    // Close the dialog
                    Navigator.of(context).pop(true);
                  },
                  child:  Text(LocaleKeys.yes.tr(),style: TextStyle(color: Theme.of(context).primaryColor))),
              CupertinoDialogAction(
                  onPressed: () {
                    // Close the dialog
                    Navigator.of(context).pop(false);
                  },
                  child:  Text(LocaleKeys.no.tr(),style: TextStyle(color: Theme.of(context).primaryColor)))
            ],
          ),
        );
      });
}

Future<bool?> showAlertText(BuildContext context, String question) async {
  return await showCupertinoDialog<bool>(
      context: context,
      builder: (BuildContext ctx) {
        return Theme(
          data: isDark()?ThemeData.dark():ThemeData.light(),
          child: CupertinoAlertDialog(
            title: Text(LocaleKeys.do_confirm.tr()),
            content: Text(question),
            actions: [
              // The "Yes" button
              CupertinoDialogAction(
                  onPressed: () {
                    // Close the dialog
                    Navigator.of(context).pop(true);
                  },
                  child:  Text(LocaleKeys.yes.tr(),style: TextStyle(color: Theme.of(context).primaryColor))),
              CupertinoDialogAction(
                  onPressed: () {
                    // Close the dialog
                    Navigator.of(context).pop(false);
                  },
                  child:  Text(LocaleKeys.no.tr(),style: TextStyle(color: Theme.of(context).primaryColor),))
            ],
          ),
        );
      });
}
