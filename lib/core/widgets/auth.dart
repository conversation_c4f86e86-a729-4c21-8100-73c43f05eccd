import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/services.dart';
import 'package:local_auth/local_auth.dart';
import 'package:govassist/translations/locale_keys.g.dart';

class LocalAuthApi {
  static final _auth = LocalAuthentication();

  static Future<bool> hasBiometrics() async {
    try {
      return await _auth.canCheckBiometrics;
    } on PlatformException {
      return false;
    }
  }

  static Future<List<BiometricType>> getBiometrics() async {
    try {
      return await _auth.getAvailableBiometrics();
    } on PlatformException catch (e) {
      print('Exception on available biometrics: $e');
      return <BiometricType>[];
    }
  }

  static Future<bool> authenticate() async {
    final isAvailable = await hasBiometrics();
    final support = await _auth.isDeviceSupported();

    if (!isAvailable) return false;
    if (!support) return false;
    try {
      return await _auth.authenticate(
          options: AuthenticationOptions(biometricOnly: true),
          localizedReason: LocaleKeys.authenticate_continue.tr());
    } catch (e) {
      return false;
    }
  }
}
