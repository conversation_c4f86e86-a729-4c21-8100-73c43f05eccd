import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_session_manager/flutter_session_manager.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart' hide Trans;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:govassist/core/function/functions.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/core/widgets/custom_toast.dart';
import 'package:govassist/features/lock/functional_lock_page.dart';
import 'package:govassist/translations/locale_keys.g.dart';

abstract class LocationService {
  Future<String> getLatLang();
}

class LocationServiceImpl extends LocationService {
  @override
  Future<String> getLatLang() async {
    LocationPermission permission;
    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.deniedForever) {
        return "0,0";
      }
    }
    final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high);

    return "${position.latitude},${position.longitude}";
  }
}

Future<Position> determinePosition([bool? isBackgroundService]) async {
  bool serviceEnabled;
  LocationPermission permission;
  var sm = SessionManager();
  SharedPreferences prefs = await SharedPreferences.getInstance();
  serviceEnabled = await Geolocator.isLocationServiceEnabled();
  await prefs.reload();

  var isLive = await sm.get(functional_live) ?? false;

  var backService = isBackgroundService ?? false;
  if (!serviceEnabled) {
    prefs.setBool(is_gps_active, false);

    if (!backService) {
      !isLive ? Get.offAll(FunctionalLockPage()) : null;
    }
    if (backService) {
      createNotification(await turnLocationOn(prefs));
    } else {
      CustomToast.showToast(LocaleKeys.turn_on_location.tr());
    }

    return Future.error("LOCATION_OFF");
  }

  permission = await Geolocator.checkPermission();

  if (permission == LocationPermission.denied) {
    permission = await Geolocator.requestPermission();
    if (backService) {
      createNotification(LocaleKeys.give_location_permission.tr());
    } else {
      CustomToast.showToast(LocaleKeys.give_location_permission.tr());
    }

    if (permission == LocationPermission.denied) {
      prefs.setBool(is_gps_active, false);
      if (!backService) {
        !isLive ? Get.offAll(FunctionalLockPage()) : null;
      }

      return Future.error("DENIED");
    }
  }

  if (permission == LocationPermission.deniedForever) {
    prefs.setBool(is_gps_active, false);
    if (!backService) {
      !isLive ? Get.offAll(FunctionalLockPage()) : null;
    }

    if (backService) {
      createNotification(await giveLocationPermission(prefs));
    } else {
      CustomToast.showToast(LocaleKeys.give_location_permission.tr());
    }
    return Future.error("DENIED_FOREVER");
  }

  try {
    var location = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high);

    if (location.isMocked) {
      prefs.setBool(is_not_mocked, false);
      if (!backService) {
        !isLive ? Get.offAll(FunctionalLockPage()) : null;
      }

      if (backService) {
        createNotification(await fraudLocation());
      } else {
        CustomToast.showToast(LocaleKeys.fraud_location.tr());
      }

      return Future.error('MOCKED',
          StackTrace.fromString('${location.latitude},${location.longitude}'));
    } else {
      await prefs.setBool(is_gps_active, true);
      await prefs.setBool(is_not_mocked, true);
      return location;
    }
  } on LocationServiceDisabledException catch (e) {
    return Future.error("LOCATION_OFF");
  } catch (e) {
    return Future.error("LOCATION_OFF");
  }
}

Future<String> turnLocationOn(SharedPreferences prefs) async {
  String? lang = prefs.getString(language_pref);
  if (lang == 'uz') {
    return "Iltimos, ilova tog'ri ishlashi uchun lokatsiyani yoqing!";
  } else if (lang == 'ru') {
    return "Пожалуйста, включите местоположение, чтобы приложение работало правильно!";
  } else {
    return "Ótinish, qosımsha tog'ri islewi ushın lokatsiyani qosıń!";
  }
}

Future<String> giveLocationPermission(SharedPreferences prefs) async {
  String? lang = prefs.getString(language_pref);
  if (lang == 'uz') {
    return "Lokatsiyaga ruxsat bering";
  } else if (lang == 'ru') {
    return "Разрешить местоположение";
  } else {
    return "Lokatsiyaga ruxsat beriń";
  }
}

Future<String> fraudLocation() async {
  SharedPreferences prefs = await SharedPreferences.getInstance();
  String? lang = prefs.getString(language_pref);
  if (lang == 'uz') {
    return "Lokatsiyada aldov aniqlandi!";
  } else if (lang == 'ru') {
    return "Обнаружено мошенничество с местоположением!";
  } else {
    return "Обнаружено мошенничество с местоположением!";
  }
}
