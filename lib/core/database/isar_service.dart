import 'package:govassist/features/tasks/data/model/special_done_task.dart';
import 'package:govassist/features/tasks/data/model/special_new_task.dart';
import 'package:isar/isar.dart';
import 'package:path_provider/path_provider.dart';
import 'package:govassist/back_service/model/tracked_location.dart';
import 'package:govassist/features/auth/data/model/calendar.dart';
import 'package:govassist/features/auth/data/model/user_model.dart';
import 'package:govassist/features/complaint/model/complaint_model.dart';
import 'package:govassist/features/home/<USER>/models/category_model.dart';
import 'package:govassist/features/home/<USER>/models/today_works.dart';
import 'package:govassist/features/sent_page/data/model/sent_model.dart';
import 'package:govassist/features/statistics/data/model/statistics.dart';
import 'package:govassist/features/home/<USER>/models/support_model.dart';
import 'package:govassist/features/send_data/models/not_send_model.dart';
import 'package:govassist/features/task_send/data/model/task_img_model.dart';

const SCHEMES = [
  UserModelSchema,
  TaskImgModelSchema,
  SpecialNewTaskItemSchema,
  SpecialDoneTaskItemSchema,
  SupportModelSchema,
  NotSendModelSchema,
  SentModelSchema,
  CategoryModelSchema,
  TodayWorksSchema,
  StatisticsSchema,
  TrackedLocationSchema,
  CalendarSchema,
  ComplaintModelSchema
];

class IsarService {
  late final Isar isar;

  IsarService._create(this.isar);

  static Future<IsarService> buildIsarService() async {
    final dir = await getApplicationDocumentsDirectory();
    final isar = await Isar.open(
      SCHEMES,
      directory: dir.path,
    );
    print('=== Isar opened!');
    return IsarService._create(isar);
  }
}

Future<Isar> getIsarForSchedule() async {
  var isar = await Isar.getInstance();

  print("=== Before, isar is: ${isar?.path}"); //PRINT 1

  if (isar == null) {
    print("=== Isar is not open, so opening it..");
    final dir = await getApplicationSupportDirectory();
    return isar = await Isar.open(
      SCHEMES,
      directory: dir.path,
    );
  } else {
    return isar;
  }
}
