// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

abstract class  LocaleKeys {
  static const appName = 'appName';
  static const ibrat_far_time = 'ibrat_far_time';
  static const ibrat_app_not_found = 'ibrat_app_not_found';
  static const install_app = 'install_app';
  static const code_not_actual = 'code_not_actual';
  static const unknown_server_error = 'unknown_server_error';
  static const data_not_written = 'data_not_written';
  static const check_internet_connection = 'check_internet_connection';
  static const code_is_wrong = 'code_is_wrong';
  static const reenter_number = 'reenter_number';
  static const this998 = 'this998';
  static const enter_code = 'enter_code';
  static const wait = 'wait';
  static const continue_word = 'continue_word';
  static const entering_less_four = 'entering_less_four';
  static const code = 'code';
  static const in_minutes_come = 'in_minutes_come';
  static const in_second_come = 'in_second_come';
  static const statistics = 'statistics';
  static const payment = 'payment';
  static const support_service = 'support_service';
  static const profile = 'profile';
  static const choose_language = 'choose_language';
  static const anthem_leader = 'anthem_leader';
  static const ibrat_app = 'ibrat_app';
  static const day_night = 'day_night';
  static const exit = 'exit';
  static const version = 'version';
  static const face_not_clarifying = 'face_not_clarifying';
  static const face_added = 'face_added';
  static const face_more_than_one = 'face_more_than_one';
  static const face_control_system = 'face_control_system';
  static const please_refresh_page = 'please_refresh_page';
  static const refresh = 'refresh';
  static const face_instruction = 'face_instruction';
  static const enter_face = 'enter_face';
  static const confirm_face = 'confirm_face';
  static const settings = 'settings';
  static const about = 'about';
  static const picture_uploaded = 'picture_uploaded';
  static const server_error = 'server_error';
  static const leader_not_confirm = 'leader_not_confirm';
  static const uploaded = 'uploaded';
  static const recognised = 'recognised';
  static const liveness = 'liveness';
  static const similarity = 'similarity';
  static const clarifying = 'clarifying';
  static const confirmed = 'confirmed';
  static const face_not_recognised = 'face_not_recognised';
  static const admin_can_delete = 'admin_can_delete';
  static const error = 'error';
  static const error1 = 'error1';
  static const server_form_error = 'server_form_error';
  static const error_download_data = 'error_download_data';
  static const not_work_place = 'not_work_place';
  static const arrive_work_time_receive = 'arrive_work_time_receive';
  static const unknown_server_error_retry = 'unknown_server_error_retry';
  static const unknown_error_retry = 'unknown_error_retry';
  static const switch_on_internet = 'switch_on_internet';
  static const check_work_arrive = 'check_work_arrive';
  static const arrived_work = 'arrived_work';
  static const today_work = 'today_work';
  static const count_work = 'count_work';
  static const works_sector = 'works_sector';
  static const not_have_plan = 'not_have_plan';
  static const works = 'works';
  static const inPlan = 'inPlan';
  static const back = 'back';
  static const data_incorrect = 'data_incorrect';
  static const enter_phone_number = 'enter_phone_number';
  static const phone_number = 'phone_number';
  static const privacy_policy = 'privacy_policy';
  static const agree_requirement = 'agree_requirement';
  static const please_accept_conditions = 'please_accept_conditions';
  static const not_phone_number = 'not_phone_number';
  static const tasks = 'tasks';
  static const send_from_first = 'send_from_first';
  static const deleted = 'deleted';
  static const data_uploading = 'data_uploading';
  static const data_uploaded = 'data_uploaded';
  static const editing = 'editing';
  static const sending = 'sending';
  static const make_payment = 'make_payment';
  static const given = 'given';
  static const given_date = 'given_date';
  static const password_not_match = 'password_not_match';
  static const less_then_four = 'less_then_four';
  static const old_password_wrong = 'old_password_wrong';
  static const set_password = 'set_password';
  static const enter_old_password = 'enter_old_password';
  static const enter_new_password = 'enter_new_password';
  static const confirm_password = 'confirm_password';
  static const return_word = 'return_word';
  static const save = 'save';
  static const tariff = 'tariff';
  static const monthly = 'monthly';
  static const sum = 'sum';
  static const start_time = 'start_time';
  static const finished_time = 'finished_time';
  static const payments = 'payments';
  static const next_payment_sum = 'next_payment_sum';
  static const payment_history = 'payment_history';
  static const som = 'som';
  static const to_month = 'to_month';
  static const payment_system = 'payment_system';
  static const add_payment_in_progress = 'add_payment_in_progress';
  static const outer_browser = 'outer_browser';
  static const inner_browser = 'inner_browser';
  static const choose_browser = 'choose_browser';
  static const payment_impossible = 'payment_impossible';
  static const payment_with_click = 'payment_with_click';
  static const payment_with_payme = 'payment_with_payme';
  static const error_updating_password = 'error_updating_password';
  static const change_pin_code = 'change_pin_code';
  static const enter_four_pin_code = 'enter_four_pin_code';
  static const enter_old_pin_code = 'enter_old_pin_code';
  static const enter_new_pin_code = 'enter_new_pin_code';
  static const re_enter = 're_enter';
  static const unnecessary = 'unnecessary';
  static const configure_face_id = 'configure_face_id';
  static const change_web_password = 'change_web_password';
  static const change_language = 'change_language';
  static const delete_account = 'delete_account';
  static const passwords_not_match = 'passwords_not_match';
  static const error_updateing_password = 'error_updateing_password';
  static const not_written = 'not_written';
  static const deleting = 'deleting';
  static const cancelling = 'cancelling';
  static const cancelling1 = 'cancelling1';
  static const cancel = 'cancel';
  static const take_picture = 'take_picture';
  static const take_video = 'take_video';
  static const video_is_saving = 'video_is_saving';
  static const taken_picture = 'taken_picture';
  static const taken_video = 'taken_video';
  static const sure_to_exit = 'sure_to_exit';
  static const common = 'common';
  static const individual = 'individual';
  static const event_type = 'event_type';
  static const count = 'count';
  static const comment = 'comment';
  static const text = 'text';
  static const web_edit = 'web_edit';
  static const aid_supported = 'aid_supported';
  static const fio = 'fio';
  static const aid_type = 'aid_type';
  static const choose_aid_type = 'choose_aid_type';
  static const sure_to_cancel = 'sure_to_cancel';
  static const data_is_saving = 'data_is_saving';
  static const permission_instruct = 'permission_instruct';
  static const content_empty = 'content_empty';
  static const only_one_video = 'only_one_video';
  static const maximum_video = 'maximum_video';
  static const picture = 'picture';
  static const video = 'video';
  static const need_70_letters = 'need_70_letters';
  static const maximum_letter = 'maximum_letter';
  static const download_error = 'download_error';
  static const enter_necessary_data = 'enter_necessary_data';
  static const new_word = 'new_word';
  static const sent = 'sent';
  static const inEdit = 'inEdit';
  static const performance_indicators = 'performance_indicators';
  static const all_plan_work = 'all_plan_work';
  static const done_work = 'done_work';
  static const need_done_work = 'need_done_work';
  static const percentage_indicator = 'percentage_indicator';
  static const rating = 'rating';
  static const republic_indicator = 'republic_indicator';
  static const province_indicator = 'province_indicator';
  static const city_indicator = 'city_indicator';
  static const support_contact = 'support_contact';
  static const telegram_group = 'telegram_group';
  static const moderator = 'moderator';
  static const email = 'email';
  static const website = 'website';
  static const telephone = 'telephone';
  static const re_load = 're_load';
  static const task_giver = 'task_giver';
  static const doing_task = 'doing_task';
  static const confirm = 'confirm';
  static const done = 'done';
  static const give_permission = 'give_permission';
  static const file_name = 'file_name';
  static const not_exist_not_sent_work = 'not_exist_not_sent_work';
  static const choose = 'choose';
  static const pin_code = 'pin_code';
  static const confirm_pin_code = 'confirm_pin_code';
  static const pin_code_not_match = 'pin_code_not_match';
  static const pin_code_error_retry = 'pin_code_error_retry';
  static const forgot_password = 'forgot_password';
  static const create_pin_code = 'create_pin_code';
  static const pin_code_text = 'pin_code_text';
  static const enter_pin_code = 'enter_pin_code';
  static const confirm_pin_code1 = 'confirm_pin_code1';
  static const error_in_uploading = 'error_in_uploading';
  static const edited = 'edited';
  static const year = 'year';
  static const password_mismatch = 'password_mismatch';
  static const task = 'task';
  static const warning1 = 'warning1';
  static const autoWallDesc = 'autoWallDesc';
  static const sms_confirmation = 'sms_confirmation';
  static const want_exit_profile = 'want_exit_profile';
  static const exit_warning = 'exit_warning';
  static const yes = 'yes';
  static const no = 'no';
  static const open_settings = 'open_settings';
  static const no_user = 'no_user';
  static const login_error = 'login_error';
  static const password = 'password';
  static const authenticate_continue = 'authenticate_continue';
  static const turn_on_location = 'turn_on_location';
  static const give_location_permission = 'give_location_permission';
  static const fraud_location = 'fraud_location';
  static const please_solve_above = 'please_solve_above';
  static const compare_server_time = 'compare_server_time';
  static const check_location_permission = 'check_location_permission';
  static const check_fraud_location = 'check_fraud_location';
  static const notExistSentWork = 'notExistSentWork';
  static const not_exist_not_sent_task = 'not_exist_not_sent_task';
  static const done_task_not_exist = 'done_task_not_exist';
  static const new_task_not_exist = 'new_task_not_exist';
  static const privacy_policy_word = 'privacy_policy_word';
  static const difference_server_time = 'difference_server_time';
  static const minute = 'minute';
  static const lock_page_info = 'lock_page_info';
  static const autostart_instruction = 'autostart_instruction';
  static const autostart_not_exist = 'autostart_not_exist';
  static const give_accurate_location_permission = 'give_accurate_location_permission';
  static const do_confirm = 'do_confirm';
  static const sure_to_delete = 'sure_to_delete';
  static const hasUpdate = 'hasUpdate';
  static const open_app = 'open_app';
  static const main_menu = 'main_menu';
  static const not_send_menu = 'not_send_menu';
  static const sent_menu = 'sent_menu';
  static const digital_library = 'digital_library';
  static const soon_add_feature = 'soon_add_feature';
  static const date_gathering = 'date_gathering';
  static const permission_instruct_app = 'permission_instruct_app';
  static const not_paid = 'not_paid';
  static const user_not_found = 'user_not_found';
  static const already_checked = 'already_checked';
  static const my_area = 'my_area';
  static const work_map = 'work_map';
  static const work_place = 'work_place';
  static const not_work_place_in_base = 'not_work_place_in_base';
  static const send_complaint = 'send_complaint';
  static const new_complaint = 'new_complaint';
  static const not_saw = 'not_saw';
  static const saw = 'saw';
  static const complaint_content = 'complaint_content';
  static const complaint_length = 'complaint_length';
  static const cannot_send_empty_data = 'cannot_send_empty_data';
  static const complaintEmpty = 'complaintEmpty';
  static const empty_data = 'empty_data';
  static const labor_discipline = 'labor_discipline';
  static const date = 'date';
  static const time = 'time';
  static const hour = 'hour';
  static const arrive_work_in_time = 'arrive_work_in_time';
  static const arrive_late = 'arrive_late';
  static const unAttended_causeless = 'unAttended_causeless';
  static const unAttended_cause = 'unAttended_cause';
  static const upload_file = 'upload_file';
  static const select_region = 'select_region';
  static const select_city = 'select_city';
  static const select_gender = 'select_gender';
  static const need_to_fill = 'need_to_fill';
  static const need_fill_phoneNumber = 'need_fill_phoneNumber';
  static const enter_information = 'enter_information';
  static const name = 'name';
  static const surname = 'surname';
  static const father_name = 'father_name';
  static const birth_date = 'birth_date';
  static const region = 'region';
  static const city = 'city';
  static const live_place = 'live_place';
  static const gender = 'gender';
  static const education_level = 'education_level';
  static const speciality = 'speciality';
  static const JSHSHIR = 'JSHSHIR';
  static const un_employed = 'un_employed';
  static const employed = 'employed';
  static const loading_error = 'loading_error';
  static const network_error = 'network_error';
  static const successfully_saved = 'successfully_saved';
  static const error_saving_data = 'error_saving_data';
  static const select_category = 'select_category';
  static const must_fill_this_field = 'must_fill_this_field';
  static const loading = 'loading';
  static const retry = 'retry';
  static const certificate_number = 'certificate_number';
  static const certificate_date = 'certificate_date';
  static const company_name = 'company_name';
  static const position = 'position';
  static const order_number = 'order_number';
  static const orderDate = 'orderDate';
  static const creditPurpose = 'creditPurpose';
  static const creditAmount = 'creditAmount';
  static const ssudaPurpose = 'ssudaPurpose';
  static const ssudaAmount = 'ssudaAmount';
  static const make_employment = 'make_employment';
  static const saving = 'saving';
  static const address = 'address';
  static const detailed = 'detailed';
  static const search = 'search';
  static const employment = 'employment';
  static const jshshir_requirement = 'jshshir_requirement';
  static const employment_status = 'employment_status';

}
