// import 'dart:convert';
//
// import 'package:govassist/back_service/model/tracked_location.dart';
//
// void main() {
//   List<TrackedLocation> list = [
//     TrackedLocation(
//         lat: 40.71,
//         lng: 70.34,
//         dateTime: '2024-02-03 11:27',
//         date: '2024-02-03',
//         offside: true,
//         timeChange: false,
//         locChange: false),
//     TrackedLocation(
//         lat: 40.71,
//         lng: 70.34,
//         dateTime: '2024-02-03 11:40',
//         date: '2024-02-03',
//         offside: false,
//         timeChange: false,
//         locChange: false),
//     TrackedLocation(
//         lat: 40.71,
//         lng: 70.34,
//         dateTime: '2024-02-04 11:54',
//         date: '2024-02-04',
//         offside: false,
//         timeChange: false,
//         locChange: false),
//     TrackedLocation(
//         lat: 40.71,
//         lng: 70.34,
//         dateTime: '2024-02-04 12:08',
//         date: '2024-02-04',
//         offside: false,
//         timeChange: false,
//         locChange: false),
//     TrackedLocation(
//         lat: 40.71,
//         lng: 70.34,
//         dateTime: '2024-02-04 12:15',
//         date: '2024-02-04',
//         offside: false,
//         timeChange: false,
//         locChange: false),
//   ];
//   // print(getFilteredLocation(list));
//   List<Map<String, dynamic>> filteredList = getFilteredLocation(list);
//   List<Map<String, dynamic>> sendList = [];
//   // print(filteredList[0].length);
//   for (int j = 0; j < filteredList.length; j++) {
//     for (int i = 1; i < filteredList[j]['data'].length; i++) {
//       // print(i);
//       DateTime currentDateTime =
//           DateTime.parse(filteredList[j]['data'][i]['date']);
//       DateTime previousDateTime =
//           DateTime.parse(filteredList[j]['data'][i - 1]['date']);
//       int differenceInMinutes =
//           currentDateTime.difference(previousDateTime).inMinutes;
//       if (differenceInMinutes > 15) {
//         filteredList[j]['data'][i]['deviceOff'] = true;
//         filteredList[j]['data'][i - 1]['deviceOff'] = true;
//       } else {
//         filteredList[j]['data'][i]['deviceOff'] = false;
//         filteredList[j]['data'][i - 1]['deviceOff'] = false;
//       }
//     }
//   }
//   String jsonString = json.encode(filteredList);
//   print(jsonString);
//   for (int j = 0; j <filteredList.length; j++) {
//     bool needRemove = true;
//     for (int i = 0; i < filteredList[j]['data'].length; i++) {
//       // print("if: ${filteredList[j]['data'][i]['offside']}");
//       // print("if: ${filteredList[j]['data'][i]['deviceOff']}");
//       // print("if: ${filteredList[j]['data'][i]['timeChange']}");
//       // print("if: ${filteredList[j]['data'][i]['locChange']}");
//      // print("\n\n");
//       if (filteredList[j]['data'][i]['offside'] == true ||
//           filteredList[j]['data'][i]['deviceOff'] == true ||
//           filteredList[j]['data'][i]['timeChange'] == true ||
//           filteredList[j]['data'][i]['locChange'] == true) {
//         // print("ichkarida");
//         needRemove = false;
//         break;
//       }
//     }
//     print(needRemove);
//     if (needRemove == false) {
//       // print("ich : ${j}");
//       sendList.add(filteredList[j]);
//       needRemove = false;
//     }
//     print(needRemove);
//   }
//   // print("\n");
//   String jsonString1 = json.encode(filteredList);
//   // print(jsonString1);
//   // print(sendList);
// }
//
// List<Map<String, dynamic>> getFilteredLocation(List<TrackedLocation?> list) {
//   Map<String, List<Map<String, dynamic>>> filteredData = {};
//   for (var obj in list) {
//     if (!filteredData.containsKey(obj?.date)) {
//       filteredData[obj!.date] = [];
//     }
//
//     filteredData[obj?.date]!.add({
//       "date": obj?.dateTime,
//       "lat": obj?.lat,
//       "lng": obj?.lng,
//       "offside": obj?.offside,
//       "locChange": obj?.locChange,
//       "timeChange": obj?.timeChange
//     });
//   }
//   List<Map<String, dynamic>> result = [];
//
//   filteredData.forEach((date, data) {
//     result.add({"data": data, "date": date});
//   });
//   // print("uzunligi: ${result[0]['data'].length}");
//   return result;
// }
