import 'package:get_storage/get_storage.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:isar/isar.dart';
import 'package:govassist/core/database/isar_service.dart';
import 'package:govassist/di/dependency_injection.dart';
import 'package:govassist/features/auth/data/model/calendar.dart';
import 'package:govassist/features/auth/data/model/user_model.dart';
import 'package:govassist/features/home/<USER>/models/category_model.dart';
import 'package:govassist/features/home/<USER>/models/data_model.dart';
import 'package:govassist/features/home/<USER>/models/support_model.dart';
import 'package:govassist/features/home/<USER>/models/today_works.dart';

abstract class HomeLocalDataSource {
  Future<List<CategoryModel>> getCategory();

  Future<TodayWorks> getTodayWorks();

  Future<int> getSpecialTasksCount();

  Future<bool> setCategory(List<CategoryModel> list);

  Future<bool> setTodayWorks(TodayWorks todayWorks);

  Future<bool> setSpecialTasksCount(int specialTasksCount);

  Future<bool> setSupportTypes(List<SupportModel> supportTypes);

  Future<bool> setDataModel(DataModel dataModel);
}

class HomeLocalDataSourceImpl extends HomeLocalDataSource {
  final IsarService isarService = di();
  final GetStorage gs = di();

  @override
  Future<List<CategoryModel>> getCategory() async {
    try {
      List<CategoryModel> list =
          await isarService.isar.categoryModels.where().findAll();
      return list;
    } catch (e) {
      throw Exception(e);
    }
  }

  @override
  Future<int> getSpecialTasksCount() async {
    try {
      var count = await gs.read(NOTIFICATION_COUNT) ?? 0;
      return count;
    } catch (e) {
      throw Exception(e);
    }
  }

  @override
  Future<TodayWorks> getTodayWorks() async {
    try {
      TodayWorks todayWork =
          await isarService.isar.todayWorks.where().findFirst() ?? TodayWorks();
      return todayWork;
    } catch (e) {
      throw Exception(e);
    }
  }

  @override
  Future<bool> setCategory(List<CategoryModel> list) async {
    try {
      await isarService.isar.writeTxn(() async {
        isarService.isar.categoryModels.where().deleteAll();
      });
      await isarService.isar.writeTxn(() async {
        isarService.isar.categoryModels.putAll(list);
      });
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> setSpecialTasksCount(int specialTasksCount) async {
    try {
      gs.write(NOTIFICATION_COUNT, specialTasksCount);
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> setTodayWorks(TodayWorks todayWorks) async {
    try {
      await isarService.isar.writeTxn(() async {
        isarService.isar.todayWorks.where().deleteAll();
      });
      await isarService.isar.writeTxn(() async {
        isarService.isar.todayWorks.put(todayWorks);
      });
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> setSupportTypes(List<SupportModel> supportTypes) async {
    try {
      await isarService.isar.writeTxn(() async {
        isarService.isar.supportModels.where().deleteAll();
      });
      await isarService.isar.writeTxn(() async {
        isarService.isar.supportModels.putAll(supportTypes);
      });
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> setDataModel(DataModel dataModel) async {
    try {
      await isarService.isar.writeTxn(() async {
        isarService.isar.userModels.where().deleteAll();
      });
      await isarService.isar.writeTxn(() async {
        isarService.isar.userModels.put(dataModel.userModel ?? UserModel());
      });
      await isarService.isar.writeTxn(() async {
        isarService.isar.calendars.where().deleteAll();
      });
      await isarService.isar.writeTxn(() async {
        isarService.isar.calendars.put(dataModel.calendar ?? Calendar());
      });
      return true;
    } catch (e) {
      return false;
    }
  }
}
