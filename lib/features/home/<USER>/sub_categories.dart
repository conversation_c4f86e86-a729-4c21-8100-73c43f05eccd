import 'package:easy_localization/easy_localization.dart';
import 'package:fading_edge_scrollview/fading_edge_scrollview.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart' hide Trans;
import 'package:govassist/core/database/embedded_models.dart';
import 'package:govassist/core/function/functions.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/core/widgets/header_widget.dart';
import 'package:govassist/features/face_control/facedetectionview.dart';
import 'package:govassist/features/send_data/send_page.dart';
import 'package:govassist/generated/assets.dart';
import 'package:govassist/translations/locale_keys.g.dart';
import 'package:zoom_tap_animation/zoom_tap_animation.dart';

import '../transformer/src/transformable_list_item.dart';
import '../transformer/src/transformable_list_view.dart';

class SubCategoriesPage extends StatefulWidget {
  const SubCategoriesPage(
      {super.key,
      required this.subCategories,
      required this.categoryId,
      required this.categoryName});

  final List<SubCategorys> subCategories;
  final String categoryId;
  final String categoryName;

  @override
  State<SubCategoriesPage> createState() => _SubCategoriesPageState();
}

class _SubCategoriesPageState extends State<SubCategoriesPage> {
  final _listController = ScrollController();

  late List<SubCategorys> subCategories;

  @override
  void initState() {
    subCategories = widget.subCategories;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppHeaderWidget(
          title: widget.categoryName,
          onBackTap: () {
            Get.back();
          },
          isBackVisible: true),
      body: Column(
        children: [
          SizedBox(
            height: 10.h,
          ),
          Container(
            height: 60.h,
            margin: EdgeInsets.symmetric(
              horizontal: 16.w,
              vertical: 4.h,
            ),
            decoration: BoxDecoration(
                color: cSecondColor,
                borderRadius: BorderRadius.circular(20.r),
                boxShadow: [
                  BoxShadow(
                    color: cFirstColor.withOpacity(0.1),
                    spreadRadius: 3.r,
                    blurRadius: 15.r,
                    offset: Offset(0, 10.w), // changes position of shadow
                  ),
                ]),
            alignment: Alignment.center,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(LocaleKeys.works.tr(),
                      style: TextStyle(
                        color: cWhiteColor,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                      )),
                  Text(LocaleKeys.inPlan.tr(),
                      style: TextStyle(
                        color: cWhiteColor,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                      )),
                ],
              ),
            ),
          ),
          Expanded(
            child: subCategories.length > 0
                ? FadingEdgeScrollView.fromScrollView(
                    gradientFractionOnEnd: 0,
                    child: TransformableListView.builder(
                      physics: BouncingScrollPhysics(
                          parent: AlwaysScrollableScrollPhysics()),
                      controller: _listController,
                      getTransformMatrix: getTransformMatrix,
                      padding: EdgeInsets.only(bottom: 140.h, top: 10.h),
                      itemBuilder: (context, index) {
                        return ZoomTapAnimation(
                          onTap: () {
                            Get.off(() => FaceRecognitionView(
                                  debug: EMULATOR,
                                  child: SendPage(
                                    categoryId: widget.categoryId,
                                    subCategoryId:
                                        subCategories[index].subCategory?.id ??
                                            '',
                                    subCategoryName: subCategoryTitle(
                                        subCategory:
                                            subCategories[index].subCategory),
                                    withVideo: subCategories[index]
                                            .subCategory
                                            ?.withVideo ??
                                        true,
                                    withMeet: subCategories[index]
                                            .subCategory
                                            ?.meet ??
                                        true,
                                    isWithAid: subCategories[index]
                                            .subCategory
                                            ?.support ??
                                        true,
                                    isWebEdit: subCategories[index]
                                            .subCategory
                                            ?.webEdit ??
                                        true,
                                  ),
                                ));
                          },
                          child: Container(
                            margin: EdgeInsets.symmetric(
                              horizontal: 16.w,
                              vertical: 4.h,
                            ),
                            decoration: BoxDecoration(
                                color: Theme.of(context).cardTheme.color,
                                borderRadius: BorderRadius.circular(20.r),
                                boxShadow: [
                                  BoxShadow(
                                    color: cFirstColor.withOpacity(0.1),
                                    spreadRadius: 3.r,
                                    blurRadius: 15.r,
                                    offset: Offset(
                                        0, 10.w), // changes position of shadow
                                  ),
                                ]),
                            alignment: Alignment.center,
                            child: Padding(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 20.w, vertical: 14.h),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: Text(
                                        subCategoryTitle(
                                            subCategory: subCategories[index]
                                                .subCategory),
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                          fontSize: 16.sp,
                                          fontWeight: FontWeight.w600,
                                        )),
                                  ),
                                  subCategories[index].value != 0
                                      ? Padding(
                                          padding: EdgeInsets.only(left: 15.w),
                                          child: Text(
                                              subCategories[index]
                                                  .value
                                                  .toString(),
                                              overflow: TextOverflow.ellipsis,
                                              style: TextStyle(
                                                fontSize: 16.sp,
                                                fontWeight: FontWeight.w600,
                                              )),
                                        )
                                      : SvgPicture.asset(
                                          Assets.iconsCheckCircle),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                      itemCount: subCategories.length,
                    ),
                  )
                : Container(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          ClipRect(
                            child: Container(
                              height: 300.h,
                              child: Column(
                                children: [
                                  SizedBox(
                                    height: 20.h,
                                  ),
                                  Expanded(
                                      child: Image.asset(
                                    Assets.iconsEmpty,
                                    height: 140.h,
                                  )),
                                  Padding(
                                      padding: EdgeInsets.only(
                                          top: 10.h,
                                          left: 30.w,
                                          right: 30.w,
                                          bottom: 10.h),
                                      child: Text(
                                        LocaleKeys.not_have_plan.tr(),
                                        textAlign: TextAlign.center,
                                        style: TextStyle(color: cGrayColor1),
                                      )),
                                  CupertinoButton(
                                      child: Text(
                                        LocaleKeys.back.tr(),
                                        style: TextStyle(color: cGrayColor1),
                                      ),
                                      color: cGrayColor1.withAlpha(80),
                                      onPressed: () {
                                        Get.back();
                                      }),
                                  SizedBox(
                                    height: 20.h,
                                  )
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
          ),
        ],
      ),
    );
  }
}

Matrix4 getTransformMatrix(TransformableListItem item) {
  /// final scale of child when the animation is completed
  const endScaleBound = 0.9;

  /// 0 when animation completed and [scale] == [endScaleBound]
  /// 1 when animation starts and [scale] == 1
  final animationProgress = item.visibleExtent / item.size.height;

  /// result matrix
  final paintTransform = Matrix4.identity();

  /// animate only if item is on edge
  if (item.position == TransformableListItemPosition.bottomEdge) {
    final scale = endScaleBound + ((1 - endScaleBound) * animationProgress);

    paintTransform
      ..translate(item.size.width / 2)
      ..scale(scale)
      ..translate(-item.size.width / 2);
  }

  return paintTransform;
}
