import 'package:dartz/dartz.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:govassist/core/errors/failures.dart';
import 'package:govassist/core/network/network_info.dart';
import 'package:govassist/core/widgets/custom_toast.dart';
import 'package:govassist/features/home/<USER>/datasources/home_local_datasources.dart';
import 'package:govassist/features/home/<USER>/datasources/home_remote_datasources.dart';
import 'package:govassist/features/home/<USER>/models/category_model.dart';

import 'package:govassist/features/home/<USER>/models/support_model.dart';
import 'package:govassist/features/home/<USER>/models/today_works.dart';
import 'package:govassist/features/home/<USER>/repositories/home_repository.dart';
import 'package:govassist/features/home/<USER>/bloc/category_bloc.dart';
import 'package:govassist/translations/locale_keys.g.dart';

class HomeRepositoryImpl extends HomeRepository {
  final HomeRemoteDatasourceImpl homeRemoteDatasourceImpl;
  final HomeLocalDataSourceImpl homeLocalDatasourceImpl;
  final NetworkInfo networkInfo;

  HomeRepositoryImpl(
      {required this.homeRemoteDatasourceImpl,
      required this.homeLocalDatasourceImpl,
      required this.networkInfo});

  @override
  Future<Either<Failure, dynamic>> getCategory(bool refresh) async {
    if (await networkInfo.isConnected && refresh) {
      try {
        final result = await homeRemoteDatasourceImpl.getHome();
        homeLocalDatasourceImpl.setCategory(result[0] as List<CategoryModel>);
        homeLocalDatasourceImpl.setTodayWorks(result[1] as TodayWorks);
        homeLocalDatasourceImpl.setSpecialTasksCount(result[2] as int);
        homeLocalDatasourceImpl.setSupportTypes(result[3] as List<SupportModel>);
        return Right(HomeSuccessState(
            mainCategoryList: result[0],
            todayWorks: result[1],
            specialTaskCount: result[2]));
      } on NoConnectionFailure catch (e) {
        CustomToast.showToast(LocaleKeys.check_internet_connection.tr());
        return  Left(
            NoConnectionFailure(LocaleKeys.error_download_data.tr()));
      } on ServerFailure catch (e) {
        CustomToast.showToast(LocaleKeys.server_error.tr());
        return  Left(ServerFailure(LocaleKeys.error_download_data.tr()));
      } catch (e) {
        print(e);
        CustomToast.showToast(LocaleKeys.unknown_server_error.tr());
        return  Left(ServerFailure(LocaleKeys.error_download_data.tr()));
      }
    } else {
      try {
        var categories = await homeLocalDatasourceImpl.getCategory();
        var todayWorks = await homeLocalDatasourceImpl.getTodayWorks();
        var specialTasks = await homeLocalDatasourceImpl.getSpecialTasksCount();

        return Right(HomeSuccessState(
            mainCategoryList: categories,
            todayWorks: todayWorks,
            specialTaskCount: specialTasks));
      } on LocalFailure {
        return  Left(ServerFailure(LocaleKeys.error_download_data.tr()));
      }
    }
  }
}
