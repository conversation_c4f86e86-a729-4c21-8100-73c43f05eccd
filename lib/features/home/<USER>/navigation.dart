import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:govassist/core/database/isar_service.dart';
import 'package:govassist/core/widgets/header_widget_main.dart';
import 'package:govassist/core/widgets/rounded_bottom_navigation_bar.dart';
import 'package:govassist/di/dependency_injection.dart';
import 'package:govassist/features/bandlik/presentation/page/bandlik_page.dart';
import 'package:govassist/features/drawer/presentation/page/drawer_page.dart';
import 'package:govassist/features/home/<USER>/home.dart';
import 'package:govassist/features/not_send_page/presentation/pages/not_send_page.dart';
import 'package:govassist/features/sent_page/presentation/page/sent_page.dart';
import 'package:govassist/features/tasks/presentation/pages/task_main_page.dart';


class BottomNavigationPage extends StatefulWidget {
  final int? pageIndex;
  final int? tabIndex;

  const BottomNavigationPage({Key? key, this.pageIndex, this.tabIndex})
      : super(key: key);

  @override
  State<BottomNavigationPage> createState() => _BottomNavigationPageState();
}

class _BottomNavigationPageState extends State<BottomNavigationPage> {
  final GlobalKey<ScaffoldState> _navigatorKey = GlobalKey(); //// Create a key
  int _selectedIndex = 0;
  int _tabIndex = 0;
  final IsarService isarService = di();
  int notSendCount = 0;
  late List<Widget> _widgetOptions;

  refresh() {
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    if (widget.pageIndex != null) {
      _selectedIndex = widget.pageIndex ?? 0;
    }
    _widgetOptions = <Widget>[
      HomePage.screen(),
      NotSendPage(
        tabIndex: widget.tabIndex,
      ),
      SentPage.screen(),
      BandlikPage.screen(navigatorKey: _navigatorKey)
    ];
  }

  @override
  Widget build(BuildContext context) {
    print("=== bottom rebuild");

    return Scaffold(
      key: _navigatorKey,
      extendBody: _selectedIndex != 1 ? true : false,
      appBar: _selectedIndex == 3
          ? null
          : AppHeaderWidgetMain(onHamburgerTap: () {
              _navigatorKey.currentState!.openDrawer();
            }, onActionTap: () {
              Get.to(TaskMainPage());
            }),
      drawer: DrawerPage(),
      body: Center(
        child: _widgetOptions.elementAt(_selectedIndex),
      ),
      bottomNavigationBar: Container(
        child: RoundedBottomNavigationBar(
          onSelected: (int index) {
            setState(() => _selectedIndex = index);
          },
          pageIndex: _selectedIndex,
        ),
      ),
    );
  }
}
