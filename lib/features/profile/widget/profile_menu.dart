import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../core/utils/app_constants.dart';

class ProfileMenuButton extends StatelessWidget {
  final String title;
  final String icon;
  final Color? iconColor;
  final Color? titleColor;
  final VoidCallback onTap;

  const ProfileMenuButton(
      {super.key,
      required this.title,
      required this.icon,
      this.iconColor = cFirstColor,
      this.titleColor,
      required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 56.h,
      decoration: BoxDecoration(
          boxShadow: [
            boxShadow20,
          ],
          color: Theme.of(context).cardTheme.color,
          borderRadius: BorderRadius.circular(20.r)),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20.r),
        child: Material(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(20.r)),
          color: Theme.of(context).cardTheme.color,
          child: InkWell(
            onTap: onTap,
            child: Row(
              children: [
                SizedBox(
                  width: 16.w,
                ),
                SvgPicture.asset(
                  icon,
                  width: 28.w,
                  height: 28.w,
                  color: iconColor,
                ),
                SizedBox(
                  width: 10.w,
                ),
                Text(
                  title,
                  style: TextStyle(
                      fontSize: 16.sp,
                      color: titleColor ??
                          Theme.of(context).textTheme.bodyLarge?.color,
                      fontWeight: FontWeight.w500),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
