import 'dart:ui';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/core/widgets/custom_toast.dart';
import 'package:govassist/di/dependency_injection.dart';
import 'package:govassist/features/profile/presentation/bloc/web_password/web_password_cubit.dart';
import 'package:govassist/generated/assets.dart';
import 'package:govassist/translations/locale_keys.g.dart';

class WebPasswordChangePage extends StatefulWidget {
  static Widget screen() {
    return BlocProvider(
      create: (context) => di<WebPasswordCubit>(),
      child: WebPasswordChangePage(),
    );
  }

  const WebPasswordChangePage({super.key});

  @override
  State<WebPasswordChangePage> createState() => _WebPasswordChangePageState();
}

class _WebPasswordChangePageState extends State<WebPasswordChangePage> {
  bool isVisible1 = true;
  bool isVisible2 = true;
  TextEditingController password = TextEditingController();
  TextEditingController repeat = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
        child: Container(
          padding: EdgeInsets.only(
            right: 20.w,
            left: 20.w,
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                  topRight: Radius.circular(20.r),
                  topLeft: Radius.circular(20.r)),
              color: Theme.of(context).cardTheme.color),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 8.h,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SvgPicture.asset(Assets.iconsSheetPull),
                ],
              ),
              SizedBox(
                height: 20.h,
              ),
              Text(
                LocaleKeys.change_web_password.tr(),
                style: TextStyle(
                    fontSize: 20.sp,
                    color: Theme.of(context).textTheme.bodyLarge?.color,
                    fontWeight: FontWeight.bold),
              ),
              SizedBox(
                height: 30.h,
              ),
              Text(
                LocaleKeys.enter_new_password.tr(),
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.normal,
                  color: Theme.of(context).textTheme.bodyMedium?.color,
                ),
              ),
              SizedBox(
                height: 4.h,
              ),
              TextField(
                controller: password,
                obscureText: isVisible1,
                decoration: InputDecoration(
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(30.r),
                      borderSide: BorderSide(
                        color: Theme.of(context).textTheme.bodySmall!.color!,
                        width: 1,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(30.r),
                      borderSide: BorderSide(
                        color: Theme.of(context).textTheme.bodySmall!.color!,
                        width: 1,
                      ),
                    ),
                    prefixIcon: Container(
                      padding: EdgeInsets.all(10.w),
                      child: SvgPicture.asset(Assets.iconsLock2,
                          colorFilter: ColorFilter.mode(
                              Theme.of(context).textTheme.bodyMedium!.color!,
                              BlendMode.srcIn,),width: 20.w,),
                    ),
                    suffixIcon: InkWell(
                        onTap: () {
                          setState(() {
                            isVisible1 = !isVisible1;
                          });
                        },
                        child: Padding(
                          padding: EdgeInsets.only(right: 10.w),
                          child: Icon(
                            isVisible1 ? Icons.visibility_off : Icons.visibility,
                            color: Theme.of(context).textTheme.bodyMedium!.color!,
                            size: 20.w,
                          ),
                        ))),
                style: TextStyle(
                  color: Theme.of(context).textTheme.bodySmall?.color,
                ),
              ),
              SizedBox(height: 14.h,),
              Text(
                LocaleKeys.re_enter.tr(),
                style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.normal,
                    color: Theme.of(context).textTheme.bodyMedium?.color),
              ),
              SizedBox(
                height: 4.h,
              ),
              TextField(
                controller: repeat,
                obscureText: isVisible2,
                decoration: InputDecoration(
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(30.r),
                      borderSide: BorderSide(
                        color: Theme.of(context).textTheme.bodySmall!.color!,
                        width: 1,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(30.r),
                      borderSide: BorderSide(
                        color: Theme.of(context).textTheme.bodySmall!.color!,
                        width: 1,
                      ),
                    ),
                    prefixIcon: Container(
                      padding: EdgeInsets.all(10.w),
                      child: SvgPicture.asset(Assets.iconsLock2,
                          colorFilter: ColorFilter.mode(
                              Theme.of(context).textTheme.bodyMedium!.color!,
                              BlendMode.srcIn),width: 20.w,),
                    ),
                    suffixIcon: InkWell(
                        onTap: () {
                          setState(() {
                            isVisible2 = !isVisible2;
                          });
                        },
                        child: Padding(
                          padding: EdgeInsets.only(right: 10.w),
                          child: Icon(
                              isVisible2
                                  ? Icons.visibility_off
                                  : Icons.visibility,
                              color: Theme.of(context)
                                  .textTheme
                                  .bodyMedium!
                                  .color!,size: 20.w,),
                        ))),
                style: TextStyle(
                  color: Theme.of(context).textTheme.bodySmall?.color,
                ),
              ),
              SizedBox(
                height: 20.h,
              ),
              BlocConsumer<WebPasswordCubit, WebPasswordState>(
                listener: (context, state) {
                  if (state.status == WebPasswordStatus.success) {
                    CustomToast.showToast(state.message ?? "");
                    Navigator.pop(context);
                  } else if (state.status == WebPasswordStatus.failure) {
                    CustomToast.showToast(state.message ?? "");
                    Navigator.pop(context);
                  }
                },
                builder: (context, state) {
                  return MaterialButton(
                      height: 56.h,
                      minWidth: MediaQuery.of(context).size.width,
                      onPressed: () {
                        if (password.text == repeat.text) {
                          BlocProvider.of<WebPasswordCubit>(context)
                              .changeWebPassword(password.text, repeat.text);
                        } else {
                          setState(() {
                            password.clear();
                            repeat.clear();
                            CustomToast.showToast(
                                LocaleKeys.password_mismatch.tr());
                          });
                        }
                      },
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30.r),
                      ),
                      color: cFirstColor,
                      child: _button(state));
                },
              ),
              SizedBox(
                height: 8.h,
              ),
              MaterialButton(
                height: 56.h,
                minWidth: MediaQuery.of(context).size.width,
                onPressed: () {
                  Navigator.pop(context);
                },
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(30.r),
                    side: BorderSide(
                        width: 1,
                        color: Theme.of(context).textTheme.bodySmall!.color!)),
                child: Text(
                  LocaleKeys.unnecessary.tr(),
                  style: TextStyle(
                      color: Theme.of(context).textTheme.bodySmall!.color!,
                      fontSize: 18.sp),
                ),
              ),
              SizedBox(
                height: 10.h,
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _button(state) {
    if (state.status == WebPasswordStatus.loading) {
      return const CupertinoActivityIndicator(color: cWhiteColor);
    } else {
      return Text(
        LocaleKeys.save.tr(),
        style: TextStyle(fontSize: 18.sp, color: cWhiteColor),
      );
    }
  }
}
