import 'dart:ui';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart' hide Trans;
import 'package:isar/isar.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:govassist/core/database/isar_service.dart';
import 'package:govassist/core/function/functions.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/core/widgets/language_dialog.dart';
import 'package:govassist/core/widgets/logout.dart';
import 'package:govassist/di/dependency_injection.dart';
import 'package:govassist/features/face_control/bio_lock_page.dart';
import 'package:govassist/features/profile/presentation/pages/pin_code_change_page.dart';
import 'package:govassist/features/profile/presentation/pages/web_password_change_page.dart';
import 'package:govassist/features/profile/widget/profile_menu.dart';
import 'package:govassist/generated/assets.dart';
import 'package:govassist/features/auth/data/model/user_model.dart';
import 'package:govassist/translations/locale_keys.g.dart';

class ProfilePage extends StatefulWidget {
  ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  final maskFormatter = MaskTextInputFormatter(mask: '+998 ## ###-##-##');
  final IsarService isarService = di();

  UserModel? userModel;

  @override
  void initState() {
    super.initState();
    getUser();
  }

  getUser() async {
    userModel = await isarService.isar.userModels.where().findFirst();
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height,
        child: Stack(
          alignment: Alignment.topCenter,
          children: [
            Container(
              height: 200.h,
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: isDark()
                        ? [cCardDarkColor, cCardDarkColor]
                        : [
                            cSecondColor,
                            cFirstColor,
                          ],
                  ),
                  borderRadius: BorderRadius.only(
                      bottomRight: Radius.circular(20.r),
                      bottomLeft: Radius.circular(20.r))),
            ),
            SvgPicture.asset(
              Assets.iconsWebProfile,
              width: MediaQuery.of(context).size.width,
            ),
            Positioned(
              top: 60.h,
              child: Text(
                LocaleKeys.profile.tr(),
                style: TextStyle(
                    fontSize: 20.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.white),
              ),
            ),
            Positioned(
                top: 60.h,
                left: 10.w,
                child: ClipOval(
                  child: Material(
                    color: Colors.transparent,
                    child: IconButton(
                      iconSize: 45.h,
                      onPressed: () {
                        Get.back();
                      },
                      icon: SvgPicture.asset(
                        Assets.iconsBackArrowCircle,
                        height: 45.h,
                      ),
                    ),
                  ),
                )),
            Positioned(
              top: 140.h,
              child: CachedNetworkImage(
                imageUrl: userModel?.avatar ??
                    "https://ui-avatars.com/api/?name=${fullName(userModel)}&background=3A40D3&color=fff",
                placeholder: (context, url) => Container(
                  width: 100.w,
                  height: 100.w,
                  decoration: BoxDecoration(
                    color: cGrayColor0,
                    border: Border.all(width: 2.w, color: cWhiteColor),
                    borderRadius: BorderRadius.all(Radius.circular(20.r)),
                  ),
                  child:
                      SvgPicture.asset(Assets.iconsProfile, color: cWhiteColor),
                ),
                errorWidget: (context, url, error) => Container(
                  width: 100.w,
                  height: 100.w,
                  decoration: BoxDecoration(
                    color: cGrayColor0,
                    border: Border.all(
                      width: 2.w,
                      color: cWhiteColor,
                    ),
                    borderRadius: BorderRadius.all(Radius.circular(20.r)),
                  ),
                  child:
                      SvgPicture.asset(Assets.iconsProfile, color: cWhiteColor),
                ),
                fit: BoxFit.cover,
                imageBuilder: (context, imageProvider) {
                  return Container(
                    width: 100.w,
                    height: 100.w,
                    decoration: BoxDecoration(
                        border: Border.all(width: 2.w, color: cWhiteColor),
                        borderRadius: BorderRadius.all(Radius.circular(20.r)),
                        image: DecorationImage(
                            image: imageProvider, fit: BoxFit.cover)),
                  );
                },
              ),
            ),
            Positioned(
                top: 280.h,
                left: 0,
                right: 0,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: Column(
                    children: [
                      Text(
                        fullName(userModel),
                        style: TextStyle(
                            fontSize: 20.sp,
                            color:
                                Theme.of(context).textTheme.bodyLarge?.color),
                      ),
                      Text(
                        maskFormatter.maskText(
                          userModel?.phone != null
                              ? maskFormatter
                                  .maskText(userModel?.phone ?? "0000000")
                              : "loading",
                        ),
                        style: TextStyle(
                            fontSize: 14.sp,
                            color:
                                Theme.of(context).textTheme.bodyLarge?.color),
                      ),
                      SizedBox(
                        height: 20.h,
                      ),
                      ProfileMenuButton(
                        title: LocaleKeys.configure_face_id.tr(),
                        icon: Assets.iconsFaceId,
                        iconColor: null,
                        onTap: () {
                          Get.to(() => BioLockPage());
                        },
                      ),
                      SizedBox(
                        height: 10.h,
                      ),
                      ProfileMenuButton(
                        title: LocaleKeys.change_pin_code.tr(),
                        icon: Assets.iconsLock,
                        onTap: () {
                          showModalBottomSheet(
                              context: context,
                              isScrollControlled: true,
                              backgroundColor: Colors.transparent,
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.only(
                                      topRight: Radius.circular(20.r),
                                      topLeft: Radius.circular(20.r))),
                              builder: (_) {
                                return PinCodeChangePage.screen();
                              });
                        },
                      ),
                      SizedBox(
                        height: 10.h,
                      ),
                      ProfileMenuButton(
                        title: LocaleKeys.change_web_password.tr(),
                        icon: Assets.iconsLock,
                        onTap: () {
                          showModalBottomSheet(
                              context: context,
                              isScrollControlled: true,
                              backgroundColor: Colors.transparent,
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.only(
                                      topRight: Radius.circular(20.r),
                                      topLeft: Radius.circular(20.r))),
                              builder: (_) {
                                return WebPasswordChangePage.screen();
                              });
                        },
                      ),
                      SizedBox(
                        height: 10.h,
                      ),
                      ProfileMenuButton(
                        title: LocaleKeys.change_language.tr(),
                        icon: Assets.iconsGlobal,
                        onTap: () {
                          showDialog(
                              context: context,
                              builder: (_) {
                                return BackdropFilter(
                                    filter: ImageFilter.blur(
                                        sigmaX: 5.0, sigmaY: 5.0),
                                    child: LanguageDialog(
                                      onSelectCallBack: () async {
                                        Navigator.pop(context);
                                      },
                                    ));
                              });
                        },
                      ),
                      SizedBox(
                        height: 10.h,
                      ),
                      ProfileMenuButton(
                        title: LocaleKeys.autostart_instruction.tr(),
                        icon: Assets.iconsInfoCircle,
                        iconColor: cRedColor,
                        onTap: () {
                          autoStartWall(context, true);
                        },
                      ),
                      SizedBox(
                        height: 10.h,
                      ),
                      ProfileMenuButton(
                        title: LocaleKeys.delete_account.tr(),
                        icon: Assets.iconsDelete,
                        iconColor: cRedColor,
                        onTap: () {
                          showDialog(
                              context: context,
                              builder: (_) {
                                return LogOutDialog();
                              });
                        },
                      ),
                    ],
                  ),
                )),
          ],
        ),
      ),
    );
  }
}
