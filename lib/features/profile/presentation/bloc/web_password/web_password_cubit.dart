import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:govassist/core/network/network_info.dart';
import 'package:govassist/core/utils/api_path.dart';
import 'package:govassist/translations/locale_keys.g.dart';

part 'web_password_state.dart';

class WebPasswordCubit extends Cubit<WebPasswordState> {
  final Dio dio;
  final NetworkInfo networkInfo;
  final SharedPreferences prefs;

  WebPasswordCubit(
      {required this.dio, required this.networkInfo, required this.prefs})
      : super(WebPasswordState(status: WebPasswordStatus.initial));

  void changeWebPassword(String password, String repeat) async {
    if (await networkInfo.isConnected) {
      try {
        emit(state.copyWith(status: WebPasswordStatus.loading));
        var response = await dio.put(changeWebPath + "${prefs.get("id")}",
            data: {"password": password, "repeat": repeat});
        if (response.statusCode == 200) {
          emit(state.copyWith(
              status: WebPasswordStatus.success,
              message: response.data['message']));
        }
      } catch (e) {
        emit(state.copyWith(
            status: WebPasswordStatus.failure,
            message: LocaleKeys.error_updating_password.tr()));
      }
    } else {
      emit(state.copyWith(
          status: WebPasswordStatus.failure, message: LocaleKeys.check_internet_connection.tr()));
    }
  }
}
