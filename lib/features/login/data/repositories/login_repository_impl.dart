import 'package:dartz/dartz.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:govassist/core/errors/failures.dart';
import 'package:govassist/core/network/network_info.dart';
import 'package:govassist/features/login/data/datasources/login_local_datasources.dart';
import 'package:govassist/features/login/data/datasources/login_remote_datasources.dart';
import 'package:govassist/translations/locale_keys.g.dart';

abstract class LoginRepository {
  Future<Either<Failure, dynamic>> sendLogin(
      String tel, String? password, String macAddress);
}

class LoginRepositoryImpl extends LoginRepository {
  final LoginRemoteDatasourceImpl loginRemoteDatasource;
  final LoginLocalDataSourceImpl loginLocalDatasource;
  final NetworkInfo networkInfo;

  LoginRepositoryImpl(
      {required this.loginRemoteDatasource,
      required this.loginLocalDatasource,
      required this.networkInfo});

  @override
  Future<Either<Failure, dynamic>> sendLogin(
      String tel, String? password, String macAddress) async {
    if (await networkInfo.isConnected) {
      try {
        final result =
            await loginRemoteDatasource.setData(tel, password, macAddress);
        if (result == "0" ||
            result == "1" ||
            result == "2" ||
            result == "3" ||
            result == "4") {
          return Right(result);
        } else {
          try {
            final resultLocal = await loginLocalDatasource.setDataLocal(result);
            return Right(resultLocal);
          } on LocalFailure {
            return Left(LocalFailure(LocaleKeys.data_incorrect.tr()));
          }
        }
      } on FormatException catch (e) {
        return Left(ServerFailure(e.toString()));
      } on ServerFailure catch (e) {
        return Left(ServerFailure(LocaleKeys.error_download_data.tr()));
      } catch (e) {
        return Left(ServerFailure(e.toString()));
      }
    } else {
      return Left(
          NoConnectionFailure(LocaleKeys.check_internet_connection.tr()));
    }
  }
}
