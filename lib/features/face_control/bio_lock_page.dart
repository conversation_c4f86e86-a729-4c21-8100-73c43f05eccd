// ignore_for_file: depend_on_referenced_packages

import 'package:camera/camera.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math';
import 'package:facesdk_plugin/facesdk_plugin.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart' hide Trans;
import 'package:isar/isar.dart';
import 'package:path/path.dart' hide Context;
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sqflite/sqflite.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:govassist/core/database/isar_service.dart';
import 'package:govassist/core/function/functions.dart';
import 'package:govassist/core/photo/image_picker_utils.dart';
import 'package:govassist/core/widgets/custom_toast.dart';
import 'package:govassist/core/widgets/logout.dart';
import 'package:govassist/features/auth/data/model/user_model.dart';
import 'package:govassist/core/network/network_info.dart';
import 'package:govassist/core/utils/api_path.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/di/dependency_injection.dart';
import 'package:govassist/generated/assets.dart';
import 'package:govassist/translations/locale_keys.g.dart';
import 'package:zoom_tap_animation/zoom_tap_animation.dart';
import 'dart:io' show File, Platform;
import '../auth/data/model/calendar.dart';
import 'about.dart';
import 'settings.dart';
import 'person.dart';
import 'personview.dart';
import 'facedetectionview.dart';

// ignore: must_be_immutable

///TODO: Change DB to faster one / Optimize here
class BioLockPage extends StatefulWidget {
  BioLockPage({super.key});

  @override
  BioLockPageState createState() => BioLockPageState();
}

class BioLockPageState extends State<BioLockPage> {
  String _warningState = "";
  bool _visibleWarning = false;

  bool hasAnyUser = false;
  bool serverApproved = false;
  bool localApproved = false;
  bool _shouldRequestPermission = false;
  late AndroidDeviceInfo? androidInfo;
  late Future<dynamic> _initFuture;
  late final AppLifecycleListener _listener;
  final NetworkInfo networkInfo = di();
  SharedPreferences prefs = di();
  Dio dio = di();
  var personList = <Person>[];
  final ValueNotifier<bool> _listLoading = ValueNotifier<bool>(false);

  final _faceSdkPlugin = FacesdkPlugin();

  Future<Map<Permission, PermissionStatus>> requestPermissions() async {
    var verSDK = androidInfo?.version.sdkInt ?? 0;
    Map<Permission, PermissionStatus> statuses = await [
      Platform.isAndroid && verSDK < 33
          ? Permission.storage
          : Permission.photos,
      Permission.camera,
      Permission.location,
      Permission.notification
    ].request();

    return statuses;
  }

  Future<void> permissionWall() async {
    Map<Permission, PermissionStatus> statuses = {};
    statuses = await requestPermissions();
    var verSDK = androidInfo?.version.sdkInt ?? 0;

    if (statuses[Permission.camera] != PermissionStatus.granted ||
        statuses[Permission.location] != PermissionStatus.granted ||
        statuses[Permission.notification] != PermissionStatus.granted ||
        statuses[Platform.isAndroid && verSDK < 33
                ? Permission.storage
                : Permission.photos] !=
            PermissionStatus.granted) {
      if (statuses[Permission.camera] == PermissionStatus.permanentlyDenied ||
          statuses[Permission.location] == PermissionStatus.permanentlyDenied ||
          statuses[Permission.notification] ==
              PermissionStatus.permanentlyDenied ||
          statuses[Platform.isAndroid && verSDK < 33
                  ? Permission.storage
                  : Permission.photos] ==
              PermissionStatus.permanentlyDenied) {
        print(statuses[Permission.camera]);
        print(statuses[Permission.storage]);
        print(statuses[Permission.location]);
        print(statuses[Permission.notification]);

        ///Shows custom dialog after user refuses for giving of any permissions
        showCustomDialog(this.context);
      } else {
        ///Points to the recursion
        await permissionWall();
      }
    } else {
      await initBioLock();
    }
  }

  @override
  void initState() {
    if (Platform.isAndroid) {
      androidInfo = di();
    } else {
      androidInfo = null;
    }

    _listener = AppLifecycleListener(
      onStateChange: _onStateChanged,
    );

    reInitializerButton();

    super.initState();
  }

  @override
  void dispose() {
    _listener.dispose();
    super.dispose();
  }

  // Listen to the app lifecycle state changes
  void _onStateChanged(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.detached:
        _onDetached();
        break;
      case AppLifecycleState.resumed:
        _onResumed();
        break;
      case AppLifecycleState.inactive:
        _onInactive();
        break;
      case AppLifecycleState.hidden:
        _onHidden();
        break;
      case AppLifecycleState.paused:
        _onPaused();
        break;
    }
  }

  void _onDetached() => print('detached');

  void _onResumed() {
    if (_shouldRequestPermission) {
      reInitializerButton();
      print('===== OnResumed');
      _shouldRequestPermission = false;
    }
  }

  void _onInactive() => print('inactive');

  void _onHidden() => print('hidden');

  void _onPaused() {
    print('===== OnPaused');
    _shouldRequestPermission = true;
  }

  initialQueries() async {
    var personList = await loadAllPersons();
    var count = personList.length;
    String? imageUrl;
    UserModel user;
    Calendar calendar;
    String userId = prefs.getString('id') ?? '';

    if (await networkInfo.isConnected) {
      try {
        final responseUser = await dio.get(usersPath + userId);
        var data = responseUser.data;

        if (responseUser.statusCode == 200) {
          if (data != null) {
            user = UserModel.fromJson(data['user']);
            calendar = Calendar.fromJson(data['calendar']);
            saveDataModel(user, calendar, di());
            imageUrl = user.avatar;

            print('User image: $imageUrl');
            print('User model: ${user.toJson()}');
          }
        }
      } on DioException catch (e) {
        ///These should be set in every internet connection
        prefs.setBool(server_approved, false);
        prefs.setBool(local_approved, false);
        print(e);
        CustomToast.showToast('JSON is malformed: $e');
      } catch (e) {
        ///These should be set in every internet connection
        prefs.setBool(server_approved, false);
        prefs.setBool(local_approved, false);
        print(e);
        CustomToast.showToast('JSON is malformed: $e');
      }
    }

    if (count == 0) {
      ///

      print('DB is zero');

      if (await networkInfo.isConnected) {
        try {
          if (imageUrl != null) {
            setState(() {
              print('Loading: true');
              _listLoading.value = true;
              serverApproved = true;
              prefs.setBool(server_approved, serverApproved);
            });

            var directory = await getApplicationDocumentsDirectory();
            var now = DateTime.now();
            var time = (DateFormat('yyyy-MM-dd_HH-mm-ss').format(now));
            var filePath =
                '${directory.path}${"${imageUrl.split("/").last.split('.').first}_${time}.${imageUrl.split("/").last.split('.').last}"}';

            var responseImage = await dio.download(imageUrl, filePath);

            // final responseImage = await dio.get(imageUrl);
            // var imageData = responseImage.data;

            if (responseImage.statusCode == 200) {
              var imageFile = XFile(filePath);

              if (count == 0) {
                await enrollPerson(
                    personImage: imageFile,
                    personId: userId,
                    isFromCamera: false);
              }

              this.personList = await loadAllPersons();
              count = this.personList.length;

              if (count == 1) {
                setState(() {
                  hasAnyUser = true;
                });
              }
            }

            setState(() {
              print('Loading: false');
              _listLoading.value = false;
            });
          } else {
            setState(() {
              serverApproved = false;
              _listLoading.value = false;
              prefs.setBool(server_approved, serverApproved);
            });
          }
        } on DioException catch (e) {
          setState(() {
            print('Loading: false');
            _listLoading.value = false;
          });
          print(e);
        } catch (e, s) {
          // CustomToast.showToast(e.toString() + s.toString());
          setState(() {
            print('Loading: false');
            _listLoading.value = false;
          });
          print(e);
        }
      }
    } else if (count == 1) {
      setState(() {
        hasAnyUser = true;
      });

      ///This synchronizes data (re-check)
      if (await networkInfo.isConnected) {
        if (imageUrl != null) {
          setState(() {
            serverApproved = true;
            prefs.setBool(server_approved, serverApproved);
          });
        } else {
          setState(() {
            serverApproved = false;
            prefs.setBool(server_approved, serverApproved);
          });
        }
      }
    }
  }

  reInitializerButton() {
    setState(() {
      _initFuture = permissionWall();
    });
  }

  Future<void> initBioLock() async {
    int facePluginState = -1;
    String warningState = "";
    bool visibleWarning = false;

    try {
      if (Platform.isAndroid) {
        await _faceSdkPlugin
            .setActivation(AppStrings.androidFaceToken)
            .then((value) => facePluginState = value ?? -1);
      } else {
        await _faceSdkPlugin
            .setActivation(AppStrings.iOSFaceToken)
            .then((value) => facePluginState = value ?? -1);
      }

      if (facePluginState == 0) {
        await _faceSdkPlugin
            .init()
            .then((value) => facePluginState = value ?? -1);
      }
    } catch (e) {}

    List<Person> personList = await loadAllPersons();
    await SettingsPageState.initSettings();

    int? livenessLevel = prefs.getInt("liveness_level");

    try {
      await _faceSdkPlugin
          .setParam({'check_liveness_level': livenessLevel ?? 0});
    } catch (e) {}

    // If the widget was removed from the tree while the asynchronous platform
    // message was in flight, we want to discard the reply rather than calling
    // setState to update our non-existent appearance.
    if (!mounted) return;

    if (facePluginState == -1) {
      warningState = "Invalid license!";
      visibleWarning = true;
    } else if (facePluginState == -2) {
      warningState = "License expired!";
      visibleWarning = true;
    } else if (facePluginState == -3) {
      warningState = "Invalid license!";
      visibleWarning = true;
    } else if (facePluginState == -4) {
      warningState = "No activated!";
      visibleWarning = true;
    } else if (facePluginState == -5) {
      warningState = "Init error!";
      visibleWarning = true;
    }

    setState(() {
      _warningState = warningState;
      _visibleWarning = visibleWarning;
      this.personList = personList;
    });

    await initialQueries();
  }

  Future<void> insertPerson(Person person, String cachePath) async {
    // Get a reference to the database.
    final db = await createDB();

    // Insert the Dog into the correct table. You might also specify the
    // `conflictAlgorithm` to use in case the same dog is inserted twice.
    //
    // In this case, replace any previous data.
    await db.insert(
      'person',
      person.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );

    deleteFileFromInternalStorage(cachePath);

    setState(() {
      this.personList.add(person);
    });
  }

  Future<void> deleteAllPerson() async {
    final db = await createDB();
    await db.delete('person');

    setState(() {
      this.personList.clear();
    });

    Fluttertoast.showToast(
        msg: "All person deleted!",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.red,
        textColor: Colors.white,
        fontSize: 16.0);
  }

  Future<void> deletePerson(index) async {
    // ignore: invalid_use_of_protected_member

    final db = await createDB();
    await db.delete('person',
        where: 'name=?', whereArgs: [this.personList[index].name]);

    // ignore: invalid_use_of_protected_member
    setState(() {
      this.personList.removeAt(index);
    });

    prefs.remove(local_approved);
    prefs.remove(server_approved);

    Fluttertoast.showToast(
        msg: "Person removed!",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.red,
        textColor: Colors.white,
        fontSize: 16.0);
  }

  Future<int?> getPersonTableSize() async {
    final db = await createDB();

    // Get the size of the 'person' table
    List<Map<String, dynamic>> result =
        await db.rawQuery('SELECT COUNT(*) FROM person');
    int? size = Sqflite.firstIntValue(result);

    // Close the database
    await db.close();
    return size;
  }

  Future enrollPerson(
      {dynamic personImage,
      String? personId,
      required bool isFromCamera}) async {
    var verSDK = androidInfo?.version.sdkInt ?? 0;

    Map<Permission, PermissionStatus> statuses = await [
      Platform.isAndroid && verSDK < 33
          ? Permission.storage
          : Permission.photos,
      Permission.camera,
      Permission.location,
      Permission.notification,
    ].request();

    if (statuses[Platform.isAndroid && verSDK < 33
                ? Permission.storage
                : Permission.photos] ==
            PermissionStatus.granted &&
        statuses[Permission.camera] == PermissionStatus.granted &&
        statuses[Permission.notification] == PermissionStatus.granted &&
        statuses[Permission.location] == PermissionStatus.granted) {
      try {
        var image;
        if (personImage == null) {
          final picker = di<ImagePickerUtils>();

          ///Custom camera
          image = File(await picker.selectImageFromCamera(
              Get.key.currentContext!,

              ///TODO: Add translate
              previewMessage:
                  "Iltimos, olingan rasmni tekshiring, zarur bo'lsa yo'nalishni to'g'irlang. Yuzingiz tik turishi kerak!",
              isVideo: false,
              isHD: true,
              isFront: true));

          ///Native camera
          // image = await ImagePicker().pickImage(
          //     source: isFromCamera ? ImageSource.camera : ImageSource.gallery);
        } else {
          image = personImage;
        }

        if (image == null) return;

        final faces = await _faceSdkPlugin.extractFaces(image.path);

        if (faces.length == 1) {
          num randomNumber =
              10000 + Random().nextInt(10000); // from 0 upto 99 included
          Person person = Person(
              name: personId == null ? 'Person-[$randomNumber]' : personId,
              faceJpg: faces[0]['faceJpg'],
              templates: faces[0]['templates']);
          insertPerson(person, image.path);
        } else {
          deleteFileFromInternalStorage(image.path);
        }

        ///For many faces
        // for (var face in faces) {
        //   num randomNumber =
        //       10000 + Random().nextInt(10000); // from 0 up to 99 included
        //   Person person = Person(
        //       name: 'Person' + randomNumber.toString(),
        //       faceJpg: face['faceJpg'],
        //       templates: face['templates']);
        //   insertPerson(person);
        // }

        if (faces.length == 0) {
          Fluttertoast.showToast(
              msg: LocaleKeys.face_not_clarifying.tr(),
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.BOTTOM,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.red,
              textColor: Colors.white,
              fontSize: 16.0);
        } else if (faces.length == 1) {
          Fluttertoast.showToast(
              msg: LocaleKeys.face_added.tr(),
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.BOTTOM,
              timeInSecForIosWeb: 1,
              backgroundColor: cFirstColor,
              textColor: Colors.white,
              fontSize: 16.0);
        } else {
          Fluttertoast.showToast(
              msg: LocaleKeys.face_more_than_one.tr(),
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.BOTTOM,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.red,
              textColor: Colors.white,
              fontSize: 16.0);
        }
      } catch (e, s) {
        print(e);
        CustomToast.showToast(e.toString() + s.toString());
      }
    } else {
      //CustomToast.showToast("Permission issue!");
      permissionWall();
    }
  }

  @override
  Widget build(BuildContext context) {
    ///These should be checked in every build!
    serverApproved = prefs.getBool(server_approved) ?? false;
    localApproved = prefs.getBool(local_approved) ?? false;

    if (personList.isNotEmpty) {
      hasAnyUser = true;
    } else {
      hasAnyUser = false;
    }

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).primaryColorDark,
        title: Text(LocaleKeys.face_control_system.tr(),
            style: TextStyle(fontSize: context.isTablet ? 12.sp : 16.sp,color: cWhiteColor)),
        toolbarHeight: 70.h,
        centerTitle: true,
        leadingWidth: 40.w,
        iconTheme: IconThemeData(size: 22.h, color: cWhiteColor),
        actions: [
          Padding(
            padding: EdgeInsets.only(right: context.isTablet ? 10.w : 0),
            child: IconButton(
                onPressed: () {
                  showDialog(
                      context: context,
                      builder: (_) {
                        return LogOutDialog();
                      });
                },
                icon: Icon(Icons.logout, size: 22.h)),
          ),
        ],
      ),
      body: FutureBuilder<dynamic>(
          future: _initFuture,
          builder: (context, snapshot) {
            switch (snapshot.connectionState) {
              case ConnectionState.waiting:
                {
                  // Otherwise, display a loading indicator.
                  return Center(
                      child: CupertinoActivityIndicator(
                    color: Theme.of(context).primaryColor,
                    radius: 30.r,
                  ));
                }
              default:
                if (snapshot.hasError) {
                  print('Error: ${snapshot.connectionState}');
                  return Container(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            height: 300.h,
                            child: Column(
                              children: [
                                SizedBox(
                                  height: 20.h,
                                ),
                                Expanded(
                                    child: SvgPicture.asset(
                                  Assets.iconsWarning,
                                  height: 140.h,
                                )),
                                Padding(
                                    padding: EdgeInsets.only(
                                        top: 10.h,
                                        left: 30.w,
                                        right: 30.w,
                                        bottom: 10.h),
                                    child: Text(
                                      LocaleKeys.please_refresh_page.tr(),
                                      textAlign: TextAlign.center,
                                      style: TextStyle(color: cGrayColor1),
                                    )),
                                CupertinoButton(
                                    child: Text(
                                      LocaleKeys.refresh.tr(),
                                      style: TextStyle(color: cGrayColor1),
                                    ),
                                    color: cGrayColor1.withAlpha(80),
                                    onPressed: () {
                                      reInitializerButton();
                                    }),
                                SizedBox(
                                  height: 20.h,
                                )
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                } else {
                  return Container(
                    margin: EdgeInsets.only(left: 16.w, right: 16.w),
                    child: Column(
                      children: <Widget>[
                        SizedBox(height: 20.h),

                        Card(
                            color: Theme.of(context).cardTheme.color,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(15.r),
                            ),
                            child: Padding(
                              padding: EdgeInsets.symmetric(
                                  vertical: 8.h, horizontal: 10.w),
                              child: ListTile(
                                leading: Icon(Icons.tips_and_updates,
                                    color: Theme.of(context).iconTheme.color,
                                    size: 22.h),
                                subtitle: Text(
                                  LocaleKeys.face_instruction.tr(),
                                  // 'Server approved:  - ${serverApproved} -\n'
                                  // 'Local approved:  - ${localApproved} -',
                                  style: TextStyle(
                                      fontSize: context.isPhone ? 13.sp : 9.sp),
                                ),
                              ),
                            )),
                        SizedBox(height: 20.h),
                        ZoomTapAnimation(
                            onTap: () {
                              ///Refresh page if something happens
                              reInitializerButton();
                              CustomToast.showToast('😊');
                            },
                            child: SvgPicture.asset(Assets.iconsFaceIdBig,
                                    height: 150.h)
                                .animate()
                                .fadeIn(
                                    duration: 1000
                                        .ms) // uses `Animate.defaultDuration`
                                .scale(
                                    curve: Curves.easeInSine,
                                    duration: 500.ms,
                                    begin: Offset(0.9, 0.9),
                                    end: Offset(
                                        1, 1)) // inherits duration from fadeIn
                                .shimmer(
                                    delay: 1000.ms,
                                    duration: 1500.ms,
                                    color: cSecondColor)),
                        SizedBox(
                          height: 20.h,
                        ),
                        Row(
                          children: <Widget>[
                            Visibility(
                              visible: !hasAnyUser,
                              child: Expanded(
                                flex: 1,
                                child: ElevatedButton.icon(
                                    label: Text(LocaleKeys.enter_face.tr(),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                            fontSize: context.isTablet
                                                ? 12.sp
                                                : 16.sp)),
                                    icon: Padding(
                                      padding: EdgeInsets.only(left: 5.w),
                                      child: ValueListenableBuilder<bool>(
                                          valueListenable: _listLoading,
                                          builder: (ctx, value, _) {
                                            if (!value) {
                                              return Icon(Icons.person_add,
                                                  size: 22.h);
                                            } else {
                                              return Center(
                                                child:
                                                    CupertinoActivityIndicator(
                                                        color: Theme.of(context)
                                                            .primaryColor),
                                              );
                                            }
                                          }),
                                    ),
                                    style: ElevatedButton.styleFrom(
                                        padding: EdgeInsets.only(
                                            top: 10.h, bottom: 10.h),
                                        // foregroundColor: Colors.white70,
                                        backgroundColor: cFirstColor,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.all(
                                              Radius.circular(12.r)),
                                        )),
                                    onPressed: !_listLoading.value
                                        ? () => enrollPerson(isFromCamera: true)
                                        : null),
                              ),
                            ),
                            Visibility(
                                visible: !localApproved && !hasAnyUser,
                                child: SizedBox(width: 20.w)),
                            Visibility(
                              visible: !localApproved,
                              child: Expanded(
                                flex: 1,
                                child: ElevatedButton.icon(
                                    label: Text(LocaleKeys.confirm_face.tr(),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                            fontSize: context.isTablet
                                                ? 12.sp
                                                : 16.sp,color: cWhiteColor)),
                                    icon: Padding(
                                      padding: EdgeInsets.only(left: 5.w),
                                      child:
                                          Icon(Icons.person_search, size: 22.h,color: cWhiteColor,),
                                    ),
                                    style: ElevatedButton.styleFrom(
                                        padding: EdgeInsets.only(
                                            top: 10.h, bottom: 10.h),
                                        backgroundColor: cFirstColor,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.all(
                                              Radius.circular(12.r)),
                                        )),
                                    onPressed: () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                            builder: (context) =>
                                                FaceRecognitionView(
                                                  personList: this.personList,
                                                  server_approved:
                                                      serverApproved,
                                                )),
                                      ).then((value) => reInitializerButton());
                                    }),
                              ),
                            ),
                          ],
                        ),
                        ///For debugging
                        Visibility(
                          visible: false,
                          child: Padding(
                            padding: EdgeInsets.only(top: 6.h),
                            child: Row(
                              children: <Widget>[
                                Expanded(
                                  flex: 1,
                                  child: ElevatedButton.icon(
                                      label: Text(LocaleKeys.settings.tr(), style: TextStyle(color: cWhiteColor),),
                                      icon: Icon(Icons.settings, size: 22.h,color: cWhiteColor,),
                                      style: ElevatedButton.styleFrom(
                                          padding: EdgeInsets.only(
                                              top: 10.h, bottom: 10.h),
                                          backgroundColor: cFirstColor,
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.all(
                                                Radius.circular(12.r)),
                                          )),
                                      onPressed: () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                              builder: (context) =>
                                                  SettingsPage(
                                                    homePageState: this,
                                                  )),
                                        );
                                      }),
                                ),
                                SizedBox(width: 20.w),
                                Expanded(
                                  flex: 1,
                                  child: ElevatedButton.icon(
                                      label: Text(LocaleKeys.about.tr(),style: TextStyle(color: cWhiteColor),),
                                      icon: Icon(Icons.info, size: 22.h,color: cWhiteColor,),
                                      style: ElevatedButton.styleFrom(
                                          padding: EdgeInsets.only(
                                              top: 10.h, bottom: 10.h),
                                          backgroundColor: cFirstColor,
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.all(
                                                Radius.circular(12.r)),
                                          )),
                                      onPressed: () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                              builder: (context) =>
                                                  const AboutPage()),
                                        );
                                      }),
                                ),
                              ],
                            ),
                          ),
                        ),
                        SizedBox(height: 8.h),

                        Expanded(
                            child: Stack(
                          children: [
                            ValueListenableBuilder<bool>(
                                valueListenable: _listLoading,
                                builder: (ctx, value, _) {
                                  if (!value) {
                                    return PersonView(
                                      personList: this.personList,
                                      homePageState: this,
                                      isServerApproved: serverApproved,
                                      isLocalApproved: localApproved,
                                    );
                                  } else {
                                    return Center(
                                      child: CupertinoActivityIndicator(
                                          color: cFirstColor, radius: 30.r),
                                    );
                                  }
                                }),
                            Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: <Widget>[
                                Visibility(
                                    visible: _visibleWarning,
                                    child: Container(
                                      width: double.infinity,
                                      height: 40.h,
                                      color: Colors.redAccent,
                                      child: Center(
                                        child: Text(
                                          _warningState,
                                          textAlign: TextAlign.center,
                                          style: TextStyle(fontSize: 20.sp),
                                        ),
                                      ),
                                    ))
                              ],
                            )
                          ],
                        )),
                        SizedBox(
                          height: 4.h,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Flexible(
                              child: SvgPicture.asset(
                                Assets.iconsCompanyLogo,
                                color: Theme.of(context).iconTheme.color,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 20.h),
                      ],
                    ),
                  );
                }
            }
          }),
    );
  }

  void saveDataModel(
      UserModel userModel, Calendar calendar, IsarService isarService) async {
    try {
      await isarService.isar.writeTxn(() async {
        isarService.isar.userModels.where().deleteAll();
      });
      await isarService.isar.writeTxn(() async {
        isarService.isar.userModels.put(userModel);
      });

      await isarService.isar.writeTxn(() async {
        isarService.isar.calendars.where().deleteAll();
      });
      await isarService.isar.writeTxn(() async {
        isarService.isar.calendars.put(calendar);
      });
    } catch (e) {
      CustomToast.showToast("Error saving user, malformat!");
      print("Can't save data model to DB");
    }
  }
}

Future<Database> createDB() async {
  final database = openDatabase(
    // Set the path to the database. Note: Using the `join` function from the
    // `path` package is best practice to ensure the path is correctly
    // constructed for each platform.
    join(await getDatabasesPath(), 'person.db'),
    // When the database is first created, create a table to store dogs.
    onCreate: (db, version) {
      // Run the CREATE TABLE statement on the database.
      return db.execute(
        'CREATE TABLE person(name text, faceJpg blob, templates blob)',
      );
    },
    // Set the version. This executes the onCreate function and provides a
    // path to perform database upgrades and downgrades.
    version: 1,
  );

  return database;
}

// A method that retrieves all the dogs from the dogs table.
Future<List<Person>> loadAllPersons() async {
  // Get a reference to the database.
  final db = await createDB();

  // Query the table for all The Dogs.
  final List<Map<String, dynamic>> maps = await db.query('person', limit: 1);

  // Convert the List<Map<String, dynamic> into a List<Dog>.
  return List.generate(maps.length, (i) {
    return Person.fromMap(maps[i]);
  });
}
