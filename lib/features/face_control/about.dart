import 'package:flutter/material.dart';
import 'package:govassist/core/utils/app_constants.dart';

class AboutPage extends StatelessWidget {
  const AboutPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: cFourthColor,
      appBar: AppBar(
        backgroundColor: cFirstColor,
        title: const Text('Premium Soft'),
        toolbarHeight: 70,
        centerTitle: true,
      ),
      body: Container(
          margin: const EdgeInsets.only(left: 16.0, right: 16.0),
          child: const Column(children: [
            SizedBox(height: 10),
            Text(
                "Hi! I'm developer. If you have trouble with Face control, contact me!", textAlign: TextAlign.center,),
            Sized<PERSON><PERSON>(height: 4),
            <PERSON>(
              children: [
                Icon(
                  Icons.email,
                  color: Colors.black,
                  weight: 24,
                ),
                Sized<PERSON>ox(width: 4),
                Text('Email: <EMAIL>')
              ],
            ),
            <PERSON>zed<PERSON><PERSON>(height: 4),
            Row(
              children: [
                Icon(
                  Icons.phone,
                  color: Colors.black,
                  weight: 24,
                ),
                SizedBox(width: 4),
                Text('Tel: +998911283725')
              ],
            ),
            <PERSON><PERSON><PERSON><PERSON>(height: 4),
            <PERSON>(
              children: [
                Icon(
                  Icons.telegram,
                  color: Colors.black,
                  weight: 24,
                ),
                SizedBox(width: 4),
                Text('Telegram: @flutterblogs')
              ],
            ),
            SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.code,
                  color: Colors.black,
                  weight: 24,
                ),
                SizedBox(width: 4),
                Text('Github: https://github.com/mamasodikov')
              ],
            ),
          ])),
    );
  }
}
