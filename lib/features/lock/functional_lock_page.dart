import 'dart:ui';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_session_manager/flutter_session_manager.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart' hide Trans;
import 'package:ntp/ntp.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:govassist/core/function/functions.dart';
import 'package:govassist/core/location/location_service.dart';
import 'package:govassist/core/network/network_info.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/core/widgets/custom_toast.dart';
import 'package:govassist/di/dependency_injection.dart';
import 'package:govassist/generated/assets.dart';
import 'package:govassist/translations/locale_keys.g.dart';

import 'lock_switcher.dart';

///TODO: Use cached image for background / Future stuck loading (why)
class FunctionalLockPage extends StatefulWidget {
  const FunctionalLockPage({super.key});

  @override
  State<FunctionalLockPage> createState() => _FunctionalLockPageState();
}

class _FunctionalLockPageState extends State<FunctionalLockPage> {
  SharedPreferences prefs = di();
  final NetworkInfo networkInfo = di();
  var sm = SessionManager();
  late Future<void> _initFuture;
  late Future<void> _initFutureTime;

  late final AppLifecycleListener _listener;
  bool _shouldRequestPermission = false;

  @override
  void initState() {
    _listener = AppLifecycleListener(
      onStateChange: _onStateChanged,
    );
    getLive();
    setLive(true);

    reInitializerButton();
    reInitializerButtonTime();
    super.initState();
  }

  setLive(bool live) async {
    await sm.set(functional_live, live);
  }

  getLive() async {
    print('IS FUNCTIONAL LIVE: ${await sm.get(functional_live)}');
  }

  reInitializerButton() {
    setState(() {
      _initFuture = permissionWall(context);
    });
  }

  reInitializerButtonTime() {
    setState(() {
      _initFutureTime = getServerTime();
    });
  }

  @override
  void dispose() {
    setLive(false);
    _listener.dispose();
    super.dispose();
  }

  void _onDetached() => print('detached');

  void _onResumed() async {
    if (_shouldRequestPermission) {
      _shouldRequestPermission = false;
      setLive(true);
    }
  }

  void _onInactive() {
    setLive(true);
    _shouldRequestPermission = true;
  }

  void _onHidden() => setLive(true);

  void _onPaused() {
    setLive(true);
    _shouldRequestPermission = true;
  }

  // Listen to the app lifecycle state changes
  void _onStateChanged(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.detached:
        _onDetached();
        break;
      case AppLifecycleState.resumed:
        _onResumed();
        break;
      case AppLifecycleState.inactive:
        _onInactive();
        break;
      case AppLifecycleState.hidden:
        _onHidden();
        break;
      case AppLifecycleState.paused:
        _onPaused();
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    var time = prefs.getBool(is_time_correct) ?? false;
    var location = prefs.getBool(is_gps_active) ?? false;
    var mockLocation = prefs.getBool(is_not_mocked) ?? false;

    print('REBUILD ----- PAGE');

    print('Time approved: $time\n');
    print('GPS approved: $location\n');
    print('MOCK approved: $mockLocation\n');

    var unlocked = (location && mockLocation && time);

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: Padding(
        padding: EdgeInsets.only(bottom: 50.h),
        child: SizedBox(
          height: 60.h,
          width: 250.h,
          child: FloatingActionButton.extended(
            backgroundColor: unlocked ? cGreenColor : cRedColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(14.r),
            ),
            label: Row(
              children: [
                SizedBox(
                  width: 10.w,
                ),
                Image.asset(
                  Assets.imagesIcon,
                  scale: context.isTablet ? 15 : 20,
                ),
                SizedBox(
                  width: 5.w,
                ),
                SizedBox(
                  width: 120.w,
                  child: Text(
                    LocaleKeys.open_app.tr(),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                    style: TextStyle(color: cWhiteColor, fontSize: 15.sp),
                  ),
                ),
                SizedBox(
                  width: 10.w,
                ),
              ],
            ),
            onPressed: () {
              if (unlocked) {
                Get.offAll(() => LockProvider());
              } else {
                CustomToast.showToast(LocaleKeys.please_solve_above.tr());
              }
            },
          ),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
            image: DecorationImage(
          opacity: 0.1,
          image: AssetImage(Assets.imagesLockPattern),
          fit: BoxFit.cover,
        )),
        child: Stack(
          alignment: Alignment.center,
          children: [
            Align(
                alignment: Alignment.topRight,
                child: Padding(
                  padding: EdgeInsets.only(
                      right: context.isTablet ? 30.w : 8.w, top: 40.h),
                  child: IconButton(
                    padding: EdgeInsets.zero,
                    onPressed: () {
                      showDialog(
                          context: context,
                          builder: (_) {
                            return BackdropFilter(
                                filter:
                                    ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
                                child: Dialog(
                                  backgroundColor:
                                      Theme.of(context).cardTheme.color,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(15.r),
                                  ),
                                  child: Padding(
                                    padding: EdgeInsets.all(20.h),
                                    child: Text(
                                        "${LocaleKeys.lock_page_info.tr()}\n\n$SUPPORT_TEL1\n$SUPPORT_TEL2\n$TELEGRAM"),
                                  ),
                                ));
                          });
                    },
                    icon: Icon(
                      Icons.info,
                      size: context.isTablet ? 40.h : 35.h,
                      color: Theme.of(context).primaryColor.withOpacity(0.7) ??
                          cBlackColor,
                    ),
                  ),
                )),
            Column(
              children: [
                SizedBox(height: 100.h),
                Expanded(
                    flex: 3,
                    child: SvgPicture.asset(
                      Assets.iconsBustedLock,
                      height: 250.h,
                    )),
                Expanded(
                  flex: 4,
                  child: ListView(
                    physics: BouncingScrollPhysics(
                        parent: AlwaysScrollableScrollPhysics()),
                    children: [
                      FutureBuilder(
                          future: _initFutureTime,
                          builder: (context, snapshot) {
                            switch (snapshot.connectionState) {
                              case ConnectionState.waiting:
                                {
                                  // Otherwise, display a loading indicator.
                                  return functionButton(
                                      Icons.lock_clock,
                                      'TIME',
                                      LocaleKeys.compare_server_time.tr(),
                                      time,
                                      context,
                                      true);
                                }
                              default:
                                return functionButton(
                                    Icons.lock_clock,
                                    'TIME',
                                    LocaleKeys.compare_server_time.tr(),
                                    time,
                                    context);
                            }
                          }),
                      FutureBuilder(
                          future: _initFuture,
                          builder: (context, snapshot) {
                            switch (snapshot.connectionState) {
                              case ConnectionState.waiting:
                                {
                                  // Otherwise, display a loading indicator.
                                  return functionButton(
                                      Icons.gps_fixed,
                                      'LOCATION',
                                      LocaleKeys.check_location_permission.tr(),
                                      location,
                                      context,
                                      true);
                                }
                              default:
                                return functionButton(
                                    Icons.gps_fixed,
                                    'LOCATION',
                                    LocaleKeys.check_location_permission.tr(),
                                    location,
                                    context);
                            }
                          }),
                      FutureBuilder(
                          future: _initFuture,
                          builder: (context, snapshot) {
                            switch (snapshot.connectionState) {
                              case ConnectionState.waiting:
                                {
                                  // Otherwise, display a loading indicator.
                                  return functionButton(
                                      Icons.wrong_location_sharp,
                                      'MOCK',
                                      LocaleKeys.check_fraud_location.tr(),
                                      mockLocation,
                                      context,
                                      true);
                                }
                              default:
                                return functionButton(
                                    Icons.wrong_location_sharp,
                                    'MOCK',
                                    LocaleKeys.check_fraud_location.tr(),
                                    mockLocation,
                                    context);
                            }
                          })
                    ],
                  ),
                ),
                Spacer(
                  flex: 2,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget functionButton(IconData icon, String functionCode, String functionName,
      bool isGranted, BuildContext context,
      [isLoading = false]) {
    return InkWell(
      onTap: () {
        checkFunction(functionCode, context);
      },
      child: Container(
        width: double.infinity,
        height: 75.h,
        margin: EdgeInsets.only(left: 25.w, right: 25.w, bottom: 10.h),
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 4.h),
        decoration: ShapeDecoration(
          color: Theme.of(context).primaryColor.withAlpha(200),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(cRadius10.r),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Expanded(
              child: Icon(
                icon,
                color: cWhiteColor,
                size: 25.h,
              ),
            ),
            Expanded(
              flex: 6,
              child: Padding(
                padding: EdgeInsets.all(8.h),
                child: Text(
                  functionName,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                  style: TextStyle(color: cWhiteColor, fontSize: 14.sp),
                ),
              ),
            ),
            Expanded(
              child: CircleAvatar(
                radius: 30.r,
                backgroundColor: isLoading
                    ? cGreenColor
                    : isGranted
                        ? cGreenColor
                        : cRedColor,
                child: isLoading
                    ? Center(
                        child: CupertinoActivityIndicator(
                        color: cWhiteColor,
                        radius: 8.r,
                      ))
                    : Icon(
                        isGranted ? Icons.gpp_good : Icons.gpp_bad,
                        color: cWhiteColor,
                        size: 22.h,
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<Map<Permission, PermissionStatus>> requestPermissions() async {
    Map<Permission, PermissionStatus> statuses = await [
      Permission.location,
    ].request();

    return statuses;
  }

  Future<void> permissionWall(BuildContext context) async {
    Map<Permission, PermissionStatus> statuses = {};
    statuses = await requestPermissions();

    if (statuses[Permission.location] != PermissionStatus.granted) {
      if (statuses[Permission.location] == PermissionStatus.permanentlyDenied) {
        print(statuses[Permission.location]);

        ///Shows custom dialog after user refuses for giving of any permissions
        showCustomDialog(context);
      } else {
        ///Points to the recursion
        await permissionWall(context);
      }
    } else {
      try {
        if (await networkInfo.isConnected) {
          await determinePosition();
          prefs.reload().then((value) => setState(() {}));
        } else {
          CustomToast.showToast(LocaleKeys.check_internet_connection.tr());
        }
      } catch (e) {
        print(e);
      }
    }
  }

  void checkFunction(String langCode, BuildContext context) async {
    switch (langCode) {
      case "TIME":
        {
          CustomToast.showToast("${LocaleKeys.wait.tr()}...");
          reInitializerButtonTime();
        }
        break;

      case "LOCATION":
        {
          CustomToast.showToast("${LocaleKeys.wait.tr()}...");
          reInitializerButton();
        }

        break;

      case "MOCK":
        {
          CustomToast.showToast("${LocaleKeys.wait.tr()}...");
          reInitializerButton();
        }

        break;

      default:
        // Handle the default case or leave it empty if not needed
        break;
    }
  }

  ///TODO: Write this function to app.class (startup) to prevent hacking time
  getServerTime() async {

    ///TODO: Use forEach if necessary
    var _lookUpAddres = 'time.google.com';

    if (await networkInfo.isConnected) {
      DateTime _myTime;
      DateTime _ntpTime;

      /// Or you could get NTP current (It will call DateTime.now() and add NTP offset to it)
      _myTime = DateTime.now();

      /// Or get NTP offset (in milliseconds) and add it yourself
      final int offset = await NTP.getNtpOffset(
          localTime: _myTime, lookUpAddress: _lookUpAddres, timeout: Duration(seconds: 5));

      _ntpTime = _myTime.add(Duration(milliseconds: offset));
      var difference = _myTime.difference(_ntpTime).inMinutes.abs();

      print('\n==== $_lookUpAddres ====');
      print('My time: $_myTime');
      print('NTP time: $_ntpTime');
      print('Difference: $difference minute');

      if (difference < ALLOWED_MINUTE) {
        setState(() {
          prefs.setBool(is_time_correct, true);
        });
      } else {
        setState(() {
          prefs.setBool(is_time_correct, false);
        });

        CustomToast.showToast(
            "${LocaleKeys.difference_server_time.tr()}: $difference ${LocaleKeys.minute.tr()}");
      }

      return;
    } else {
      CustomToast.showToast(LocaleKeys.check_internet_connection.tr());
    }
  }
}
