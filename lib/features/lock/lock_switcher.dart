import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/di/dependency_injection.dart';
import 'package:govassist/features/face_control/bio_lock_page.dart';
import 'package:govassist/features/lock/functional_lock_page.dart';
import 'package:govassist/features/lock/presentation/pages/lock_page.dart';

// ignore: must_be_immutable
class LockProvider extends StatelessWidget {
  final SharedPreferences prefs = di();

  var emulator = EMULATOR;

  @override
  Widget build(BuildContext context) {
    var isDemo = prefs.getBool(is_demo) ?? false;
    bool isServerApproved = prefs.getBool(server_approved) ?? false;
    bool isLocalApproved = prefs.getBool(local_approved) ?? false;

    bool isTimeCorrect = prefs.getBool(is_time_correct) ?? false;
    bool isGpsOn = prefs.getBool(is_gps_active) ?? false;
    bool isNotMocked = prefs.getBool(is_not_mocked) ?? false;

    print('Server approved: $isServerApproved\n');
    print('Local approved: $isLocalApproved\n');
    print('Time approved: $isTimeCorrect\n');
    print('GPS approved: $isGpsOn\n');
    print('MOCK approved: $isNotMocked\n');

    if (isDemo) {
      emulator = true;
    }

    return (!emulator ? isServerApproved && isLocalApproved : true)
        ? isTimeCorrect && isGpsOn && isNotMocked
            ? PincodeScreen.screen()
            : FunctionalLockPage()
        : BioLockPage();
  }
}
