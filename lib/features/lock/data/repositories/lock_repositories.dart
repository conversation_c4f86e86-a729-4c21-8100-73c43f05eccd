import 'package:dartz/dartz.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:govassist/core/errors/failures.dart';
import 'package:govassist/features/lock/data/datasources/lock_local_datasources.dart';
import 'package:govassist/features/lock/domain/repositories/lock_repositories.dart';
import 'package:govassist/translations/locale_keys.g.dart';

class PinRepositoryImpl implements PassRepository {
  final PinLocalDataSourceImpl passLocalDataSource;

  PinRepositoryImpl({required this.passLocalDataSource});

  @override
  Future<Either<Failure, bool>> setCompile(String pass, bool isNotSet) async {
    try {
      final result = await passLocalDataSource.setCompile(pass, isNotSet);
      return Right(result);
    } on LocalFailure {
      return  Left(ServerFailure(LocaleKeys.error1.tr()));
    }
  }
}
