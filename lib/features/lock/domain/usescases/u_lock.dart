import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:govassist/core/errors/failures.dart';
import 'package:govassist/core/usescases/usecase.dart';
import 'package:govassist/features/lock/domain/repositories/lock_repositories.dart';

class UPin extends UseCase<bool, PasswordParams> {
  final PassRepository repository;

  UPin({required this.repository});

  @override
  Future<Either<Failure, bool>> call(PasswordParams params) {
    return repository.setCompile(params.oldPassword, params.isNotSet);
  }
}

class PasswordParams extends Equatable {
  final String oldPassword;
  final bool isNotSet;

  const PasswordParams(this.oldPassword, this.isNotSet);

  @override
  List<Object?> get props => [oldPassword, isNotSet];
}
