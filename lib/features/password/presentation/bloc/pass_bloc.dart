import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/translations/locale_keys.g.dart';

import '../../../../core/widgets/custom_toast.dart';

part 'pass_event.dart';

part 'pass_state.dart';

class PassBloc extends Bloc<PassEvent, PassState> {
  final SharedPreferences sharedPreferences;

  PassBloc({required this.sharedPreferences}) : super(PassInitial()) {
    on<IsSharedPinEmpty>((event, emit) async {
      (sharedPreferences.get(pin_code) == null)
          ? emit(PassIsEmptyState(isEmpty: true))
          : emit(PassIsEmptyState(isEmpty: false));
    });
    on<OnPressedEvent>((event, emit) {
      if (sharedPreferences.get(pin_code) != null) {
        _sharedIsNotEmpty(event, emit);
      } else {
        _sharedIsEmpty(event, emit);
      }
    });
  }

  Future saveShared({required TextEditingController pinPutController}) async {
    sharedPreferences.setString('pin_code', pinPutController.text);
    debugPrint('SHARED ${sharedPreferences.get('pin_code')}');
  }

  _sharedIsEmpty(OnPressedEvent event, Emitter<PassState> emit) {
    if (event.confirmPassword.text.length == 4 &&
        event.newPassword.text.length == 4) {
      print("CONFIRM PASSWORD:${event.confirmPassword.text.length}");
      print("NEW PASSWORD:${event.newPassword.text.length}");

      if (event.newPassword.text == event.confirmPassword.text) {
        saveShared(pinPutController: event.confirmPassword);
        emit(PassSuccessState());
      } else {
        event.confirmPassword.clear();
        emit(PassConfirmFailureState(
            message: LocaleKeys.password_not_match.tr()));
      }
    } else {
      CustomToast.showToast(LocaleKeys.less_then_four.tr());
    }
  }

  _sharedIsNotEmpty(OnPressedEvent event, Emitter<PassState> emit) {
    if (sharedPreferences.get('pin_code') == event.oldPassword.text) {
      if (event.confirmPassword.text.length == 4 &&
          event.newPassword.text.length == 4) {
        print("CONFIRM PASSWORD:${event.confirmPassword.text.length}");
        print("NEW PASSWORD:${event.newPassword.text.length}");

        if (event.newPassword.text == event.confirmPassword.text) {
          emit(PassSuccessState());
          saveShared(pinPutController: event.confirmPassword);
        } else {
          event.confirmPassword.clear();
          emit(PassConfirmFailureState(message: LocaleKeys.password_not_match.tr()));
        }
      } else {
        emit(PassConfirmFailureState(
            message: LocaleKeys.less_then_four.tr()));
      }
    } else if (event.oldPassword.text.isNotEmpty) {
      event.oldPassword.clear();
      emit(PassConfirmFailureState(message: LocaleKeys.old_password_wrong.tr()));
    } else {
      emit(PassConfirmFailureState(
          message: LocaleKeys.less_then_four.tr()));
    }
  }
}
