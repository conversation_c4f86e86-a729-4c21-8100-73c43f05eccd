import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:govassist/translations/locale_keys.g.dart';

import '../../../../core/utils/app_constants.dart';
import '../../../../core/widgets/custom_toast.dart';
import '../../../../core/widgets/dialog_frame.dart';
import '../../../../di/dependency_injection.dart';
import '../bloc/pass_bloc.dart';
import '../widgets/password_text_field_widget.dart';

///============= EDIT HERE (LOGIC FROM MM-BAZA)

class PasswordEditDialog extends StatefulWidget {
  const PasswordEditDialog({Key? key}) : super(key: key);

  static Widget screen() => BlocProvider(
        create: (context) => di<PassBloc>()..add(IsSharedPinEmpty()),
        child: const PasswordEditDialog(),
      );

  @override
  _PasswordEditDialogState createState() => _PasswordEditDialogState();
}

class _PasswordEditDialogState extends State<PasswordEditDialog> {
  bool isOldVisible = true;
  bool isNewVisible = true;
  bool isConfirmVisible = true;
  TextEditingController oldPassword = TextEditingController();
  TextEditingController newPassword = TextEditingController();
  TextEditingController confirmPassword = TextEditingController();
  var maskFormatter = MaskTextInputFormatter(mask: '####');
  late PassBloc bloc;

  @override
  void initState() {
    bloc = BlocProvider.of<PassBloc>(context);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AllDialogSkeleton(
      title: LocaleKeys.set_password.tr(),
      icon: 'assets/icons/ic_shield-security.svg',
      child: BlocBuilder<PassBloc, PassState>(
        builder: (context, state) {
          if (state is PassSuccessState) {
            Navigator.pop(context);
          } else if (state is PassConfirmFailureState) {
            CustomToast.showToast(state.message);
            bloc.add(IsSharedPinEmpty());
          } else if (state is PassIsEmptyState) {
            return Column(
              children: [
                SizedBox(height: 24.h),
                Visibility(
                  visible: !state.isEmpty,
                  child: PasswordTextFieldWidget(
                    controller: oldPassword,
                    visible: isOldVisible,
                    hintTextTitle: LocaleKeys.enter_old_password.tr(),
                    inputFormatter: maskFormatter,
                    prefix: false,
                  ),
                ),
                PasswordTextFieldWidget(
                  controller: newPassword,
                  visible: isNewVisible,
                  hintTextTitle: LocaleKeys.enter_new_password.tr(),
                  inputFormatter: maskFormatter,
                  prefix: true,
                ),
                PasswordTextFieldWidget(
                  controller: confirmPassword,
                  visible: isConfirmVisible,
                  hintTextTitle: LocaleKeys.confirm_password.tr(),
                  inputFormatter: maskFormatter,
                  prefix: true,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        child: Text(
                          LocaleKeys.return_word.tr(),
                          style: TextStyle(
                              fontSize: 16.sp,
                              fontFamily: 'Medium',
                              color: cFirstColor),
                        ),
                        style: ButtonStyle(
                          fixedSize: MaterialStateProperty.all(Size(
                              MediaQuery.of(context).size.width / 2.5, 57.h)),
                          elevation: MaterialStateProperty.all(0),
                          backgroundColor: MaterialStateProperty.all(
                              cFirstColor.withOpacity(0.1)),
                          shape:
                              MaterialStateProperty.all(RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          )),
                        )),
                    ElevatedButton(
                      style: ButtonStyle(
                          fixedSize: MaterialStateProperty.all(Size(
                              MediaQuery.of(context).size.width / 2.5, 57.h)),
                          backgroundColor:
                              MaterialStateProperty.all(cFirstColor),
                          elevation: MaterialStateProperty.all(0),
                          shape: MaterialStateProperty.all(
                              RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10)))),
                      onPressed: () {
                        bloc.add(OnPressedEvent(
                          newPassword: newPassword,
                          confirmPassword: confirmPassword,
                          oldPassword: oldPassword,
                        ));
                        setState(() {});
                      },
                      child: Text(LocaleKeys.save.tr(),
                          style: TextStyle(
                              color: cWhiteColor,
                              fontSize: 16.sp,
                              fontFamily: 'Medium')),
                    ),
                  ],
                )
              ],
            );
          }
          return Center(
            child: SizedBox(
                height: 310.h,
                child: CupertinoActivityIndicator(
                  color: Theme.of(context).primaryColor,
                )),
          );
        },
      ),
    );
  }
}
