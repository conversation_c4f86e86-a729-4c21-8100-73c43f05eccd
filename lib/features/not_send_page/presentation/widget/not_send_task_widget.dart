
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/features/tasks/data/model/user.dart';
import 'package:govassist/generated/assets.dart';
import 'package:zoom_tap_animation/zoom_tap_animation.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:govassist/translations/locale_keys.g.dart';

class NotSendTaskWidget extends StatefulWidget {
  final bool withPhoto;
  final bool withVideo;
  final bool withText;
  final String date;
  final String title;
  final String desc;
  final VoidCallback onTap;
  final Color color;
  final bool selected;
  final User user;

  const NotSendTaskWidget(
      {super.key,
      required this.withPhoto,
      required this.withVideo,
      required this.withText,
      required this.date,
      required this.title,
      required this.desc,
      required this.onTap,
      required this.color,
      required this.selected,
      required this.user});

  @override
  State<NotSendTaskWidget> createState() => _NotSendTaskWidgetState();
}

class _NotSendTaskWidgetState extends State<NotSendTaskWidget> {
  final DateFormat formatterDate = DateFormat('dd.MM.yyyy');

  @override
  Widget build(BuildContext context) {
    return ZoomTapAnimation(
      onTap: widget.onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
        margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 6.h),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20.r),
          color: widget.color,
          boxShadow: [boxShadow20],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(cRadius10.r),
                    child: Container(
                      width: 84.h,
                      height: 84.h,
                      padding: EdgeInsets.all(10.w),
                      decoration: BoxDecoration(
                        color: widget.selected
                            ? cWhiteColor
                            : cFirstColor.withAlpha(30),
                        borderRadius: BorderRadius.circular(cRadius10.r),
                      ),
                      child: pictureContent(
                          widget.withPhoto, widget.withVideo, widget.withText),
                    ),
                  ),
                  SizedBox(
                    width: 14.w,
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Text(
                          widget.title ?? "loading",
                          maxLines: 2,
                          style: TextStyle(
                              fontSize: 15.sp,
                              color: widget.selected
                                  ? cWhiteColor
                                  : Theme.of(context)
                                      .textTheme
                                      .bodyMedium
                                      ?.color,
                              overflow: TextOverflow.ellipsis,
                              fontWeight: FontWeight.w600),
                        ),
                        SizedBox(
                          height: 8.h,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              "${LocaleKeys.given.tr()}:",
                              style: TextStyle(
                                color: widget.selected
                                    ? cWhiteColor
                                    : Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.color,
                                fontSize: 15.sp,
                              ),
                            ),
                            SizedBox(
                              width: 10.w,
                            ),
                            Flexible(
                              child: Text(
                                fullName(widget.user),
                                textAlign: TextAlign.right,
                                style: TextStyle(
                                    fontSize: 15.sp,
                                    fontWeight: FontWeight.w500,
                                    color: widget.selected
                                        ? cWhiteColor
                                        : Theme.of(context)
                                            .textTheme
                                            .bodyLarge
                                            ?.color),
                              ),
                            )
                          ],
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              "${LocaleKeys.given_date.tr()}:",
                              style: TextStyle(
                                color: widget.selected
                                    ? cWhiteColor
                                    : Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.color,
                                fontSize: 15.sp,
                              ),
                            ),
                            Text(
                                formatterDate.format(DateTime.parse(
                                    widget.date ?? "1998-10-16")),
                                style: TextStyle(
                                    fontSize: 15.sp,
                                    fontWeight: FontWeight.w500,
                                    color: widget.selected
                                        ? cWhiteColor
                                        : Theme.of(context)
                                            .textTheme
                                            .bodyLarge
                                            ?.color))
                          ],
                        ),
                      ],
                    ),
                  ),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      SizedBox(
                        height: 8.h,
                      ),
                    ],
                  )
                ],
              ),
            ),
            Visibility(
              visible: widget.desc.isEmpty ? false : true,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    height: 8.h,
                  ),
                  SvgPicture.asset(
                    Assets.iconsDashDivider,
                    width: MediaQuery.of(context).size.width,
                    colorFilter: ColorFilter.mode(
                        widget.selected == true ? cWhiteColor : cGrayColor1,
                        BlendMode.srcIn),
                  ),
                  SizedBox(
                    height: 4.h,
                  ),

                  Text(
                    widget.desc ?? "Empty",
                    textAlign: TextAlign.left,
                    style: TextStyle(
                      overflow: TextOverflow.ellipsis,
                      fontSize: 15.sp,
                      color: widget.selected
                          ? cWhiteColor
                          : Theme.of(context).textTheme.bodyMedium?.color,
                    ),
                    maxLines: 2,
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  String fullName(User? user) {
    String? middleName = user?.middleName;
    String? lastName = user?.lastName;
    if (middleName != null && lastName != null) {
      return "$middleName $lastName";
    } else if (middleName != null && lastName == null) {
      return middleName;
    } else if (middleName == null && lastName != null) {
      return lastName;
    } else {
      return "loading...";
    }
  }

  Widget pictureContent(bool withPhoto, bool withVideo, bool withText) {
    if (withPhoto == true && withVideo == true && withText == true) {
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            children: [
              SvgPicture.asset(Assets.iconsCamera1, width: 20.h, height: 20.h),
              SvgPicture.asset(Assets.iconsImageUpload,
                  width: 20.h, height: 20.h),
            ],
          ),
          SvgPicture.asset(Assets.iconsText, width: 20.h, height: 20.h),
        ],
      );
    } else if (withPhoto == false && withVideo == true && withText == true) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(Assets.iconsText, width: 20.h, height: 20.h),
          SvgPicture.asset(Assets.iconsCamera1, width: 20.h, height: 20.h),
        ],
      );
    } else if (withPhoto == true && withVideo == false && withText == true) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(Assets.iconsText, width: 20.h, height: 20.h),
          SvgPicture.asset(Assets.iconsImageUpload, width: 20.h, height: 20.h),
        ],
      );
    } else if (withPhoto == true && withVideo == true && withText == false) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(Assets.iconsCamera1, width: 20.h, height: 20.h),
          SvgPicture.asset(Assets.iconsImageUpload, width: 20.h, height: 20.h),
        ],
      );
    } else if (withPhoto == true && withVideo == false && withText == false) {
      return SvgPicture.asset(
        Assets.iconsImageUpload,
        width: 40.h,
        height: 40.h,
      );
    } else if (withPhoto == false && withVideo == true && withText == false) {
      return SvgPicture.asset(
        Assets.iconsCamera1,
        width: 40.h,
        height: 40.h,
      );
    } else if (withPhoto == false && withVideo == false && withText == true) {
      return SvgPicture.asset(
        Assets.iconsText,
        width: 40.h,
        height: 40.h,
      );
    }

    return SizedBox();
  }
}
