import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart' hide Trans;
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/core/widgets/gradient_material_button.dart';
import 'package:govassist/core/widgets/header_widget.dart';
import 'package:govassist/di/dependency_injection.dart';
import 'package:govassist/features/complaint/presentation/bloc/complaint_new/complaint_cubit.dart';
import 'package:govassist/features/complaint/presentation/bloc/complaint_read/complain_read_cubit.dart';
import 'package:govassist/features/complaint/presentation/page/complaint_sheet.dart';
import 'package:govassist/features/complaint/presentation/page/new_complaint_page.dart';
import 'package:govassist/features/complaint/presentation/page/read_complaint_page.dart';
import 'package:govassist/translations/locale_keys.g.dart';

class ComplaintPage extends StatefulWidget {
  static Widget screen() {
    return MultiBlocProvider(providers: [
      BlocProvider(create: (context) => di<ComplaintCubit>()),
      BlocProvider(create: (context) => di<ComplaintReadCubit>()),
    ], child: ComplaintPage());
  }

  const ComplaintPage({super.key});

  @override
  State<ComplaintPage> createState() => _ComplaintPageState();
}

class _ComplaintPageState extends State<ComplaintPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // late ComplaintCubit cubit1;
  // late ComplaintCubit cubit2;

  @override
  void initState() {
    super.initState();
    // cubit1=BlocProvider.of<ComplaintCubit>(context);
    // cubit2=BlocProvider.of<ComplaintCubit>(context);
    _tabController = TabController(vsync: this, initialIndex: 0, length: 2);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppHeaderWidget(
        title: LocaleKeys.send_complaint.tr(),
        onBackTap: () {
          Navigator.pop(context);
        },
        isBackVisible: true,
      ),
      body: Column(
        children: [
          Container(
            margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
            padding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 3.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(cRadius10.r),
              color: Theme.of(context).cardTheme.color,
              boxShadow: [
                boxShadow5,
              ],
            ),
            child: TabBar(
              dividerColor: Colors.transparent,
              indicatorSize: TabBarIndicatorSize.tab,
              controller: _tabController,
              indicatorColor: Colors.transparent,
              unselectedLabelColor:
                  Theme.of(context).textTheme.bodySmall?.color,
              labelColor: cWhiteColor,
              indicator: BoxDecoration(
                  color: cFirstColor,
                  borderRadius: BorderRadius.circular(cRadius10.r)),
              tabs: [
                Tab(
                  height: context.isTablet ? 50.h : 40.h,
                  child: Text(
                    LocaleKeys.not_saw.tr(),
                    style:
                        TextStyle(fontWeight: FontWeight.w500, fontSize: 16.sp),
                  ),
                ),
                Tab(
                  height: context.isTablet ? 50.h : 40.h,
                  child: Text(
                    LocaleKeys.saw.tr(),
                    style:
                        TextStyle(fontWeight: FontWeight.w500, fontSize: 16.sp),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
              child: TabBarView(
                  controller: _tabController,
                  children: [NewComplaintPage(), ReadComplaintPage()]))
        ],
      ),
      floatingActionButton: GradientMaterialButton(
        onTap: () {
          showModalBottomSheet(
              context: context,
              isScrollControlled: true,
              backgroundColor: Colors.transparent,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.only(
                      topRight: Radius.circular(20.r),
                      topLeft: Radius.circular(20.r))),
              builder: (_) {
                return ComplaintSheet.screen();
              }).then((value) {
            if (_tabController.index == 0) {
              BlocProvider.of<ComplaintCubit>(context).getComplaint(false);
            }
          });
        },
        height: 56.h,
        width: 180.w,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add,
              color: cWhiteColor,
              size: 20.w,
            ),
            SizedBox(
              width: 10.w,
            ),
            Text(
              LocaleKeys.new_complaint.tr(),
              style: TextStyle(
                  color: cWhiteColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 14.sp),
            )
          ],
        ),
      ),
    );
  }
}
