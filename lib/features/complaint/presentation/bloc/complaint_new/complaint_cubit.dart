import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:govassist/core/database/isar_service.dart';
import 'package:govassist/core/network/network_info.dart';
import 'package:govassist/core/utils/api_path.dart';
import 'package:govassist/features/complaint/model/complaint_model.dart';
import 'package:govassist/translations/locale_keys.g.dart';

part 'complaint_state.dart';

class ComplaintCubit extends Cubit<ComplaintState> {
  final Dio dio;
  final NetworkInfo networkInfo;
  final IsarService isarService;

  ComplaintCubit(
      {required this.dio, required this.networkInfo, required this.isarService})
      : super(ComplaintState.initial());

  getComplaint(bool filter) async {
      if (await networkInfo.isConnected) {
        emit(state.copyWith(status: ComplaintStatus.loading));
        try {
          var response =
          await dio.post(getComplaintPath, data: {"readed": filter});
          if (response.statusCode == 200) {

            emit(state.copyWith(
                status: ComplaintStatus.success,
                list: complaintListFromJson(response.data)));

          } else {
            emit(state.copyWith(
                status: ComplaintStatus.failure,
                message:
                "${LocaleKeys.error_download_data.tr()}. Status code:${response.statusCode}"));
          }
        } on DioException catch (e) {
          if (e.type == DioExceptionType.connectionTimeout) {
            emit(state.copyWith(
                status: ComplaintStatus.failure, message: "Timeout exception"));
          } else {
            emit(state.copyWith(
                status: ComplaintStatus.failure,
                message:
                "${LocaleKeys.error_download_data.tr()}. Status code:${e.response?.statusCode}"));
          }
        } catch (e) {
          emit(state.copyWith(
              status: ComplaintStatus.failure, message: "${LocaleKeys.error_download_data.tr()}"));
        }
      } else {
        emit(state.copyWith(
            status: ComplaintStatus.failure, message: LocaleKeys.check_internet_connection.tr()));
      }

  }
}
