import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:govassist/core/network/network_info.dart';
import 'package:govassist/core/utils/api_path.dart';
import 'package:govassist/translations/locale_keys.g.dart';

part 'complaint_send_state.dart';

class ComplaintSendCubit extends Cubit<ComplaintSendState> {
  final Dio dio;
  final NetworkInfo networkInfo;

  ComplaintSendCubit({required this.dio, required this.networkInfo})
      : super(ComplaintSendState.initial());

  sendComplaint(String text) async {
    if (await networkInfo.isConnected) {
      emit(state.copyWith(status: ComplaintSendStatus.loading));
      try {
        var response = await dio.post(complaintSend, data: {"text": text});
        if (response.statusCode == 200) {
          emit(state.copyWith(status: ComplaintSendStatus.success));
        } else {
          emit(state.copyWith(
              status: ComplaintSendStatus.failure,
              message:
                  "${LocaleKeys.unknown_server_error_retry.tr()}. Status code:${response.statusCode}"));
        }
      } on DioException catch (e) {
        if (e.type == DioExceptionType.connectionTimeout) {
          emit(state.copyWith(
              status: ComplaintSendStatus.failure,
              message: "Timeout exception"));
        } else {
          emit(state.copyWith(
              status: ComplaintSendStatus.failure,
              message:
                  "${LocaleKeys.unknown_server_error_retry.tr()}. Status code:${e.response?.statusCode}"));
        }
      } catch (e) {
        emit(state.copyWith(
            status: ComplaintSendStatus.failure,
            message: "${LocaleKeys.unknown_server_error_retry.tr()}"));
      }
    } else {
      emit(state.copyWith(
          status: ComplaintSendStatus.failure,
          message: LocaleKeys.check_internet_connection.tr()));
    }
  }
}
