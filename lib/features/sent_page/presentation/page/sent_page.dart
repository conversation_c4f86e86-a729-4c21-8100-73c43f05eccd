import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:govassist/core/widgets/failure_widget.dart';
import 'package:govassist/di/dependency_injection.dart';
import 'package:govassist/features/sent_page/data/model/sent_model.dart';
import 'package:govassist/features/sent_page/presentation/bloc/sent_cubit.dart';
import 'package:govassist/features/sent_page/presentation/widget/sent_item.dart';
import 'package:govassist/features/tasks/presentation/widgets/empty_list_widget.dart';
import 'package:govassist/translations/locale_keys.g.dart';

class SentPage extends StatefulWidget {
  static Widget screen() {
    return BlocProvider(
      create: (context) => di<SentCubit>(),
      child: SentPage(),
    );
  }

  const SentPage({super.key});

  @override
  State<SentPage> createState() => _SentPageState();
}

class _SentPageState extends State<SentPage> {
  var headerTimes = Map<String, dynamic>();
  final DateFormat formatterDate = DateFormat('dd.MM.yyyy');
  List<SentModel> list = [];

  @override
  void initState() {
    super.initState();
    BlocProvider.of<SentCubit>(context).getSentList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<SentCubit, SentState>(
        listener: (context, state) {
          if (state.status == SentStatus.success) {
            list = sortSentModelsByUploadTime(state.list ?? []);
          }
        },
        builder: (context, state) {
          if (state.status == SentStatus.loading) {
            return Center(
              child: CupertinoActivityIndicator(
                color: Theme.of(context).primaryColor,
                radius: 30.r,
              ),
            );
          } else if (state.status == SentStatus.success) {
            return list.isEmpty == true
                ? EmptyListWidget(onTap: () {
                    BlocProvider.of<SentCubit>(context).getSentList();
                  }, title: LocaleKeys.notExistSentWork.tr(),)
                : RefreshIndicator(
                    onRefresh: () async {
                      BlocProvider.of<SentCubit>(context).getSentList();
                    },
                    child: ListView.builder(
                        padding: EdgeInsets.only(bottom: 120.h),
                        itemCount: list.length,
                        physics: AlwaysScrollableScrollPhysics(parent: BouncingScrollPhysics()),
                        itemBuilder: (BuildContext context, int index) {
                          SentModel sentModel = list[index];
                          String time = formatterDate.format(
                              DateTime.parse(sentModel.uploadTime.toString()));
                          String id = sentModel.id ?? "-1";
                          if ((!headerTimes.containsKey(time)) ||
                              headerTimes.containsValue(id)) {
                            headerTimes[time] = id;
                            return SentItem(
                              sentModel: list[index],
                              visible: true,
                            );
                          } else {
                            return SentItem(
                              sentModel: list[index],
                              visible: false,
                            );
                          }
                        }),
                  );
          } else if (state.status == SentStatus.failure) {
            return Center(
              child: FailureWidget(
                text: state.message ?? "",
                onTap: () {
                  BlocProvider.of<SentCubit>(context).getSentList();
                },
              ),
            );
          } else {
            return Center(
              child: FailureWidget(
                text: state.message ?? "",
                onTap: () {
                  BlocProvider.of<SentCubit>(context).getSentList();
                },
              ),
            );
          }
        },
      ),
    );
  }

  List<SentModel> sortSentModelsByUploadTime(List<SentModel> sentModels) {
    sentModels.sort((a, b) {
      // Parse the uploadTime strings to DateTime objects
      DateTime aUploadTime = DateTime.parse(a.uploadTime!);
      DateTime bUploadTime = DateTime.parse(b.uploadTime!);

      // Compare the DateTime objects
      return bUploadTime.compareTo(aUploadTime);
    });

    return sentModels;
  }
}
