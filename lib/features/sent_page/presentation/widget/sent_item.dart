import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:govassist/core/function/functions.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/features/sent_page/data/model/sent_model.dart';
import 'package:govassist/generated/assets.dart';
import 'package:zoom_tap_animation/zoom_tap_animation.dart';

class SentItem extends StatelessWidget {
  final SentModel sentModel;
  final bool visible;

  SentItem({super.key, required this.sentModel, required this.visible});

  final DateFormat formatterDate = DateFormat('dd.MM.yyyy');
  final DateFormat formatterHour = DateFormat('HH:mm');

  @override
  Widget build(BuildContext context) {
    return ZoomTapAnimation(
      child: Column(
        children: [
          Visibility(
            visible: visible,
            child: Container(
              margin: EdgeInsets.only(top: 10.h, bottom: 6.h),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  color: isDark() ? cCardDarkColor : cBlackColor.withAlpha(20),
                  borderRadius: BorderRadius.circular(30.r)),
              width: 100.w,
              height: 26.h,
              child: Text(
                formatterDate.format(
                    DateTime.parse(sentModel.uploadTime ?? "1970-01-01")),
                style: Theme.of(context)
                    .textTheme
                    .bodyMedium
                    ?.copyWith(fontSize: 14.sp),
              ),
            ),
          ),
          InkWell(
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
              margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 6.h),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20.r),
                color: Theme.of(context).cardTheme.color,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    spreadRadius: 4,
                    blurRadius: 5,
                    offset: Offset(0, 4), // changes position of shadow
                  ),
                ],
              ),
              child: Container(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(10.r),
                      child: Container(
                        alignment: Alignment.center,
                        width: 84.h,
                        height: 84.h,
                        decoration: BoxDecoration(
                          color: cFirstColor.withAlpha(30),
                          borderRadius: BorderRadius.circular(10.r),
                        ),
                        child: CachedNetworkImage(
                          width: 84.h,
                          height: 84.h,
                          imageUrl: sentModel.media?.isNotEmpty == true
                              ? sentModel.media![0].image!
                              : "",
                          placeholder: (context, url) =>
                              CupertinoActivityIndicator(),
                          errorWidget: (context, url, error) {
                            if (sentModel.media?.isEmpty == true) {
                              return Padding(
                                padding: EdgeInsets.all(10.h),
                                child: SvgPicture.asset(
                                  Assets.iconsCamera1,
                                  width: 40.h,
                                  height: 40.h,
                                ),
                              );
                            } else {
                              return Icon(
                                Icons.error,
                                size: 40.h,
                                color: cRedColor,
                              );
                            }
                          },
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 10.w,
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            subCategoryWorkTitle(
                                subCategory: sentModel.subCategory),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                                fontSize: 16.sp, fontWeight: FontWeight.w600),
                          ),
                          SizedBox(
                            height: 8.h,
                          ),
                          Text(
                            sentModel.desc ?? "Loading...",
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                                fontSize: 14.sp, fontWeight: FontWeight.w400),
                          ),
                          SizedBox(
                            height: 8.h,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                formatterDate.format(DateTime.parse(
                                    sentModel.uploadTime ?? "1970-01-01")),
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.copyWith(fontSize: 12.sp),
                              ),
                              Text(
                                formatterHour.format(DateTime.parse(
                                    sentModel.uploadTime ?? "1970-01-01")),
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.copyWith(fontSize: 12.sp),
                              ),
                            ],
                          )
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
