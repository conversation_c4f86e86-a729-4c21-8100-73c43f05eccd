import 'dart:convert';
import 'dart:io';
import 'package:animated_loading_border/animated_loading_border.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart' hide Trans;
import 'package:govassist/features/tasks/data/model/special_new_task.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sn_progress_dialog/options/cancel.dart';
import 'package:sn_progress_dialog/progress_dialog.dart';
import 'package:govassist/core/database/embedded_models.dart';
import 'package:govassist/core/database/isar_service.dart';
import 'package:govassist/core/errors/failures.dart';
import 'package:govassist/core/function/functions.dart';
import 'package:govassist/core/location/location_service.dart';
import 'package:govassist/core/photo/image_picker_utils.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/core/widgets/alert_dialog.dart';
import 'package:govassist/core/widgets/custom_toast.dart';
import 'package:govassist/core/widgets/gradient_material_button.dart';
import 'package:govassist/core/widgets/header_widget_sub.dart';
import 'package:govassist/core/widgets/simple_material_button.dart';
import 'package:govassist/di/dependency_injection.dart';
import 'package:govassist/features/home/<USER>/navigation.dart';
import 'package:govassist/features/send_data/camera_page.dart';
import 'package:govassist/features/send_data/models/img_file_model.dart';
import 'package:govassist/features/task_send/data/model/task_img_model.dart';
import 'package:govassist/generated/assets.dart';

import 'package:govassist/translations/locale_keys.g.dart';
import 'package:zoom_tap_animation/zoom_tap_animation.dart';

import '../../../../core/widgets/dotted_border.dart';

class TaskSendPage extends StatefulWidget {
  final SpecialNewTaskItem specialNewTaskItem;


  const TaskSendPage({super.key, required this.specialNewTaskItem});

  @override
  State<TaskSendPage> createState() => _TaskSendPageState();
}

class _TaskSendPageState extends State<TaskSendPage> {
  AnimationController? controllerImage;
  AnimationController? controllerVideo;
  AnimationController? controllerText;
  AnimationController? controllerWithFile;
  late final AppLifecycleListener _listener;
  late AndroidDeviceInfo? androidInfo;
  final FocusNode unitCodeCtrlFocusNode = FocusNode();
  TextEditingController text = TextEditingController();
  bool _shouldRequestPermission = false;
  String sana = "0000-00-00", latLang = "0.00,0.00";
  final customFormat = DateFormat('yyyy-MM-dd HH:mm:ss');
  List<int>? imageBytes;
  String? imageString;
  String? documentPath;
  late ProgressDialog pd;

  List<int>? videoBytes;
  String? videoString;

  late List<ImgModel> media = [];

  List<ImgFileModel?> works = [];
  ImgFileModel? video;
  ImgFileModel? image;
  int emptyCount = 0;
  final IsarService isarService = di();

  @override
  void initState() {
    // Initialize the AppLifecycleListener class and pass callbacks
    pd = ProgressDialog(context: context);

    if (Platform.isAndroid) {
      androidInfo = di();
    } else {
      androidInfo = null;
    }

    _listener = AppLifecycleListener(
      onStateChange: _onStateChanged,
    );

    permissionWall();
    super.initState();
  }

  // Listen to the app lifecycle state changes
  void _onStateChanged(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.detached:
        _onDetached();
        break;
      case AppLifecycleState.resumed:
        _onResumed();
        break;
      case AppLifecycleState.inactive:
        _onInactive();
        break;
      case AppLifecycleState.hidden:
        _onHidden();
        break;
      case AppLifecycleState.paused:
        _onPaused();
        break;
    }
  }

  void _onDetached() => print('detached');

  void _onResumed() {
    if (_shouldRequestPermission) {
      permissionWall();
      _shouldRequestPermission = false;
    }
  }

  void _onInactive() => print('inactive');

  void _onHidden() => print('hidden');

  void _onPaused() async {
    _shouldRequestPermission = true;
  }

  @override
  void dispose() {
    _listener.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;

    /*24 is for notification bar on Android*/
    final double itemHeight = (size.height - kToolbarHeight - 24) / 200;
    final double itemWidth = size.width;
    return Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: AppHeaderWidgetSub(
          title: LocaleKeys.task.tr(),
          onBackTap: () async {
            var result =
                await showAlertText(context, LocaleKeys.sure_to_exit.tr()) ??
                    false;
            if (result) {
              Navigator.of(context).pop();
            }
          },
          actionImage: Assets.iconsText,
          onActionTap: () {},
        ),
        body: Container(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Visibility(
                visible: true,
                child: Column(
                  children: [
                    SizedBox(
                      height: 33.h,
                    ),
                    Container(
                        height: widget.specialNewTaskItem.withPhoto == true ||
                                widget.specialNewTaskItem.withVideo == true
                            ? ((itemWidth / itemHeight) + 20).h
                            : 0,
                        child: Row(
                          children: [
                            Visibility(
                              visible:
                                  widget.specialNewTaskItem.withVideo == true,
                              child: Expanded(
                                  flex: 1,
                                  child: AnimatedLoadingBorder(
                                      controller: (control) {
                                        controllerVideo = control;
                                        control.reset();
                                      },
                                      borderColor: cFirstColor,
                                      trailingBorderColor: cSecondColor,
                                      duration: Duration(milliseconds: 800),
                                      borderWidth: 2,
                                      cornerRadius: cRadius12.r,
                                      child: ZoomTapAnimation(
                                          begin: 1,
                                          end: 0.8,
                                          child: videoWidget()))),
                            ),
                            SizedBox(
                              width:
                                  widget.specialNewTaskItem.withVideo == true &&
                                          widget.specialNewTaskItem.withPhoto ==
                                              true
                                      ? 10.w
                                      : 0,
                            ),
                            Visibility(
                              visible:
                                  widget.specialNewTaskItem.withPhoto == true,
                              child: Expanded(
                                  flex: 1,
                                  child: AnimatedLoadingBorder(
                                    controller: (control) {
                                      controllerImage = control;
                                      control.reset();
                                    },
                                    borderColor: cFirstColor,
                                    trailingBorderColor: cSecondColor,
                                    duration: Duration(milliseconds: 800),
                                    borderWidth: 2,
                                    cornerRadius: cRadius12.r,
                                    child: ZoomTapAnimation(
                                        begin: 1,
                                        end: 0.8,
                                        child: imageWidget()),
                                  )),
                            )
                          ],
                        )),
                    SizedBox(
                      height: 24.h,
                    ),
                  ],
                ),
              ),
              Visibility(
                visible: widget.specialNewTaskItem.withText == true,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      LocaleKeys.comment.tr(),
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    SizedBox(
                      height: 10.h,
                    ),
                    AnimatedLoadingBorder(
                      controller: (control) {
                        controllerText = control;
                        control.reset();
                      },
                      borderColor: cFirstColor,
                      trailingBorderColor: cSecondColor,
                      duration: Duration(milliseconds: 800),
                      borderWidth: 4,
                      cornerRadius: cRadius12.r,
                      child: Container(
                        width: MediaQuery.of(context).size.width,
                        height: 160.h,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(cRadius12.r),
                          color: Theme.of(context).cardTheme.color,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withOpacity(0.1),
                              spreadRadius: 4,
                              blurRadius: 5,
                              offset:
                                  Offset(0, 4), // changes position of shadow
                            ),
                          ],
                        ),
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                              vertical: 2.h, horizontal: 10.w),
                          child: TextField(
                            controller: text,
                            maxLines: null,
                            decoration: InputDecoration(
                                border: InputBorder.none,
                                hintText: LocaleKeys.text.tr(),
                                helperStyle: TextStyle(color: cSecondTextColor),
                                hintStyle: TextStyle(
                                    color: cGrayColor1, fontSize: 16.sp)),
                            style: TextStyle(
                                color: Theme.of(context)
                                    .textTheme
                                    .bodySmall
                                    ?.color,
                                fontSize: 16.sp),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 10.h,
                    ),
                  ],
                ),
              ),
              Visibility(
                visible: widget.specialNewTaskItem.withFile ?? false,
                child: AnimatedLoadingBorder(
                  controller: (control) {
                    controllerWithFile = control;
                    control.reset();
                  },
                  borderColor: cFirstColor,
                  trailingBorderColor: cSecondColor,
                  duration: Duration(milliseconds: 400),
                  borderWidth: 2,
                  cornerRadius: cRadius12.r,
                  child: MaterialButton(
                    color: Theme.of(context).cardTheme.color,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(cRadius12.r)),
                    padding:
                        EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.w),
                    onPressed: () async {
                      pickFile();
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            documentPath == null
                                ? Container(
                                    width: 24.w,
                                    height: 24.w,
                                    padding: EdgeInsets.all(4.w),
                                    child: SvgPicture.asset(
                                        Assets.iconsDownload,
                                        colorFilter: ColorFilter.mode(
                                            isDark()
                                                ? cPrimaryTextDark
                                                : cFirstColor,
                                            BlendMode.srcIn)),
                                  )
                                : Container(
                                    width: 24.w,
                                    height: 24.w,
                                    decoration: BoxDecoration(
                                        borderRadius:
                                            BorderRadius.circular(6.r),
                                        color: isDark()
                                            ? cFourthColorDark
                                            : cGrayColor0),
                                    padding: EdgeInsets.all(4.w),
                                    child: SvgPicture.asset(
                                        Assets.iconsDocument,
                                        colorFilter: ColorFilter.mode(
                                            isDark()
                                                ? cPrimaryTextDark
                                                : cFirstColor,
                                            BlendMode.srcIn)),
                                  ),
                            SizedBox(
                              width: 8.w,
                            ),
                            Text(
                              documentPath?.split('/').last ??
                                  "${LocaleKeys.upload_file.tr()}",
                              style: TextStyle(fontSize: 14.sp),
                            ),
                          ],
                        ),
                        Visibility(
                          visible: documentPath != null,
                          child: InkWell(
                            onTap: () {
                              setState(() {
                                documentPath = null;
                              });
                            },
                            child: SvgPicture.asset(
                              Assets.iconsCloseRed,
                              width: 16.w,
                              height: 16.w,
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ),
              SizedBox(
                height: 60.h,
              ),
              Row(
                children: [
                  Expanded(
                    child: SimpleMaterialButton(
                      title: LocaleKeys.cancelling1.tr(),
                      onTap: () {
                        Get.back();
                      },
                      height: 56.h,
                      color: Theme.of(context).cardColor,
                      textColor: cRedTextColor,
                    ),
                  ),
                  SizedBox(
                    width: 17.w,
                  ),
                  Expanded(
                      child: GradientMaterialButton(
                          title: LocaleKeys.sending.tr(),
                          onTap: () {
                            pd.show(
                                max: 100,
                                msg: "${LocaleKeys.data_is_saving.tr()}...",
                                msgFontSize: 15.sp,
                                cancel: Cancel(
                                    autoHidden: true,
                                    cancelImageColor: Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.color),
                                msgColor: Theme.of(context)
                                    .textTheme
                                    .bodyMedium!
                                    .color!,
                                valueColor: Theme.of(context)
                                    .textTheme
                                    .bodyMedium!
                                    .color!,
                                backgroundColor:
                                    Theme.of(context).scaffoldBackgroundColor);
                            addFile().then((value) {
                              pd.close();
                            });
                          },
                          height: 56.h))
                ],
              )
            ],
          ),
        ));
  }

  String typeIcon(String type) {
    switch (type) {
      case VIDEO:
        {
          return Assets.iconsCamera1;
        }
      case IMAGE:
        {
          return Assets.iconsPlaceholder;
        }
      case TEXT:
        {
          return Assets.iconsText;
        }
      default:
        {
          return "";
        }
    }
  }

  Future<Map<Permission, PermissionStatus>> requestPermissions() async {
    var verSDK = androidInfo?.version.sdkInt ?? 0;
    Map<Permission, PermissionStatus> statuses = await [
      Platform.isAndroid && verSDK < 33
          ? Permission.storage
          : Permission.photos,
      Permission.location,
      Permission.camera,
      Permission.microphone,
    ].request();

    return statuses;
  }

  void permissionWall() async {
    Map<Permission, PermissionStatus> statuses = {};
    var verSDK = androidInfo?.version.sdkInt ?? 0;
    statuses = await requestPermissions();

    if (statuses[Permission.location] != PermissionStatus.granted ||
        statuses[Platform.isAndroid && verSDK < 33
                ? Permission.storage
                : Permission.photos] !=
            PermissionStatus.granted ||
        statuses[Permission.camera] != PermissionStatus.granted ||
        statuses[Permission.microphone] != PermissionStatus.granted) {
      if (statuses[Permission.location] == PermissionStatus.permanentlyDenied ||
          statuses[Platform.isAndroid && verSDK < 33
                  ? Permission.storage
                  : Permission.photos] ==
              PermissionStatus.permanentlyDenied ||
          statuses[Permission.camera] == PermissionStatus.permanentlyDenied ||
          statuses[Permission.microphone] ==
              PermissionStatus.permanentlyDenied) {
        print(statuses[Permission.camera]);
        print(statuses[Permission.storage]);
        print(statuses[Permission.microphone]);
        print(statuses[Permission.location]);

        ///Shows custom dialog after user refuses for giving of any permissions
        showCustomDialog(context);
      } else {
        ///Points to the recursion
        permissionWall();
      }
    } else {
      /// If everything is granted, getting location and date
      getInfo();
    }
  }

  void storagePermissionWall() async {
    var verSDK = androidInfo?.version.sdkInt ?? 0;
    PermissionStatus status = await Permission.storage.request();
    if (Platform.isAndroid &&
        verSDK < 33 &&
        status != PermissionStatus.granted) {
      if (Platform.isAndroid &&
          verSDK < 33 &&
          status != PermissionStatus.permanentlyDenied) {
        showCustomDialog(context);
      } else {
        storagePermissionWall();
      }
    } else {
      pickFile();
    }
  }

  pickFile() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['pdf', 'docx', '.xls', '.zip'],
    );
    if (result?.files.single.path != null) {
      List<String> extensions = ['pdf', 'docx', '.xls', '.zip'];
      String? extension = result?.files.single.path?.split(".").last;
      if (extensions.contains(extension)) {
        setState(() {
          documentPath = result?.files.single.path!;
        });
      } else {
        CustomToast.showToast("Unsupported format");
      }
    }
  }

  getInfo() async {
    final latLangService = di<LocationService>();
    latLang = await latLangService.getLatLang();
    sana = customFormat.format(DateTime.now()).toString();

    var lat = latLang.split(",").first;
    var lng = latLang.split(",").last;

    print(double.parse(lat));
    print(double.parse(lng));
  }

  takeCam({bool withVideo = false}) async {
    File? mediaFile;
    final picker = di<ImagePickerUtils>();

    mediaFile =
        File(await picker.selectImageFromCamera(context, isVideo: withVideo));
    mediaFile.path == "" ? mediaFile = null : mediaFile;
    sana = customFormat.format(DateTime.now()).toString();

    if (mediaFile != null && latLang != "0.00,0.00") {
      ///TODO: this line causes exception
      setState(() {
        if (withVideo) {
          video = ImgFileModel(latLang: latLang, sana: sana, image: mediaFile);
        } else {
          image = ImgFileModel(latLang: latLang, sana: sana, image: mediaFile);
        }
      });
    } else {
      CustomToast.showToast(LocaleKeys.content_empty.tr());
      permissionWall();
    }
  }

  Widget videoWidget() {
    return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () async {
          // You can request multiple permissions at once.
          Map<Permission, PermissionStatus> statuses = await [
            Permission.location,
            Permission.camera,
            Permission.microphone,
          ].request();

          if (statuses[Permission.location] == PermissionStatus.granted &&
              statuses[Permission.camera] == PermissionStatus.granted &&
              statuses[Permission.microphone] == PermissionStatus.granted) {
            takeCam(withVideo: true);
          } else {
            permissionWall();
          }
        },
        child: video == null
            ? DottedBorderWidget(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        Assets.iconsCamera2,
                        width: 40.h,
                        height: 40.h,
                        color: cFirstColor,
                      ),
                      SizedBox(
                        height: 4.h,
                      ),
                      Text(
                        LocaleKeys.video.tr(),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            color: cFirstColor,
                            fontWeight: FontWeight.w500,
                            fontSize: 16.sp),
                      )
                    ],
                  ),
                ),
              )
            : Stack(
                fit: StackFit.expand,
                children: [
                  ClipRRect(
                      borderRadius: BorderRadius.circular(10.0),
                      child:
                          ThumbnailViewer(filePath: video?.image?.path ?? '')),
                  Align(
                    alignment: Alignment.center,
                    child: InkWell(
                      onTap: () {
                        setState(() {
                          video = null;
                        });
                      },
                      child: SvgPicture.asset(
                        Assets.iconsClose,
                        color: cRedColor,
                        width: 40.w,
                        height: 40.h,
                      ),
                    ),
                  ),
                ],
              ));
  }

  Widget imageWidget() {
    return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () async {
          // You can request multiple permissions at once.
          Map<Permission, PermissionStatus> statuses = await [
            Permission.location,
            Permission.camera,
            Permission.microphone,
          ].request();

          if (statuses[Permission.location] == PermissionStatus.granted &&
              statuses[Permission.camera] == PermissionStatus.granted &&
              statuses[Permission.microphone] == PermissionStatus.granted) {
            takeCam(withVideo: false);
            print("VIDEO STATUS:${video}");
          } else {
            permissionWall();
          }
        },
        child: image == null
            ? DottedBorderWidget(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        Assets.iconsImageUpload,
                        width: 40.h,
                        height: 40.h,
                        color: cFirstColor,
                      ),
                      SizedBox(
                        height: 4.h,
                      ),
                      Text(
                        LocaleKeys.picture.tr(),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            color: cFirstColor,
                            fontWeight: FontWeight.w500,
                            fontSize: 16.sp),
                      )
                    ],
                  ),
                ),
              )
            : Stack(
                fit: StackFit.expand,
                children: [
                  ClipRRect(
                      borderRadius: BorderRadius.circular(10.0),
                      child: Image.file(
                        image?.image ?? File(''),
                        fit: BoxFit.cover,
                      )),
                  Align(
                    alignment: Alignment.center,
                    child: InkWell(
                      onTap: () {
                        setState(() {
                          image = null;
                        });
                      },
                      child: SvgPicture.asset(
                        Assets.iconsClose,
                        color: cRedColor,
                        width: 40.w,
                        height: 40.h,
                      ),
                    ),
                  ),
                ],
              ));
  }

  Future<bool> addFile() async {
    try {
      if (widget.specialNewTaskItem.withPhoto == true &&
          image == null &&
          controllerImage != null) {
        animate(controllerImage!);
      }
      if (widget.specialNewTaskItem.withVideo == true &&
          video == null &&
          controllerVideo != null) {
        animate(controllerVideo!);
      }
      if (widget.specialNewTaskItem.withText == true &&
          text.text.isEmpty &&
          controllerText != null) {
        animate(controllerText!);
      }
      if (widget.specialNewTaskItem.withFile == true && documentPath == null) {
        animate(controllerWithFile!);
      }
      print((widget.specialNewTaskItem.withText == true && text.text.isEmpty));
      print((widget.specialNewTaskItem.withVideo == true && video == null));
      print((widget.specialNewTaskItem.withText == true && image == null));

      if ((widget.specialNewTaskItem.withPhoto == true && image == null) ||
          (widget.specialNewTaskItem.withVideo == true && video == null) ||
          (widget.specialNewTaskItem.withText == true && text.text.isEmpty) ||
          (widget.specialNewTaskItem.withFile == true &&
              documentPath == null)) {
        CustomToast.showToast(LocaleKeys.content_empty.tr());
      } else {
        if (image != null) {
          imageBytes = image?.image?.readAsBytesSync();
          imageString = base64Encode(imageBytes!);
        }
        if (video != null) {
          videoBytes = video?.image?.readAsBytesSync();
          videoString = base64Encode(videoBytes!);
        }
        TaskImgModel taskImgModel = TaskImgModel(
            lat: latLang.split(",").first,
            lng: latLang.split(",").last,
            sendDate: sana,
            text: text.text,
            desc: widget.specialNewTaskItem.desc,
            image: imageString,
            taskId: widget.specialNewTaskItem.id,
            video: videoString,
            withVideo: widget.specialNewTaskItem.withVideo,
            withPhoto: widget.specialNewTaskItem.withPhoto,
            withText: widget.specialNewTaskItem.withText,
            receiveDate: widget.specialNewTaskItem.createdAt,
            titleUZ: widget.specialNewTaskItem.titleUZ,
            titleRU: widget.specialNewTaskItem.titleRU,
            titleQQ: widget.specialNewTaskItem.titleQQ,
            user: widget.specialNewTaskItem.user,
            documentPath: documentPath);
        try {
          await isarService.isar.writeTxn(() async {
            await isarService.isar.taskImgModels.put(taskImgModel);
          }).then((value) {
            WidgetsBinding.instance.addPostFrameCallback((time) {
              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(
                  builder: (BuildContext context) => BottomNavigationPage(
                    pageIndex: 1,
                    tabIndex: 1,
                  ),
                ),
                (Route route) => false,
              );
              // setState(() {
              //   image = null;
              //   video = null;
              //   text.clear();
              // });
            });
          });
        } catch (e) {
          print(e);
        }
      }
      return true;
    } on InputFormatterFailure catch (e) {
      debugPrint(LocaleKeys.error_in_uploading.tr());
      pd.close();
      return false;
    } catch (e) {
      CustomToast.showToast("${LocaleKeys.error1.tr()}: $e");
      print("${LocaleKeys.error1.tr()}: $e");
      pd.close();
      return false;
    }
  }

  animate(AnimationController controller) {
    controller.repeat();
    Future.delayed(Duration(seconds: 2)).then((value) {
      controller.reset();
    });
  }
}
