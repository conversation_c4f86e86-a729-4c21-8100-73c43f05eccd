import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class DrawerMenu extends StatelessWidget {
  final String title;
  final String icon;
  final Color? textColor;
  final Color? iconColor;
  final double? height;
  final bool? isPng;
  final VoidCallback onTap;

  const DrawerMenu(
      {super.key,
      required this.title,
      required this.icon,
      this.textColor,
      this.iconColor,
      this.height,
      required this.onTap,
      this.isPng});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: height ?? 50.h,
      child: MaterialButton(
          padding: EdgeInsets.zero,
          minWidth: MediaQuery.of(context).size.width,
          height: height ?? 50.h,
          onPressed: onTap,
          child: Row(
            children: [
              !(isPng ?? false)
                  ? SvgPicture.asset(
                      icon,
                      width: 24.w,
                      height: 24.h,
                      color: iconColor,
                    )
                  : Image.asset(
                      icon,
                      width: 24.w,
                      height: 24.h,
                    ),
              SizedBox(
                width: 16.w,
              ),
              Text(
                title,
                style: TextStyle(
                    color: textColor ??
                        Theme.of(context).textTheme.bodyLarge?.color,
                    fontSize: 16.sp),
              )
            ],
          )),
    );
  }
}
