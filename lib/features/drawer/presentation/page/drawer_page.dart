import 'dart:ui';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart' hide Trans;
import 'package:govassist/features/labor_discipline%20/presentation/page/labor_discipline_page.dart';
import 'package:isar/isar.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:theme_mode_handler/theme_mode_handler.dart';
import 'package:govassist/core/database/isar_service.dart';
import 'package:govassist/core/function/functions.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/core/widgets/logout.dart';
import 'package:govassist/di/dependency_injection.dart';
import 'package:govassist/features/auth/data/model/user_model.dart';
import 'package:govassist/features/complaint/presentation/page/complaint_page.dart';
import 'package:govassist/features/drawer/widgets/drawer_menu.dart';
import 'package:govassist/features/map/map_page.dart';
import 'package:govassist/features/payments/payment_settings.dart';
import 'package:govassist/features/profile/presentation/pages/profile_page.dart';
import 'package:govassist/features/statistics/presentation/page/statistics_page.dart';
import 'package:govassist/features/support/presentation/page/support_page.dart';
import 'package:govassist/generated/assets.dart';
import 'package:govassist/translations/locale_keys.g.dart';
import 'package:zoom_tap_animation/zoom_tap_animation.dart';

import '../../../../core/widgets/language_dialog.dart';

class DrawerPage extends StatefulWidget {
  DrawerPage({super.key});

  @override
  State<DrawerPage> createState() => _DrawerPageState();
}

class _DrawerPageState extends State<DrawerPage> {
  final maskFormatter = MaskTextInputFormatter(mask: '+998 ## ###-##-##');
  final IsarService isarService = di();

  var isEnabled = false;

  final animationDuration = Duration(milliseconds: 500);
  UserModel? userModel;

  @override
  void initState() {
    getUser();
    super.initState();
  }

  getUser() {
    userModel = isarService.isar.userModels.where().limit(1).findFirstSync();
    isEnabled = isDark();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        color: isDark() ? cCardDarkColor : cWhiteColor,
        width: MediaQuery.of(context).size.width - 70.w,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 40.h,
                ),
                ClipOval(
                  child: Material(
                    color: Colors.transparent,
                    child: IconButton(
                      iconSize: 40.w,
                      padding: EdgeInsets.zero,
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      icon: SvgPicture.asset(
                        isDark()
                            ? Assets.iconsIconCloseDark
                            : Assets.iconsClose,
                        height: 40.h,
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  height: 20.h,
                ),
                ZoomTapAnimation(
                  onTap: () {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => ProfilePage()));
                  },
                  child: Container(
                    height: 100.h,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20.r),
                        color: cFirstColor),
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 16.w),
                      child: Row(
                        children: [
                          Container(
                            width: 54.w,
                            height: 54.w,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(20.r)),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(20.r),
                              child: CachedNetworkImage(
                                imageUrl: userModel?.avatar ??
                                    "https://ui-avatars.com/api/?name=${fullName(userModel)}",
                                placeholder: (context, url) => ClipRRect(
                                  borderRadius: BorderRadius.circular(cRadius10.r),
                                  child: Container(
                                    width: 54.w,
                                    height: 54.w,
                                    color: cWhiteColor.withAlpha(100),
                                    child: SvgPicture.asset(
                                      Assets.iconsProfile,
                                      colorFilter: ColorFilter.mode(
                                          cWhiteColor, BlendMode.srcIn),
                                    ),
                                  ),
                                ),
                                errorWidget: (context, url, error) => ClipRRect(
                                  borderRadius: BorderRadius.circular(60.r),
                                  child: Container(
                                    width: 54.w,
                                    height: 54.w,
                                    color: cWhiteColor.withAlpha(100),
                                    child: SvgPicture.asset(
                                      Assets.iconsProfile,
                                      colorFilter: ColorFilter.mode(
                                          cWhiteColor, BlendMode.srcIn),
                                    ),
                                  ),
                                ),
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                          SizedBox(
                            width: 10.w,
                          ),
                          Expanded(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Flexible(
                                  child: Text(
                                    fullName(userModel),
                                    style: TextStyle(
                                        fontWeight: FontWeight.w500,
                                        fontSize:
                                            context.isTablet ? 14.sp : 16.sp,
                                        color: cWhiteColor),
                                  ),
                                ),
                                SizedBox(
                                  height: 6.h,
                                ),
                                Text(
                                    userModel?.phone != null
                                        ? maskFormatter.maskText(
                                            userModel?.phone ??
                                                "+9989XXXXXXXXX")
                                        : "+9989XXXXXXXXX",
                                    style: TextStyle(
                                        fontWeight: FontWeight.normal,
                                        fontSize:
                                            context.isTablet ? 10.sp : 14.sp,
                                        color: cWhiteColor)),
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  height: 24.h,
                ),
                Text(
                  fullAddress(userModel),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w400,
                      fontSize: context.isTablet ? 12.sp : 16.sp),
                ),
                SizedBox(
                  height: 18.h,
                ),
                Divider(
                  height: 3.h,
                  color: cGrayColor0,
                ),
                SizedBox(
                  height: 18.h,
                ),
                DrawerMenu(
                  title: LocaleKeys.statistics.tr(),
                  icon: Assets.iconsStatistics,
                  iconColor: cFirstColor,
                  onTap: () {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (_) => StatisticsPage.screen()));
                  },
                ),
                DrawerMenu(
                  title: LocaleKeys.payment.tr(),
                  icon: Assets.iconsPayment,
                  iconColor: cFirstColor,
                  onTap: () {
                    Get.to(PaymentSettings());
                  },
                ),
                DrawerMenu(
                  title: LocaleKeys.my_area.tr(),
                  icon: Assets.iconsLocation,
                  iconColor: cFirstColor,
                  onTap: () {
                    Navigator.push(
                        context, MaterialPageRoute(builder: (_) => MapPage()));
                  },
                ),
                DrawerMenu(
                  title: LocaleKeys.support_service.tr(),
                  icon: Assets.iconsSupport,
                  iconColor: cFirstColor,
                  onTap: () {
                    Navigator.push(context,
                        MaterialPageRoute(builder: (_) => SupportPage()));
                  },
                ),
                DrawerMenu(
                  title:LocaleKeys.labor_discipline.tr(),
                  icon: Assets.iconsBriefcase,
                  iconColor: cFirstColor,
                  onTap: () {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (_) => LaborDisciplinePage.screen()));
                  },
                ),
                DrawerMenu(
                  title: LocaleKeys.send_complaint.tr(),
                  icon: Assets.iconsComplaints,
                  iconColor: cFirstColor,
                  onTap: () {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (_) => ComplaintPage.screen()));
                  },
                ),

                DrawerMenu(
                  title: LocaleKeys.profile.tr(),
                  icon: Assets.iconsProfile,
                  iconColor: cFirstColor,
                  onTap: () {
                    Navigator.push(context,
                        MaterialPageRoute(builder: (_) => ProfilePage()));
                  },
                ),
                DrawerMenu(
                  title: LocaleKeys.choose_language.tr(),
                  icon: Assets.iconsGlobal,
                  iconColor: cFirstColor,
                  onTap: () {
                    showDialog(
                        context: context,
                        builder: (_) {
                          return BackdropFilter(
                              filter:
                                  ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
                              child: LanguageDialog(
                                onSelectCallBack: () async {
                                  Navigator.pop(context);
                                  Navigator.pop(context);
                                },
                              ));
                        });
                  },
                ),
                SizedBox(
                  height: 18.h,
                ),
                Divider(
                  height: 3.h,
                  color: cGrayColor0,
                ),
                SizedBox(
                  height: 18.h,
                ),
                Row(
                  children: [
                    Text(
                      LocaleKeys.day_night.tr(),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w400,
                          color: Theme.of(context).textTheme.bodyLarge?.color),
                    ),
                    SizedBox(
                      width: 40.w,
                    ),
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          isEnabled = !isEnabled;
                          if (isEnabled) {
                            ThemeModeHandler.of(context)!
                                .saveThemeMode(ThemeMode.dark);
                          } else {
                            ThemeModeHandler.of(context)!
                                .saveThemeMode(ThemeMode.light);
                          }
                        });
                      },
                      child: AnimatedContainer(
                        padding: EdgeInsets.symmetric(horizontal: 4.w),
                        height: 34.h,
                        width: 60.w,
                        duration: animationDuration,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(30.r),
                          color: isEnabled ? cFirstColor : cFirstColor,
                        ),
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            Positioned(
                                left: 2.w,
                                child: SvgPicture.asset(
                                  Assets.iconsSun,
                                  width: 16.h,
                                  height: 16.h,
                                )),
                            Positioned(
                                right: 2.w,
                                child: SvgPicture.asset(
                                  Assets.iconsMoon,
                                  width: 16.h,
                                  height: 16.h,
                                )),
                            AnimatedAlign(
                              duration: animationDuration,
                              alignment: isEnabled
                                  ? Alignment.centerRight
                                  : Alignment.centerLeft,
                              child: Padding(
                                padding: EdgeInsets.symmetric(horizontal: 1.w),
                                child: Container(
                                  alignment: Alignment.center,
                                  width: 24.h,
                                  height: 24.h,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.white,
                                  ),
                                  child: AnimatedSwitcher(
                                      duration:
                                          const Duration(milliseconds: 500),
                                      transitionBuilder: (Widget child,
                                          Animation<double> animation) {
                                        return FadeTransition(
                                            child: child, opacity: animation);
                                      },
                                      child: SvgPicture.asset(
                                        key: ValueKey<bool>(isEnabled),
                                        isEnabled
                                            ? Assets.iconsMoon
                                            : Assets.iconsSun,
                                        color: cFirstColor,
                                        width: 16.h,
                                        height: 16.h,
                                      )),
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                    )
                  ],
                ),
                SizedBox(
                  height: 10.h,
                ),
                DrawerMenu(
                  title: LocaleKeys.exit.tr(),
                  icon: Assets.iconsLogout,
                  textColor: cRedTextColor,
                  onTap: () {
                    showDialog(
                        context: context,
                        builder: (_) {
                          return LogOutDialog();
                        });
                  },
                ),
              ],
            ),
            Container(
              margin: EdgeInsets.only(bottom: 24.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text("${LocaleKeys.version.tr()}: $APP_VERSION",
                      style: TextStyle(fontSize: 12.sp, color: cGrayColor1)),
                  SizedBox(
                    height: 8.h,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        Assets.iconsC,
                        colorFilter: ColorFilter.mode(
                          Theme.of(context).disabledColor,
                          BlendMode.srcIn,
                        ),
                        width: 14.w,
                        height: 14.w,
                      ),
                      SizedBox(
                        width: 5.w,
                      ),
                      Text(
                        "PremiumSoft",
                        style: TextStyle(fontSize: 12.sp, color: cGrayColor1),
                      )
                    ],
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
