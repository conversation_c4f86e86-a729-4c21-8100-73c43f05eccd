part of 'labor_discipline_cubit.dart';

enum LaborDisciplineStatus { initial, loading, failure, success, empty }

class LaborDisciplineState extends Equatable {
  final LaborDisciplineStatus status;
  final List<LaborModel>? laborModel;
  final String? message;

  LaborDisciplineState({required this.status, this.laborModel, this.message});

  static LaborDisciplineState initial() =>
      LaborDisciplineState(status: LaborDisciplineStatus.initial);

  LaborDisciplineState copyWith(
          {LaborDisciplineStatus? status,
          List<LaborModel>? laborModel,
          String? message}) =>
      LaborDisciplineState(
          status: status ?? this.status,
          laborModel: laborModel ?? this.laborModel,
          message: this.message);

  @override
  List<Object?> get props => [status, laborModel, message];
}
