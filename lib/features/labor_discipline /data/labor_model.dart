List<LaborModel> laborList<PERSON><PERSON><PERSON>son(dynamic data) {
  return List<LaborModel>.from(data.map((e) => LaborModel.fromJson(e)));
}

class LaborModel {
  LaborModel({
    this.id,
    this.date,
    this.late,
    this.cause,
    this.createdAt,
    this.updatedAt,
  });

  LaborModel.fromJson(dynamic json) {
    id = json['_id'];
    date = json['date'];
    late = json['late'];
    cause = json['cause'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }

  String? id;
  String? date;
  dynamic late;
  dynamic cause;
  String? createdAt;
  String? updatedAt;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['_id'] = id;
    map['date'] = date;
    map['late'] = late;
    map['cause'] = cause;
    map['createdAt'] = createdAt;
    map['updatedAt'] = updatedAt;
    return map;
  }
}
