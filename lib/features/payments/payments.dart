import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart' hide Trans;
import 'package:govassist/core/function/functions.dart';
import 'package:govassist/core/network/network_info.dart';
import 'package:govassist/core/utils/api_path.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/core/widgets/custom_toast.dart';
import 'package:govassist/core/widgets/dialog_frame.dart';
import 'package:govassist/core/widgets/dotted_border.dart';
import 'package:govassist/core/widgets/header_widget.dart';
import 'package:govassist/di/dependency_injection.dart';
import 'package:govassist/features/auth/data/model/user_model.dart';
import 'package:govassist/features/payments/web_view.dart';
import 'package:govassist/generated/assets.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:govassist/translations/locale_keys.g.dart';
import 'package:zoom_tap_animation/zoom_tap_animation.dart';

import 'chrome_safari.dart';
import 'models/tariff_model.dart';
import 'widgets/expansion_item.dart';

class PaymentsPage extends StatefulWidget {
  const PaymentsPage({Key? key}) : super(key: key);

  @override
  _PaymentsPageState createState() => _PaymentsPageState();
}

class _PaymentsPageState extends State<PaymentsPage> {
  SharedPreferences prefs = di();
  TariffModel? _groupValueTariff;
  late Future<List<TariffModel>> tariffs;

  checkNetwork() async {
    NetworkInfo networkInfo = di();

    if (!await networkInfo.isConnected) {
      CustomToast.showToast(LocaleKeys.check_internet_connection.tr());
    }
  }

  @override
  void initState() {
    checkNetwork();
    tariffs = getTariffs();
    tariffs.then((value) {
      setState(() {
        _groupValueTariff = value.asMap().containsKey(0)
            ? value.first
            : TariffModel(id: 'x', price: 0, term: 0);
      });
    });
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Scaffold(
        appBar: AppHeaderWidget(
          title: LocaleKeys.make_payment.tr(),
          onBackTap: () {
            Get.back();
          },
          isBackVisible: true,
        ),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 18.w, vertical: 30.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(LocaleKeys.tariff.tr(),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontSize: context.isTablet ? 16.sp : 20.sp)),
                  SizedBox(
                    height: 10.h,
                  ),
                  ExpansionItem(
                      title:
                          "${_groupValueTariff?.term ?? '0'} ${LocaleKeys.to_month.tr()} — ${_groupValueTariff?.price ?? '0'} ${LocaleKeys.som.tr()}",
                      child: Padding(
                        padding: EdgeInsets.only(bottom: 10.h),
                        child: FutureBuilder<List<TariffModel>>(
                          future: tariffs, // async work
                          builder: (BuildContext context,
                              AsyncSnapshot<List<TariffModel>> snapshot) {
                            switch (snapshot.connectionState) {
                              // case ConnectionState.waiting:
                              //   return Text('Loading....');
                              default:
                                if (snapshot.hasError)
                                  return Text('Error: ${snapshot.error}');
                                else
                                  return Column(
                                    children: snapshot.data
                                            ?.map((TariffModel value) {
                                          return RadioListTile(
                                            title: Text(
                                              "${value.term} ${LocaleKeys.to_month.tr()} — ${value.price} ${LocaleKeys.som.tr()}",
                                              style: TextStyle(
                                                  color: Theme.of(context)
                                                      .textTheme
                                                      .bodySmall
                                                      ?.color,
                                                  fontSize: context.isTablet
                                                      ? 12.sp
                                                      : 16.sp),
                                            ),
                                            value: value,
                                            groupValue: _groupValueTariff,
                                            onChanged: (newValue) => setState(
                                                () => _groupValueTariff =
                                                    newValue as TariffModel),
                                          );
                                        }).toList() ??
                                        [],
                                  );
                            }
                          },
                        ),
                      )),
                  SizedBox(
                    height: 25.h,
                  ),
                  Text(LocaleKeys.payment_system.tr(),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontSize: context.isTablet ? 16.sp : 20.sp)),
                  SizedBox(
                    height: 10.h,
                  ),
                  Row(
                    children: [
                      Expanded(
                        flex: 1,
                        child: InkWell(
                          onTap: () => doPayment(PaymentSystem.Click),
                          child: Card(
                            elevation: 1,
                            color: cWhiteColor,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20.r)),
                            child: Center(
                              child: Container(
                                  height: 90.h,
                                  // width: 200.w,
                                  child: Image.asset(
                                    Assets.imagesClickLogo,
                                    width: 120.w,
                                    fit: BoxFit.contain,
                                  )),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(
                        width: 10.w,
                      ),
                      Expanded(
                        flex: 1,
                        child: InkWell(
                          onTap: () => doPayment(PaymentSystem.Payme),
                          child: Card(
                            elevation: 1,
                            color: cWhiteColor,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20.r)),
                            child: Center(
                              child: Container(
                                  height: 90.h,
                                  // width: 200.w,
                                  child: Image.asset(
                                    Assets.imagesPaymeLogo,
                                    width: 100.w,
                                    fit: BoxFit.contain,
                                  )),
                            ),
                          ),
                        ),
                      )
                    ],
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  doPayment(PaymentSystem paymentSystem) async {

    var price = _groupValueTariff?.price ?? 0;
    int userId = await getUserId() ?? -1;
    final ChromeSafariBrowser browser = new MyChromeSafariBrowser();
    browser.addMenuItem(new ChromeSafariBrowserMenuItem(
        id: 1,
        label: 'Hokim Yordamchisi support',
        action: (url, title) {
          launchCustomUrl(SUPPORT_TG);
        }));

    if (price > 500 && userId != -1) {
      showDialog(
          context: context,
          builder: (context) => AllDialogSkeleton(
                child: Padding(
                  padding:
                      EdgeInsets.symmetric(vertical: 20.h, horizontal: 10.w),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Expanded(
                        flex: 1,
                        child: Container(
                          child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                SizedBox(
                                  height: 10.h,
                                ),
                                ZoomTapAnimation(
                                  child: Theme(
                                    data: ThemeData(
                                      splashColor: Colors.transparent,
                                      highlightColor: Colors.transparent,
                                      scaffoldBackgroundColor:
                                          Theme.of(context).cardTheme.color,
                                    ),
                                    child: InkWell(
                                      onTap: () async {
                                        Navigator.pop(context);

                                        Uri urlRequest = Uri();

                                        if (PaymentSystem.Click ==
                                            paymentSystem) {
                                          urlRequest = Uri.https(
                                              clickPaymentPath,
                                              "/services/pay", {
                                            'service_id': CLICK_SERVICE_ID,
                                            'merchant_id': CLICK_MERCHANT_ID,
                                            'amount': price.toString(),
                                            'transaction_param':
                                                userId.toString(),
                                            'merchant_user_id':
                                                CLICK_MERCHANT_USER_ID
                                          });
                                        } else if (PaymentSystem.Payme ==
                                            paymentSystem) {
                                          String text =
                                              "m=$PAYME_MERCHANT_ID;ac.userId=${userId};a=${price}00";
                                          Codec<String, String> stringToBase64 =
                                              utf8.fuse(base64);
                                          String encoded =
                                              stringToBase64.encode(text);

                                          urlRequest = Uri.https(
                                              paymePaymentPath, encoded);
                                        }

                                        browser.open(
                                            url: WebUri.uri(urlRequest),
                                            options: ChromeSafariBrowserClassOptions(
                                                android:
                                                    AndroidChromeCustomTabsOptions(
                                                        shareState:
                                                            CustomTabsShareState
                                                                .SHARE_STATE_OFF),
                                                ios: IOSSafariOptions(
                                                    barCollapsingEnabled:
                                                        true)));
                                      },
                                      child: DottedBorderWidget(
                                        child: Center(
                                          child: Padding(
                                            padding: EdgeInsets.all(8.h),
                                            child: Column(
                                              children: [
                                                Image.asset(
                                                  Assets.imagesBrowsers,
                                                  height: 40.h,
                                                  // color: cFirstColor,
                                                ),
                                                SizedBox(
                                                  height: 10.h,
                                                ),
                                                Container(
                                                  width: 100.w,
                                                  child: Text(
                                                    LocaleKeys.outer_browser
                                                        .tr(),
                                                    textAlign: TextAlign.center,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    maxLines: 3,
                                                    style: TextStyle(
                                                        fontSize: 18.sp,
                                                        fontFamily: 'Medium',
                                                        color: Theme.of(context)
                                                            .textTheme
                                                            .bodyMedium
                                                            ?.color),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ]),
                        ),
                      ),
                      SizedBox(
                        width: 10.w,
                      ),
                      Expanded(
                        flex: 1,
                        child: Column(children: [
                          SizedBox(
                            height: 10.h,
                          ),
                          ZoomTapAnimation(
                            child: Theme(
                              data: ThemeData(
                                splashColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                scaffoldBackgroundColor:
                                    Theme.of(context).cardTheme.color,
                              ),
                              child: InkWell(
                                onTap: () {
                                  Navigator.pop(context);

                                  Navigator.push(
                                    context,
                                    CupertinoPageRoute(
                                        builder: (context) => WebViewPage(
                                              amount: price,
                                              userId: userId,
                                              paymentSystem: paymentSystem,
                                            )),
                                  );
                                },
                                child: DottedBorderWidget(
                                  child: Center(
                                    child: Padding(
                                      padding: EdgeInsets.all(8.w),
                                      child: Column(
                                        children: [
                                          Image.asset(
                                            Assets.imagesBrowser,
                                            height: 40.h,
                                            // color: cFirstColor,
                                          ),
                                          SizedBox(
                                            height: 10.h,
                                          ),
                                          Container(
                                            width: 100.w,
                                            child: Text(
                                              LocaleKeys.inner_browser.tr(),
                                              textAlign: TextAlign.center,
                                              overflow: TextOverflow.ellipsis,
                                              maxLines: 3,
                                              style: TextStyle(
                                                  fontSize: 18.sp,
                                                  fontFamily: 'Medium',
                                                  color: Theme.of(context)
                                                      .textTheme
                                                      .bodyMedium
                                                      ?.color),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ]),
                      )
                    ],
                  ),
                ),
                title: LocaleKeys.choose_browser.tr(),
                icon: Assets.iconsTickCircle,
                color: Theme.of(context).cardTheme.color,
              ));
    } else {
      CustomToast.showToast(LocaleKeys.payment_impossible.tr());
    }
  }

  Future<int?> getUserId() async {
    final Dio dio = di();
    SharedPreferences prefs = di();
    var userId = prefs.getString('id') ?? '';

    try {
      final response = await dio.get(usersPath + userId,
          options: Options(
              headers: <String, String>{'Content-Type': 'application/json'}));
      final data = response.data;
      print(data.toString());
      if (response.statusCode == 200) {
        var userInfo = UserModel.fromJson(data['user']);
        return userInfo.userId;
      } else {
        CustomToast.showToast(data.toString());
        print(data.toString());
        return null;
      }
    } on DioException catch (e) {
      if (e.type == DioExceptionType.badResponse) {
        if (e.response != null) {
          CustomToast.showToast('Server error: ' + "${e.response!.data}");
          print('Server error: ' + "${e.response!.data}");
        }
      }
      return null;
    }
  }

  Future<List<TariffModel>> getTariffs() async {
    final Dio dio = di();

    List<TariffModel> tariffs = [];

    try {
      final response = await dio.get(paymentPlans,
          options: Options(
              headers: <String, String>{'Content-Type': 'application/json'}));
      final data = response.data;
      print(data.toString());
      if (response.statusCode == 200) {
        for (int i = 0; i < (data.length); i++) {
          tariffs.add(TariffModel.fromJson(data[i]));
        }
        return tariffs;
      } else {
        CustomToast.showToast(data.toString());
        print(data.toString());
        return [];
      }
    } on DioException catch (e) {
      if (e.type == DioExceptionType.badResponse) {
        if (e.response != null) {
          CustomToast.showToast('Server error: ' + "${e.response!.data}");
          print('Server error: ' + "${e.response!.data}");
        }
      }
      return [];
    }
  }
}
