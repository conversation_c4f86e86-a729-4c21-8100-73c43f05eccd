import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:govassist/core/utils/app_constants.dart';

class ExpansionItem extends StatefulWidget {
  final String title;
  final Widget child;

  const ExpansionItem({Key? key, required this.title, required this.child})
      : super(key: key);

  @override
  State<ExpansionItem> createState() => _ExpansionItemState();
}

class _ExpansionItemState extends State<ExpansionItem> {
  @override
  Widget build(BuildContext context) {
    return Container(
      // margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 2.h),
      decoration: BoxDecoration(boxShadow: [
        BoxShadow(
          color: Colors.black.withAlpha(6),
          blurRadius: 8.r,
          spreadRadius: 8.r,
          // offset: Offset(2.0, 2.0), // shadow direction: bottom right
        )
      ], color: cWhiteColor, borderRadius: BorderRadius.circular(20.r)),
      child: Theme(
        data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
        child: ExpansionTile(
          backgroundColor: Theme.of(context).cardTheme.color,
          collapsedBackgroundColor: Theme.of(context).cardTheme.color,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15.r),
          ),
          collapsedShape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15.r),
          ),
          tilePadding: EdgeInsets.symmetric(
              horizontal: 20.w, vertical: context.isTablet ? 20.h : 2.h),
          title: Text(
            widget.title,
            style: TextStyle(
                color: Theme.of(context).primaryColor,
                fontSize: context.isTablet ? 13.sp : 17.sp),
          ),
          children: [
            Container(
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              child: widget.child,
            ),
          ],
        ),
      ),
    );
  }
}
