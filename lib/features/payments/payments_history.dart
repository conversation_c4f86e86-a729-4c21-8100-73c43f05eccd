import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart' hide Trans;
import 'package:grouped_list/grouped_list.dart';
import 'package:govassist/core/database/embedded_models.dart';
import 'package:govassist/core/function/functions.dart';
import 'package:govassist/core/network/network_info.dart';
import 'package:govassist/core/utils/api_path.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/core/widgets/custom_toast.dart';
import 'package:govassist/core/widgets/header_widget.dart';
import 'package:govassist/di/dependency_injection.dart';
import 'package:govassist/features/payments/widgets/history_item.dart';
import 'package:govassist/generated/assets.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:govassist/translations/locale_keys.g.dart';

class PaymentsHistoryPage extends StatefulWidget {
  const PaymentsHistoryPage({Key? key}) : super(key: key);

  @override
  _PaymentsHistoryPageState createState() => _PaymentsHistoryPageState();
}

class _PaymentsHistoryPageState extends State<PaymentsHistoryPage> {
  late Future<List<Payments>> userPayments;
  late List<Payments> userPaymentsStatic;
  GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();

  checkNetwork() async {
    NetworkInfo networkInfo = di();

    if (!await networkInfo.isConnected) {
      CustomToast.showToast(LocaleKeys.check_internet_connection.tr());
    }
  }

  @override
  void initState() {
    userPayments = getUserPayments();
    checkNetwork();
    userPaymentsStatic = [
      Payments(
          amount: "12 000",
          startDate: "2024-11-11",
          endDate: "2024-11-11",
          month: 3),
      Payments(
          amount: "18 000",
          startDate: "2023-11-11",
          endDate: "2023-11-11",
          month: 3)
    ];
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Scaffold(
        appBar: AppHeaderWidget(
          title: LocaleKeys.payment_history.tr(),
          onBackTap: () {
            Get.back();
          },
          isBackVisible: true,
        ),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 9,
              child: Center(
                child: FutureBuilder<List<Payments>>(
                    future: userPayments,
                    builder: (BuildContext context,
                        AsyncSnapshot<List<Payments>> snapshot) {
                      switch (snapshot.connectionState) {
                        case ConnectionState.none:
                        case ConnectionState.waiting:
                          return const CircularProgressIndicator();
                        case ConnectionState.active:
                        case ConnectionState.done:
                          if (snapshot.hasError) {
                            return Text('Error: ${snapshot.error}');
                          } else {
                            return RefreshIndicator(
                              key: _refreshIndicatorKey,
                              color: Colors.blue,
                              onRefresh: () {
                                setState(() {});
                                return userPayments = getUserPayments();
                              },
                              child: snapshot.data!.length > 0
                                  ? GroupedListView<Payments, String?>(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 18.w, vertical: 30.h),
                                      groupBy: (element) => DateTime.parse(
                                              element.startDate ?? '0000-00-00')
                                          .year
                                          .toString(),
                                      order: GroupedListOrder.DESC,
                                      elements: snapshot.data ?? [],
                                      groupSeparatorBuilder: (String? value) =>
                                          Padding(
                                        padding: const EdgeInsets.all(8.0),
                                        child: Align(
                                          alignment: Alignment.center,
                                          child: Container(
                                            decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(25.r),
                                                color: isDark()
                                                    ? cCardDarkColor
                                                    : cBlackColor
                                                        .withAlpha(20)),
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 10.w,
                                                vertical: 5.h),
                                            child: Text(
                                              '$value-${LocaleKeys.year.tr()}',
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .bodyMedium
                                                  ?.copyWith(fontSize: 16.sp),
                                            ),
                                          ),
                                        ),
                                      ),
                                      itemBuilder: (ctx, element) {
                                        return HistoryItem(
                                            paymentModel: element);
                                      },
                                      physics: AlwaysScrollableScrollPhysics(
                                          parent: BouncingScrollPhysics()),
                                    )
                                  : Column(
                                      children: [
                                        SizedBox(
                                          height: 80.h,
                                        ),
                                        Image.asset(
                                          Assets.iconsEmpty,
                                          height: 300.h,
                                        ),
                                        SizedBox(
                                          height: 20.h,
                                        ),
                                        Padding(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 20.w),
                                          child: MaterialButton(
                                            onPressed: () {
                                              setState(() {
                                                userPayments =
                                                    getUserPayments();
                                              });
                                            },
                                            child:
                                                Text(LocaleKeys.refresh.tr()),
                                            color: cFirstColor,
                                            elevation: 0,
                                            minWidth: 360.w,
                                            height: 70.h,
                                            textColor: Colors.white,
                                            shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(
                                                        cRadius16.r)),
                                          ),
                                        )
                                      ],
                                    ),
                            );
                          }
                      }
                    }),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

Future<List<Payments>> getUserPayments() async {
  final Dio dio = di();
  SharedPreferences prefs = di();
  var userId = prefs.getString('id') ?? '';
  List<Payments> payments = [];

  try {
    final response = await dio.get(usersPath + userId,
        options: Options(
            headers: <String, String>{'Content-Type': 'application/json'}));
    final data = response.data['user']?['payments'] ?? '';
    print(data.toString());
    if (response.statusCode == 200) {
      for (int i = 0; i < (data.length); i++) {
        payments.add(Payments.fromJson(data[i]));
      }
      return payments;
    } else {
      CustomToast.showToast(data.toString());
      print(data.toString());
      return [];
    }
  } catch (e) {
    print(e);
    return [];
  }

  // return Future.value([
  //   Payments(
  //       amount: "12 000",
  //       startDate: "2024-11-11",
  //       endDate: "2024-11-11",
  //       month: 3),
  //   Payments(
  //       amount: "18 000",
  //       startDate: "2023-11-11",
  //       endDate: "2023-11-11",
  //       month: 3)
  // ]);
}
