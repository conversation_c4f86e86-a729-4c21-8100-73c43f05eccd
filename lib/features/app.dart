import 'dart:async';
import 'dart:io';

import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:context_holder/context_holder.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_alice/alice.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_session_manager/flutter_session_manager.dart';
import 'package:get/get.dart';
import 'package:govassist/env.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:theme_mode_handler/theme_mode_handler.dart';
import 'package:time_change_detector/time_change_detector.dart';
import 'package:govassist/core/function/functions.dart';
import 'package:govassist/core/location/location_service.dart';
import 'package:govassist/core/theme/theme_constants.dart';
import 'package:govassist/core/theme/theme_manager.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/features/lock/definitions.dart';
import 'package:govassist/features/lock/functional_lock_page.dart';
import 'package:govassist/features/lock/lock_switcher.dart';
import 'package:govassist/di/dependency_injection.dart';
import 'package:govassist/features/login/presentation/pages/lang_select_page.dart';
import 'login/presentation/pages/intro_page.dart';

class AppProvider extends StatefulWidget {
  AppProvider({Key? key}) : super(key: key);

  @override
  State<AppProvider> createState() => _AppProviderState();
}

class _AppProviderState extends State<AppProvider> {
  final SharedPreferences prefs = di();
  var sm = SessionManager();
  Alice _alice = di();
  Offset _offset = Offset.zero;
  Offset _offsetLan = Offset.zero;
  String lan = "uz";
  bool _shouldRequestPermission = false;
  late final AppLifecycleListener _listener;
  late AndroidDeviceInfo? androidInfo;
  Stream<bool>? _controller;
  String _message = EVENT_MESSAGE_DEFAULT_UZ;
  var _subscription;

  @override
  void initState() {
    if (Platform.isAndroid) {
      androidInfo = di();
    } else {
      androidInfo = null;
    }

    print(prefs.getString('token'));

    ///Don't use if function also written in *home*
    // updateFirebaseToken();

    ///Get initial permissions
    _permissionHandler();
    // requestPermissions();

    ///It has bugs on lifecycle
    // permissionWall();
    prefs.setString(baseUrlPref,AppEnvironment.baseApiUrl);
    if (Platform.isAndroid) _initWatcher();
    _listener = AppLifecycleListener(
      onStateChange: _onStateChanged,
    );
    super.initState();
  }

  Future<void> _permissionHandler() async {
    await AwesomeNotifications().isNotificationAllowed().then((isAllowed) {
      if (!isAllowed) {
        // This is just a basic example. For real apps, you must show some
        // friendly dialog box before call the request method.
        // This is very important to not harm the user experience
        AwesomeNotifications()
            .requestPermissionToSendNotifications()
            .then((value) => requestPermissions());
      } else {
        requestPermissions();
      }
    });
  }

  _initWatcher() {
    ///***** If time set AUTOMATIC in the device, it can sink an event any time *****///

    if (_subscription != null) {
      _subscription.cancel();
    }
    var id = prefs.getString('id');
    bool isServerApproved = prefs.getBool(server_approved) ?? false;
    bool isLocalApproved = prefs.getBool(local_approved) ?? false;

    if (id != null && isServerApproved && isLocalApproved) {
      _controller ??= TimeChangeDetector.init;
      _subscription = _controller?.listen((event) {
        var now = DateTime.now();

        final DateFormat formatterDate = DateFormat('dd-MM-yyyy HH:mm');
        if (!(now.hour == 0 && now.minute == 0)) {
          prefs.setBool(is_time_correct, false);
          var lang = prefs.getString(language_pref);
          _message =
              '${lang == 'uz' ? EVENT_MESSAGE_SUCCESS_UZ : lang == 'ru' ? EVENT_MESSAGE_SUCCESS_RU : EVENT_MESSAGE_SUCCESS_QQ}: ${formatterDate.format(now)}';
          createNotification(_message);
          print(_message);
        } else {
          print(now.toString());
        }
      },
          onError: (error) => print('$ERROR: $error'),
          onDone: () => print(STREAM_COMPLETE));
    }
  }

  @override
  void dispose() {
    _listener.dispose();
    super.dispose();
  }

  // Listen to the app lifecycle state changes
  void _onStateChanged(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.detached:
        _onDetached();
        break;
      case AppLifecycleState.resumed:
        _onResumed();
        break;
      case AppLifecycleState.inactive:
        _onInactive();
        break;
      case AppLifecycleState.hidden:
        _onHidden();
        break;
      case AppLifecycleState.paused:
        _onPaused();
        break;
    }
  }

  void _onDetached() => print('detached');

  void _onResumed() async {
    if (_shouldRequestPermission) {
      _shouldRequestPermission = false;

      permissionWall();

      ///======================================
      var id = prefs.getString('id');
      bool isServerApproved = prefs.getBool(server_approved) ?? false;
      bool isLocalApproved = prefs.getBool(local_approved) ?? false;

      if (id != null && isServerApproved && isLocalApproved) {
        if (Platform.isAndroid) _initWatcher();

        var isLive = await sm.get(functional_live) ?? false;
        print('IS FUNCTIONAL LIVE: $isLive');
        if (!isLive) {
          await determinePosition();
        }
        prefs.reload().then((value) async {
          var time = prefs.getBool(is_time_correct) ?? false;
          var location = prefs.getBool(is_gps_active) ?? false;
          var mockLocation = prefs.getBool(is_not_mocked) ?? false;

          var unlocked = (location && mockLocation && time);

          print('RESUMED ----- APP');

          print('Time approved: $time\n');
          print('GPS approved: $location\n');
          print('MOCK approved: $mockLocation\n');

          if (!unlocked && !isLive) {
            Get.offAll(FunctionalLockPage());
          }
        });
      }
    }
  }

  void _onInactive() {
    _shouldRequestPermission = true;
  }

  void _onHidden() => print('hidden');

  void _onPaused() {
    _shouldRequestPermission = true;
  }

  Future<Map<Permission, PermissionStatus>> requestPermissions() async {
    var verSDK = androidInfo?.version.sdkInt ?? 0;
    Map<Permission, PermissionStatus> statuses = await [
      Platform.isAndroid && verSDK < 33
          ? Permission.storage
          : Permission.photos,
    ].request();

    return statuses;
  }

  void permissionWall() async {
    Map<Permission, PermissionStatus> statuses = {};
    var verSDK = androidInfo?.version.sdkInt ?? 0;
    statuses = await requestPermissions();

    if (statuses[Platform.isAndroid && verSDK < 33
            ? Permission.storage
            : Permission.photos] !=
        PermissionStatus.granted) {
      if (statuses[Platform.isAndroid && verSDK < 33
              ? Permission.storage
              : Permission.photos] ==
          PermissionStatus.permanentlyDenied) {
        print(statuses[Permission.storage]);

        ///Shows custom dialog after user refuses for giving of any permissions
        showCustomDialog(context);
      } else {
        ///Points to the recursion
        permissionWall();
      }
    } else {
      /// If everything is granted, do something
    }
  }

  @override
  Widget build(BuildContext context) {
    lan = prefs.getString(language_pref) ?? "uz";
    print("Language: $lan");

    String id = prefs.getString('id') ?? "";

    return ScreenUtilInit(
      splitScreenMode: true,
      builder: (BuildContext context, child) => ThemeModeHandler(
        builder: (ThemeMode themeMode) {
          return GetMaterialApp(
            useInheritedMediaQuery: true,
            navigatorKey: ContextHolder.key,
            //navigatorKey: _alice.getNavigatorKey(),
            builder: (context, widget) {
              if (_offset.dx == 0) {
                _offset = const Offset(200, 500);
              }
              if (_offsetLan.dx == 0) {
                _offsetLan = const Offset(200, 500);
              }

              ScreenUtil.init(
                context,
                designSize: const Size(DESIGN_WIDTH, DESIGN_HEIGHT),
                minTextAdapt: true,
              );
              return MediaQuery(
                data: MediaQuery.of(context).copyWith(textScaleFactor: 1.0),
                child: Stack(
                  children: [
                    widget!,
                    if (kDebugMode)
                      Visibility(
                        visible: false,
                        child: Positioned(
                          left: _offset.dx,
                          top: _offset.dy,
                          child: Visibility(
                            child: GestureDetector(
                              onPanUpdate: (d) => setState(() =>
                                  _offset += Offset(d.delta.dx, d.delta.dy)),
                              child: FloatingActionButton(
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(56),
                                ),
                                onPressed: () {
                                  ///Temporary disabled
                                  _alice.showInspector();

                                  // setState(() {
                                  //   SharedPreferences sharedPreferences = di();
                                  //   bool isDark = sharedPreferences
                                  //               .getString(theme_pref) ==
                                  //           'ThemeMode.dark'
                                  //       ? true
                                  //       : false;
                                  //
                                  //   isDark = !isDark;
                                  //   if (isDark) {
                                  //     ThemeModeHandler.of(context)!
                                  //         .saveThemeMode(ThemeMode.dark);
                                  //   } else {
                                  //     ThemeModeHandler.of(context)!
                                  //         .saveThemeMode(ThemeMode.light);
                                  //   }
                                  // });
                                },
                                backgroundColor: Colors.white.withOpacity(.5),
                                child: const Icon(Icons.http,
                                    color: Colors.green, size: 32),
                              ),
                            ),
                          ),
                        ),
                      ),
                    Visibility(
                      visible: false,
                      child: Positioned(
                        left: _offsetLan.dx,
                        top: _offsetLan.dy,
                        child: GestureDetector(
                          onPanUpdate: (d) => setState(() =>
                              _offsetLan += Offset(d.delta.dx, d.delta.dy)),
                          child: Container(
                              decoration: BoxDecoration(
                                  border: Border.all(
                                      width: 2,
                                      color: isDark()
                                          ? cWhiteColor
                                          : cBlackColor)),
                              height: 40,
                              child: Row(
                                children: [
                                  MaterialButton(
                                    onPressed: () {
                                      setState(() {
                                        context.setLocale(Locale('uz'));
                                        setLanguage('uz');
                                        Get.updateLocale(Locale("uz"));
                                      });
                                    },
                                    child: Text(
                                      "UZ",
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium,
                                    ),
                                    padding: EdgeInsets.zero,
                                    color: lan == 'uz'
                                        ? Colors.green
                                        : Colors.transparent,
                                  ),
                                  MaterialButton(
                                    onPressed: () {
                                      setState(() {
                                        context.setLocale(Locale('ru'));
                                        setLanguage('ru');
                                        Get.updateLocale(Locale("ru"));
                                      });
                                    },
                                    child: Text(
                                      "RU",
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium,
                                    ),
                                    padding: EdgeInsets.zero,
                                    color: lan == 'ru'
                                        ? Colors.green
                                        : Colors.transparent,
                                  ),
                                  MaterialButton(
                                    onPressed: () {
                                      setState(() {
                                        context.setLocale(Locale('kk'));
                                        setLanguage('kk');
                                        Get.updateLocale(Locale("kk"));
                                      });
                                    },
                                    child: Text(
                                      "QQ",
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium,
                                    ),
                                    padding: EdgeInsets.zero,
                                    color: lan == 'kk'
                                        ? Colors.green
                                        : Colors.transparent,
                                  )
                                ],
                              )),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
            debugShowCheckedModeBanner: false,
            home: startAnimator(id),
            themeMode: themeMode,
            theme: lightTheme,
            darkTheme: darkTheme,
            localizationsDelegates: context.localizationDelegates,
            supportedLocales: context.supportedLocales,
            locale: context.locale,
          );
        },
        manager: MyManager(),
      ),
    );
  }

  Widget startAnimator(String id) {
    ///Setting orientation for the tablet
    context.isTablet ? setDown() : setUp();

    return IntroPage(
      child: id == '' ? LangSelectPage() : LockProvider(),
    );
  }
}
