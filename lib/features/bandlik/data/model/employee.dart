import 'package:govassist/core/database/embedded_models.dart';

class Employee {
  Employee({
    this.employeeItem,
    this.totalDocs,
    this.limit,
    this.totalPages,
    this.page,
    this.pagingCounter,
    this.hasPrevPage,
    this.hasNextPage,
    this.prevPage,
    this.nextPage,
    this.employment,
    this.unemployment,});

  Employee.fromJson(dynamic json) {
    if (json['docs'] != null) {
      employeeItem = [];
      json['docs'].forEach((v) {
        employeeItem?.add(EmployeeItem.fromJson(v));
      });
    }
    totalDocs = json['totalDocs'];
    limit = json['limit'];
    totalPages = json['totalPages'];
    page = json['page'];
    pagingCounter = json['pagingCounter'];
    hasPrevPage = json['hasPrevPage'];
    hasNextPage = json['hasNextPage'];
    prevPage = json['prevPage'];
    nextPage = json['nextPage'];
    employment = json['employment'];
    unemployment = json['unemployment'];
  }
  List<EmployeeItem>? employeeItem;
  int? totalDocs;
  int? limit;
  int? totalPages;
  int? page;
  int? pagingCounter;
  bool? hasPrevPage;
  bool? hasNextPage;
  dynamic prevPage;
  dynamic nextPage;
  int? employment;
  int? unemployment;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (employeeItem != null) {
      map['docs'] = employeeItem?.map((v) => v.toJson()).toList();
    }
    map['totalDocs'] = totalDocs;
    map['limit'] = limit;
    map['totalPages'] = totalPages;
    map['page'] = page;
    map['pagingCounter'] = pagingCounter;
    map['hasPrevPage'] = hasPrevPage;
    map['hasNextPage'] = hasNextPage;
    map['prevPage'] = prevPage;
    map['nextPage'] = nextPage;
    map['employment'] = employment;
    map['unemployment'] = unemployment;
    return map;
  }
}

class EmployeeItem {
  EmployeeItem({
    this.user,
    this.id,
    this.firstName,
    this.lastName,
    this.middleName,
    this.province,
    this.region,
    this.district,
    this.address,
    this.birthDate,
    this.gender,
    this.pinfl,
    this.phone,
    this.information,
    this.specialization,
    this.employment,
    this.employmentDate,
    this.employmentType,
    this.certificateNumber,
    this.certificateDate,
    this.companyName,
    this.position,
    this.orderNumber,
    this.orderDate,
    this.creditPurpose,
    this.creditAmount,
    this.ssudaPurpose,
    this.ssudaAmount,
    this.active,
    this.createdAt,
    this.updatedAt,});

  EmployeeItem.fromJson(dynamic json) {
    user = json['user'] != null ? User.fromJson(json['user']) : null;
    id = json['_id'];
    firstName = json['firstName'];
    lastName = json['lastName'];
    middleName = json['middleName'];
    province = json['province'] != null ? Province.fromJson(json['province']) : null;
    region = json['region'] != null ? Region.fromJson(json['region']) : null;
    district = json['district'] != null ? District.fromJson(json['district']) : null;
    address = json['address'];
    birthDate = json['birthDate'];
    gender = json['gender'];
    pinfl = json['pinfl'];
    phone = json['phone'];
    information = json['information'];
    specialization = json['specialization'];
    employment = json['employment'];
    employmentDate = json['employmentDate'];
    employmentType = json['employmentType'] != null ? EmploymentType.fromJson(json['employmentType']) : null;
    certificateNumber = json['certificateNumber'];
    certificateDate = json['certificateDate'];
    companyName = json['companyName'];
    position = json['position'];
    orderNumber = json['orderNumber'];
    orderDate = json['orderDate'];
    creditPurpose = json['creditPurpose'];
    creditAmount = json['creditAmount'];
    ssudaPurpose = json['SsudaPurpose'];
    ssudaAmount = json['SsudaAmount'];
    active = json['active'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }
  User? user;
  String? id;
  String? firstName;
  String? lastName;
  String? middleName;
  Province? province;
  Region? region;
  District? district;
  String? address;
  String? birthDate;
  String? gender;
  String? pinfl;
  String? phone;
  String? information;
  String? specialization;
  bool? employment;
  dynamic employmentDate;
  EmploymentType? employmentType;
  dynamic certificateNumber;
  dynamic certificateDate;
  dynamic companyName;
  dynamic position;
  dynamic orderNumber;
  dynamic orderDate;
  dynamic creditPurpose;
  dynamic creditAmount;
  dynamic ssudaPurpose;
  dynamic ssudaAmount;
  bool? active;
  String? createdAt;
  String? updatedAt;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (user != null) {
      map['user'] = user?.toJson();
    }
    map['_id'] = id;
    map['firstName'] = firstName;
    map['lastName'] = lastName;
    map['middleName'] = middleName;
    if (province != null) {
      map['province'] = province?.toJson();
    }
    if (region != null) {
      map['region'] = region?.toJson();
    }
    if (district != null) {
      map['district'] = district?.toJson();
    }
    map['address'] = address;
    map['birthDate'] = birthDate;
    map['gender'] = gender;
    map['pinfl'] = pinfl;
    map['phone'] = phone;
    map['information'] = information;
    map['specialization'] = specialization;
    map['employment'] = employment;
    map['employmentDate'] = employmentDate;
    if (employmentType != null) {
      map['employmentType'] = employmentType?.toJson();
    }
    map['certificateNumber'] = certificateNumber;
    map['certificateDate'] = certificateDate;
    map['companyName'] = companyName;
    map['position'] = position;
    map['orderNumber'] = orderNumber;
    map['orderDate'] = orderDate;
    map['creditPurpose'] = creditPurpose;
    map['creditAmount'] = creditAmount;
    map['SsudaPurpose'] = ssudaPurpose;
    map['SsudaAmount'] = ssudaAmount;
    map['active'] = active;
    map['createdAt'] = createdAt;
    map['updatedAt'] = updatedAt;
    return map;
  }
}

class EmploymentType {
  EmploymentType({
    this.id,
    this.titleUZ,
    this.titleRU,
    this.titleQQ,});

  EmploymentType.fromJson(dynamic json) {
    id = json['_id'];
    titleUZ = json['titleUZ'];
    titleRU = json['titleRU'];
    titleQQ = json['titleQQ'];
  }
  String? id;
  String? titleUZ;
  String? titleRU;
  String? titleQQ;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['_id'] = id;
    map['titleUZ'] = titleUZ;
    map['titleRU'] = titleRU;
    map['titleQQ'] = titleQQ;
    return map;
  }
}

class District {
  District({
    this.id,
    this.titleUZ,
    this.titleRU,
    this.titleQQ,});

  District.fromJson(dynamic json) {
    id = json['_id'];
    titleUZ = json['titleUZ'];
    titleRU = json['titleRU'];
    titleQQ = json['titleQQ'];
  }
  String? id;
  String? titleUZ;
  String? titleRU;
  String? titleQQ;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['_id'] = id;
    map['titleUZ'] = titleUZ;
    map['titleRU'] = titleRU;
    map['titleQQ'] = titleQQ;
    return map;
  }
}

class User {
  User({
    this.id,
    this.firstName,
    this.lastName,
    this.middleName,});

  User.fromJson(dynamic json) {
    id = json['_id'];
    firstName = json['firstName'];
    lastName = json['lastName'];
    middleName = json['middleName'];
  }
  String? id;
  String? firstName;
  String? lastName;
  String? middleName;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['_id'] = id;
    map['firstName'] = firstName;
    map['lastName'] = lastName;
    map['middleName'] = middleName;
    return map;
  }
}