class EmploymentTypeInCategory {
  final String id;
  final String titleUZ;
  final String titleRU;
  final String titleQQ;
  final bool? certificateNumber;
  final bool? certificateDate;
  final bool? companyName;
  final bool? position;
  final bool? orderNumber;
  final bool? orderDate;
  final bool? creditPurpose;
  final bool? creditAmount;
  final bool? ssudaPurpose;
  final bool? ssudaAmount;

  EmploymentTypeInCategory({
    required this.id,
    required this.titleUZ,
    required this.titleRU,
    required this.titleQQ,
    this.certificateNumber,
    this.certificateDate,
    this.companyName,
    this.position,
    this.orderNumber,
    this.orderDate,
    this.creditPurpose,
    this.creditAmount,
    this.ssudaPurpose,
    this.ssudaAmount,
  });

  factory EmploymentTypeInCategory.fromJson(Map<String, dynamic> json) {
    return EmploymentTypeInCategory(
      id: json['_id'] ?? '',
      titleUZ: json['titleUZ'] ?? '',
      titleRU: json['titleRU'] ?? '',
      titleQQ: json['titleQQ'] ?? '',
      certificateNumber: json['certificateNumber'],
      certificateDate: json['certificateDate'],
      companyName: json['companyName'],
      position: json['position'],
      orderNumber: json['orderNumber'],
      orderDate: json['orderDate'],
      creditPurpose: json['creditPurpose'],
      creditAmount: json['creditAmount'],
      ssudaPurpose: json['SsudaPurpose'],
      ssudaAmount: json['SsudaAmount'],
    );
  }
}