import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:govassist/core/function/functions.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/features/bandlik/data/model/employee.dart';
import 'package:govassist/translations/locale_keys.g.dart';
import 'package:zoom_tap_animation/zoom_tap_animation.dart';

class JobLessWidget extends StatelessWidget {
  final VoidCallback onDetailTap;
  final VoidCallback onEmployTap;
  final EmployeeItem employeeItem;
  final VoidCallback onTap;

  const JobLessWidget(
      {super.key,
      required this.onDetailTap,
      required this.onEmployTap,
      required this.employeeItem,
      required this.onTap});

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(cRadius22.r),
      child: Material(
        child: InkWell(
          onTap: () {
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(cRadius22.r),
                color: isDark() ? cCardDarkColor : cWhiteColor,
                boxShadow: [boxShadow5]),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "${employeeItem.firstName} ${employeeItem.lastName} ${employeeItem.middleName}",
                  style: Theme.of(context)
                      .textTheme
                      .bodyMedium
                      ?.copyWith(fontWeight: FontWeight.bold),
                ),
                SizedBox(
                  height: 10.h,
                ),
                Text(
                  "${LocaleKeys.address.tr()}:",
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                SizedBox(
                  height: 10.h,
                ),
                Text(
                  "${getProvince(province: employeeItem.province)} ${getRegion(region: employeeItem.region)} ${employeeItem.address}",
                  style: Theme.of(context)
                      .textTheme
                      .bodyMedium
                      ?.copyWith(fontWeight: FontWeight.w400),
                ),
                SizedBox(
                  height: 10.h,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    ZoomTapAnimation(
                        onTap: () {
                          onDetailTap();
                        },
                        child: Text(
                          LocaleKeys.detailed.tr(),
                          style: TextStyle(color: cFirstColor),
                        )),
                    employeeItem.employment == true
                        ? SizedBox()
                        : ZoomTapAnimation(
                            onTap: () {
                              onEmployTap();
                            },
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 10.w, vertical: 2.h),
                              decoration: BoxDecoration(
                                  borderRadius:
                                      BorderRadius.circular(cRadius22.r),
                                color: cFirstColor),
                              child: Text(
                                LocaleKeys.make_employment.tr(),
                                style: TextStyle(
                                    color:cWhiteColor,
                                    fontWeight: FontWeight.w500),
                              ),
                            ),
                          )
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
