import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:govassist/core/function/functions.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/generated/assets.dart';
import 'package:badges/badges.dart' as badges;
import 'package:govassist/translations/locale_keys.g.dart';

PreferredSize BandlikHeaderWidget({required VoidCallback onHamburgerTap,
  required VoidCallback onActionTap,
  required Function(String text) onSearch}) {
  return PreferredSize(
    preferredSize: Size.fromHeight(HEADER_SIZE.h),
    child: HeaderBadlikMain(
      onHamburgerTap: onHamburgerTap,
      onActionTap: onActionTap,
      onSearch: (String text) {
        onSearch(text);
      },
    ), // Set this height
  );
}

class HeaderBadlikMain extends StatefulWidget {
  final VoidCallback onHamburgerTap;
  final VoidCallback onActionTap;
  final Function(String text) onSearch;

  HeaderBadlikMain({super.key,
    required this.onHamburgerTap,
    required this.onActionTap,
    required this.onSearch});

  @override
  State<HeaderBadlikMain> createState() => _HeaderBadlikMainState();
}

class _HeaderBadlikMainState extends State<HeaderBadlikMain>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _searchFieldController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _searchFieldAnimation;

  bool isSearchShow = false;
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();

    // Main animation controller for show/hide transitions
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    // Search field animation controller
    _searchFieldController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Fade animation for opacity changes
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Scale animation for search field
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    // Slide animation for search field
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, -1.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    // Search field scale animation
    _searchFieldAnimation = Tween<double>(
      begin: 0.9,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _searchFieldController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchFieldController.dispose();
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _showSearchPanel() {
    widget.onActionTap();
    setState(() {
      isSearchShow = true;
    });
    _animationController.forward().then((_) {
      _searchFieldController.forward();
      // Auto-focus the search field after animation
      Future.delayed(const Duration(milliseconds: 100), () {
        _searchFocusNode.requestFocus();
      });
    });
  }

  void _hideSearchPanel() {
    widget.onActionTap();
    _searchFieldController.reverse().then((_) {
      _animationController.reverse().then((_) {
        setState(() {
          isSearchShow = false;
        });
        // Clear search text when hiding
        _searchController.clear();
        _searchFocusNode.unfocus();
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.only(
          bottomRight: Radius.circular(20.r),
          bottomLeft: Radius.circular(20.r)),
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        transitionBuilder: (Widget child, Animation<double> animation) {
          return FadeTransition(
            opacity: animation,
            child: ScaleTransition(
              scale: animation,
              child: child,
            ),
          );
        },
        child: isSearchShow ? _buildSearchView() : _buildNormalView(),
      ),
    );
  }

  Widget _buildSearchView() {
    return Container(
      key: const ValueKey('search'),
      width: MediaQuery
          .of(context)
          .size
          .width,
      padding: EdgeInsets.symmetric(horizontal: 10.w),
      decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: isDark()
                ? [cCardDarkColor, cCardDarkColor]
                : [
              cSecondColor,
              cFirstColor,
            ],
          ),
          borderRadius: BorderRadius.only(
              bottomRight: Radius.circular(20.r),
              bottomLeft: Radius.circular(20.r))),
      child: Stack(
        alignment: Alignment.center,
        children: [
        SvgPicture.asset(
        Assets.iconsWeb,
        width: MediaQuery
            .of(context)
            .size
            .width,
        fit: BoxFit.fill,
      ),
      FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: ScaleTransition(
            scale: _searchFieldAnimation,
            child: Padding(
              padding: EdgeInsets.only(
                  left: 5.w, right: 5.w, top: 40.h, bottom: 10.h),
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 14.w),
                height: 60.h,
                width: MediaQuery
                    .of(context)
                    .size
                    .width,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(cRadius36.r),
                    color: isDark() ? cFourthColorDark : cWhiteColor,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ]),
                child: Container(
                  alignment: Alignment.center,
                  child: TextField(
                      controller: _searchController,
                      focusNode: _searchFocusNode,
                      textAlignVertical: TextAlignVertical.center,
                      onChanged: (value) {
                        widget.onSearch(value);
                        // Handle search text changes
                        // You can add your search logic here
                      },
                    cursorColor: cFirstColor,
                      decoration: InputDecoration(
                          fillColor: isDark() ?cFourthColorDark : cWhiteColor,
                          border: InputBorder.none,
                          hintText: LocaleKeys.search.tr(),
                          hintStyle: Theme.of(context)
                          .textTheme
                          .bodyMedium
                          ?.copyWith(),
                      suffixIcon: AnimatedContainer(
                        duration: const Duration(milliseconds: 200),
                        child: Material(
                          color: cGrayColor0.withValues(alpha: 0.4),
                          shape: CircleBorder(),
                          child: IconButton(
                            onPressed: _hideSearchPanel,
                            iconSize: 40.h,
                            icon: AnimatedRotation(
                              turns: isSearchShow ? 0.25 : 0.0,
                              duration: const Duration(milliseconds: 200),
                              child: SvgPicture.asset(
                                Assets.iconsXIcon,
                                width: 20.h,
                                height: 20.h,
                                color: isDark()?cWhiteColor:cGrayColor1,
                              ),
                            ),
                          ),
                        ),
                      )),
                ),
              ),
            ),
          ),
        ),
      ),
    ),]
    ,
    )
    ,
    );
  }

  Widget _buildNormalView() {
    return Container(
      key: const ValueKey('normal'),
      width: MediaQuery
          .of(context)
          .size
          .width,
      decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: isDark()
                ? [cCardDarkColor, cCardDarkColor]
                : [
              cSecondColor,
              cFirstColor,
            ],
          ),
          borderRadius: BorderRadius.only(
              bottomRight: Radius.circular(20.r),
              bottomLeft: Radius.circular(20.r))),
      child: Stack(
        alignment: Alignment.center,
        children: [
          SvgPicture.asset(
            Assets.iconsWeb,
            width: MediaQuery
                .of(context)
                .size
                .width,
            fit: BoxFit.fill,
          ),
          Padding(
            padding:
            EdgeInsets.only(left: 5.w, right: 5.w, top: 40.h, bottom: 10.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: AnimatedScale(
                    scale: 1.0,
                    duration: const Duration(milliseconds: 200),
                    child: Material(
                      color: Colors.transparent,
                      shape: const CircleBorder(),
                      clipBehavior: Clip.hardEdge,
                      child: IconButton(
                        onPressed: widget.onHamburgerTap,
                        iconSize: 40.h,
                        icon: SvgPicture.asset(
                          Assets.iconsHamburger,
                          width: 40.h,
                          height: 40.h,
                        ),
                      ),
                    ),
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: AnimatedDefaultTextStyle(
                    duration: const Duration(milliseconds: 300),
                    style: TextStyle(
                      color: cWhiteColor,
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w600,
                    ),
                    child:  Text(
                      textAlign: TextAlign.center,
                      LocaleKeys.employment.tr(),
                    ),
                  ),
                ),
                Expanded(
                  child: AnimatedScale(
                    scale: 1.0,
                    duration: const Duration(milliseconds: 200),
                    child: Container(
                      height: 60.h,
                      width: 60.h,
                      child: IconButton(
                        onPressed: _showSearchPanel,
                        iconSize: 40.h,
                        icon: AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          height: 40.h,
                          width: 40.h,
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                          ),
                          child: SvgPicture.asset(
                            Assets.iconsSearch,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
