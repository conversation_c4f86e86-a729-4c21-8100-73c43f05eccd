import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:govassist/core/network/network_info.dart';
import 'package:govassist/core/utils/api_path.dart';
import 'package:govassist/di/dependency_injection.dart';
import 'package:govassist/translations/locale_keys.g.dart';

part 'add_person_event.dart';

part 'add_person_state.dart';

class AddPersonBloc extends Bloc<AddPersonEvent, AddPersonState> {
  final NetworkInfo networkInfo = di();
  final Dio dio = di();

  AddPersonBloc() : super(AddPersonState.initial()) {
    on<AddUnEmployeedPersonEvent>(addUnEmployeedPerson);
  }

  Future<void> addUnEmployeedPerson(
      AddUnEmployeedPersonEvent event, Emitter<AddPersonState> emit) async {
    emit(AddPersonState(status: AddPersonStatus.loading));
    if (await networkInfo.isConnected) {
      try {
        var response = await dio.post(addUnemploymentsPath, data: event.map);
        if (response.statusCode == 200 || response.statusCode == 201) {
          emit(AddPersonState(status: AddPersonStatus.success));
        }
      } on DioException catch (e) {
        if (e.type == DioExceptionType.connectionTimeout) {
          emit(AddPersonState(
              status: AddPersonStatus.failure, message: "Timeout exception"));
        } else {
          emit(AddPersonState(
              status: AddPersonStatus.failure,
              message:e.response?.data??""));
        }
      } catch (e) {
        emit(AddPersonState(
            status: AddPersonStatus.failure,
            message: LocaleKeys.unknown_server_error.tr()));
      }
    } else {
      emit(AddPersonState(
          status: AddPersonStatus.failure,
          message: LocaleKeys.check_internet_connection.tr()));
    }
  }
}
