part of 'add_person_bloc.dart';

enum AddPersonStatus { initial, loading, success, failure }

class AddPersonState extends Equatable {
  final AddPersonStatus status;
  final String? message;

  AddPersonState({required this.status, this.message});

  static AddPersonState initial() =>
      AddPersonState(status: AddPersonStatus.initial);

  AddPersonState copyWith({required AddPersonStatus status, String? message}) {
    return AddPersonState(status: status, message: message ?? this.message);
  }

  @override
  List<Object?> get props => [status, message];
}
