part of 'get_persons_bloc.dart';

sealed class GetPersonsEvent extends Equatable {
  const GetPersonsEvent();
}

class GetEmployeeEvent extends GetPersonsEvent {
  final int page;
  final int limit;
  final bool employment;
  final String? search;

  final bool? refresh;

  GetEmployeeEvent(
      {required this.page, required this.limit, required this.employment, this.search, this.refresh});

  @override
  List<Object?> get props => [page, limit, employment, search, refresh];
}

class ReloadEmployeeEvent extends GetPersonsEvent {
  @override
  List<Object> get props => [];
}