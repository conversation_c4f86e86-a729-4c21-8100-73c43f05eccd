import 'package:bloc/bloc.dart';
import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:govassist/core/network/network_info.dart';
import 'package:govassist/core/utils/api_path.dart';
import 'package:govassist/di/dependency_injection.dart';
import 'package:govassist/features/bandlik/data/model/employee.dart';
import 'package:govassist/translations/locale_keys.g.dart';

part 'get_persons_event.dart';

part 'get_persons_state.dart';

class GetPersonsBloc extends Bloc<GetPersonsEvent, GetPersonsState> {
  final NetworkInfo networkInfo = di();
  final Dio dio = di();

  GetPersonsBloc() : super(GetPersonsState.initial()) {
    on<GetEmployeeEvent>(getEmployee, transformer: droppable());
    on<ReloadEmployeeEvent>(reloadEmployee, transformer: droppable());
  }

  Future<void> reloadEmployee(
      ReloadEmployeeEvent event, Emitter<GetPersonsState> emit) async {
    emit(GetPersonsState(status: GetPersonsStatus.reload));
  }

  Future<void> getEmployee(
      GetEmployeeEvent event, Emitter<GetPersonsState> emit) async {
    if (event.refresh == true) {
      emit(GetPersonsState(status: GetPersonsStatus.loading));
    }
    if (await networkInfo.isConnected) {
      Map<String, dynamic> data = {
        'limit': event.limit,
        'page': event.page,
        'employment': event.employment
      };
      try {
        var response = await dio.post(getPersonsPath, data: data);
        if (response.statusCode == 200 || response.statusCode == 201) {
          Employee employee = Employee.fromJson(response.data);
          emit(GetPersonsState(
              status: GetPersonsStatus.success, employee: employee));
        }
      } on DioException catch (e) {
        if (e.type == DioExceptionType.connectionTimeout) {
          emit(GetPersonsState(
              status: GetPersonsStatus.failure, message: "Timeout exception"));
        } else {
          emit(GetPersonsState(
              status: GetPersonsStatus.failure,
              message: LocaleKeys.unknown_error_retry.tr()));
        }
      } catch (e) {
        emit(GetPersonsState(
            status: GetPersonsStatus.failure,
            message: LocaleKeys.unknown_server_error.tr()));
      }
    } else {
      emit(GetPersonsState(
          status: GetPersonsStatus.failure,
          message: LocaleKeys.check_internet_connection.tr()));
    }
  }
}
