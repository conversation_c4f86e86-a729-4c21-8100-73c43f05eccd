part of 'get_persons_bloc.dart';

enum GetPersonsStatus { initial, loading, success, failure,reload}

class GetPersonsState extends Equatable {
  final GetPersonsStatus status;
  final Employee? employee;
  final String? message;
  GetPersonsState(
      {required this.status, this.employee, this.message});

  static GetPersonsState initial() =>
      GetPersonsState(status: GetPersonsStatus.initial);

  GetPersonsState copyWith(
      {required GetPersonsStatus status,
      Employee? employee,
      String? message,
      bool? refresh}) {
    return GetPersonsState(
        status: status,
        employee: employee ?? this.employee,
        message: message ?? this.message);
  }

  @override
  List<Object?> get props => [status, employee, message];
}
