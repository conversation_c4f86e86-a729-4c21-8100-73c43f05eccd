part of 'search_bloc.dart';

enum SearchStatus { initial, loading, success, failure }

class SearchState extends Equatable {
  final SearchStatus status;
  final List<EmployeeItem>? list;
  final String? message;

  SearchState({required this.status, this.list, this.message});

  static SearchState initial() => SearchState(status: SearchStatus.initial);

  SearchState copyWith(
      {required SearchStatus status,
      List<EmployeeItem>? list,
      String? message}) {
    return SearchState(
        status: status,
        list: list ?? this.list,
        message: message ?? this.message);
  }

  @override
  List<Object?> get props => [status,list, message];
}
