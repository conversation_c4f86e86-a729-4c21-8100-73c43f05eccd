import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:govassist/core/network/network_info.dart';
import 'package:govassist/core/utils/api_path.dart';
import 'package:govassist/di/dependency_injection.dart';
import 'package:govassist/features/bandlik/data/model/employee.dart';
import 'package:govassist/translations/locale_keys.g.dart';
import 'package:rxdart/rxdart.dart';

part 'search_event.dart';

part 'search_state.dart';

class SearchBloc extends Bloc<SearchEvent, SearchState> {
  final NetworkInfo networkInfo = di();
  final Dio dio = di();

  SearchBloc() : super(SearchState.initial()) {
    on<SearchPersonEvent>(search,
        transformer: debounce(const Duration(milliseconds: 500)));
    on<SearchInitial>(initial);
  }

  Future<void> initial(SearchInitial event, Emitter<SearchState> emit) async {
    emit(SearchState(status: SearchStatus.success, list: []));
  }

  Future<void> search(
      SearchPersonEvent event, Emitter<SearchState> emit) async {
    emit(SearchState(status: SearchStatus.loading));
    if (await networkInfo.isConnected) {
      try {
        if (event.searchText.isEmpty) {
          emit(SearchState(status: SearchStatus.success, list: []));
        } else {
          var response = await dio
              .post(getPersonSearchPath, data: {"search": event.searchText});
          if (response.statusCode == 200 || response.statusCode == 201) {
            List<EmployeeItem> list = (response.data as List)
                .map((e) => EmployeeItem.fromJson(e))
                .toList();
            print(list);
            emit(SearchState(status: SearchStatus.success, list: list));
          }
        }
      } on DioException catch (e) {
        if (e.type == DioExceptionType.connectionTimeout) {
          emit(SearchState(
              status: SearchStatus.failure, message: "Timeout exception"));
        } else {
          emit(SearchState(
              status: SearchStatus.failure,
              message: LocaleKeys.unknown_error_retry.tr()));
        }
      } catch (e) {
        emit(SearchState(
            status: SearchStatus.failure,
            message: LocaleKeys.unknown_server_error.tr()));
      }
    } else {
      emit(SearchState(
          status: SearchStatus.failure,
          message: LocaleKeys.check_internet_connection.tr()));
    }
  }

  EventTransformer<T> debounce<T>(Duration duration) {
    return (events, mapper) => events.debounceTime(duration).flatMap((mapper));
  }
}
