import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart' hide Trans;
import 'package:govassist/core/database/embedded_models.dart';
import 'package:govassist/core/database/isar_service.dart';
import 'package:govassist/core/function/functions.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/core/widgets/custom_toast.dart';
import 'package:govassist/di/dependency_injection.dart';
import 'package:govassist/features/auth/data/model/user_model.dart';
import 'package:govassist/features/bandlik/presentation/bloc/add_person_bloc/add_person_bloc.dart';
import 'package:govassist/generated/assets.dart';
import 'package:govassist/translations/locale_keys.g.dart';
import 'package:intl/intl.dart';
import 'package:isar/isar.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

class AddPerson extends StatefulWidget {
  static Widget screen() {
    return BlocProvider(
      create: (context) => AddPersonBloc(),
      child: AddPerson(),
    );
  }

  const AddPerson({super.key});

  @override
  State<AddPerson> createState() => _AddPersonState();
}

class _AddPersonState extends State<AddPerson> with TickerProviderStateMixin {
  var maskFormatter = MaskTextInputFormatter(mask: '(##) ###-##-##');

  late AnimationController _animationController;
  late AnimationController _dragAnimationController;
  late Animation<double> _animation;
  late Animation<double> _dragAnimation;
  double _dragExtent = 0.0;
  double _initialHeight = 0.0;
  bool _isExpanded = false;
  bool _isDragging = false;

  // Form controllers - Fixed controller assignments
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _surnameController = TextEditingController();
  final TextEditingController _fatherNameController = TextEditingController();
  final TextEditingController _motherNameController = TextEditingController();
  final TextEditingController _birthDateController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _streetController = TextEditingController();
  final TextEditingController _houseNumberController = TextEditingController();
  final TextEditingController _homeAddressController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _specialtyController = TextEditingController();
  final TextEditingController _idNumberController = TextEditingController();
  final TextEditingController _educationLeveController =
      TextEditingController();

  // Dropdown selections - Fixed variable names
  Province? _selectedProvince;
  Region? _selectedCity;
  String? _selectedGender;
  String? _selectedEducationLevel;

  List<Province> province = [];
  List<Region> region = [];

  // Form key for validation
  final GlobalKey<FormState> _formKey2 = GlobalKey<FormState>();
  UserModel? userModel;
  final IsarService isarService = di();

  getUser() {
    userModel = isarService.isar.userModels.where().limit(1).findFirstSync();
    province.add(userModel?.province ?? Province());
    region.add(userModel?.region ?? Region());
    _selectedProvince = province[0]; // getProvince(province: );
    _selectedCity = region[0]; //getRegion(region: region[0]);
  }

  @override
  void initState() {
    super.initState();
    getUser();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _dragAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _dragAnimation = Tween<double>(
      begin: 0.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _dragAnimationController,
      curve: Curves.easeOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    _dragAnimationController.dispose();
    _nameController.dispose();
    _surnameController.dispose();
    _fatherNameController.dispose();
    _motherNameController.dispose();
    _birthDateController.dispose();
    _addressController.dispose();
    _streetController.dispose();
    _houseNumberController.dispose();
    _homeAddressController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _specialtyController.dispose();
    _idNumberController.dispose();
    super.dispose();
  }

  void _handleDragStart(DragStartDetails details) {
    setState(() {
      _isDragging = true;
      _initialHeight = _isExpanded ? 0.95 : 0.8;
    });
    _dragAnimationController.stop();
  }

  void _handleDragUpdate(DragUpdateDetails details) {
    if (!_isDragging) return;

    setState(() {
      // Smooth drag with limited range
      _dragExtent += details.delta.dy * 0.8;

      // Strict clamping to prevent background exposure
      _dragExtent = _dragExtent.clamp(-30.0, 80.0);
    });
  }

  void _handleDragEnd(DragEndDetails details) {
    if (!_isDragging) return;

    setState(() {
      _isDragging = false;
    });

    final velocity = details.velocity.pixelsPerSecond.dy;

    // Consider both drag distance and velocity for better UX
    if (_dragExtent < -30 || velocity < -500) {
      // Expand
      if (!_isExpanded) {
        _expandModal();
      } else {
        _snapBack();
      }
    } else if (_dragExtent > 30 || velocity > 500) {
      // Collapse or close
      if (_isExpanded) {
        _collapseModal();
      } else {
        _closeModal();
      }
    } else {
      // Snap back to current position
      _snapBack();
    }
  }

  void _snapBack() {
    _dragAnimation = Tween<double>(
      begin: _dragExtent,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _dragAnimationController,
      curve: Curves.easeOut,
    ));

    _dragAnimationController.forward(from: 0.0).then((_) {
      setState(() {
        _dragExtent = 0.0;
      });
    });
  }

  void _closeModal() {
    _dragAnimation = Tween<double>(
      begin: _dragExtent,
      end: 300.0,
    ).animate(CurvedAnimation(
      parent: _dragAnimationController,
      curve: Curves.easeIn,
    ));

    _dragAnimationController.forward(from: 0.0).then((_) {
      Navigator.pop(context);
    });
  }

  void _expandModal() {
    setState(() {
      _isExpanded = true;
      _dragExtent = 0.0;
    });
    _animationController.forward();
  }

  void _collapseModal() {
    setState(() {
      _isExpanded = false;
      _dragExtent = 0.0;
    });
    _animationController.reverse();
  }

  // Validation function
  bool _validateForm() {
    if (!_formKey2.currentState!.validate()) {
      return false;
    }

    // Check required dropdowns
    if (_selectedProvince == null) {
      CustomToast.showToast(LocaleKeys.select_region.tr());
      return false;
    }
    if (_selectedCity == null) {
      CustomToast.showToast(LocaleKeys.select_city.tr());
      return false;
    }
    if (_selectedGender == null) {
      CustomToast.showToast(LocaleKeys.select_gender.tr());
      return false;
    }
    return true;
  }

// Helper function to validate JSHSHIR
  bool _isValidJSHSHIR(String jshshir) {
    // Remove any spaces or formatting
    String cleanJshshir = jshshir.replaceAll(RegExp(r'[^0-9]'), '');

    // Check if it's exactly 14 digits
    if (cleanJshshir.length != 14) {
      return false;
    }

    // Additional validation: check if all digits are not the same
    if (RegExp(r'^(\d)\1{13}$').hasMatch(cleanJshshir)) {
      return false;
    }

    return true;
  }

  Widget _buildTextField({
    required String label,
    required TextEditingController controller,
    TextInputType? keyboardType,
    bool enabled = true,
    VoidCallback? onTap,
    bool isRequired = true,
    bool isCalendar = false,
    bool isPhoneNumber = false,
    bool isJSHSHIR = false, // Add JSHSHIR parameter
  }) {
    // Create phone number formatter for +998 prefix
    var phoneFormatter = MaskTextInputFormatter(
      mask: '+998 (##) ###-##-##',
      filter: {"#": RegExp(r'[0-9]')},
      initialText: '+998 ', // This ensures +998 is always visible
    );

    // Initialize controller with +998 prefix if it's a phone field and empty
    if (isPhoneNumber && controller.text.isEmpty) {
      controller.text = '+998 ';
      controller.selection = TextSelection.fromPosition(
        TextPosition(offset: controller.text.length),
      );
    }

    return InkWell(
      onTap: !enabled && onTap != null // Fixed the logic here
          ? () {
              onTap();
            }
          : null,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (isRequired)
                Text(
                  ' *',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.red,
                    fontWeight: FontWeight.w500,
                  ),
                ),
            ],
          ),
          SizedBox(height: 8.h),
          TextFormField(
            controller: controller,
            cursorColor: cFirstColor,
            keyboardType: isPhoneNumber
                ? TextInputType.phone
                : isJSHSHIR
                    ? TextInputType.number
                    : keyboardType,
            enabled: enabled,
            onTap: onTap,
            inputFormatters: isPhoneNumber
                ? [phoneFormatter]
                : isJSHSHIR
                    ? [
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(14),
                      ]
                    : null,
            validator: isRequired
                ? (value) {
                    if (value == null || value.trim().isEmpty) {
                      return '$label ${LocaleKeys.need_to_fill.tr()}';
                    }
                    // Phone validation
                    if (isPhoneNumber && value.length < 19) {
                      return LocaleKeys.need_fill_phoneNumber.tr();
                    }
                    // JSHSHIR validation
                    if (isJSHSHIR) {
                      if (!_isValidJSHSHIR(value)) {
                        return LocaleKeys.jshshir_requirement.tr();
                      }
                    }
                    return null;
                  }
                : null,
            decoration: InputDecoration(
              filled: true,
              fillColor: isDark() ? cCardDarkColor : cWhiteColor,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: BorderSide(color: Colors.blue, width: 2),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: BorderSide(color: Colors.red, width: 1),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: BorderSide(color: Colors.red, width: 2),
              ),
              contentPadding:
                  EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              suffixIcon: !enabled && onTap != null && isCalendar
                  ? Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: SvgPicture.asset(
                        Assets.iconsCalendar,
                        color: cFirstColor,
                      ),
                    )
                  : isPhoneNumber && enabled
                      ? Icon(Icons.phone, color: cFirstColor, size: 16.w)
                      : isJSHSHIR && enabled
                          ? Icon(Icons.credit_card,
                              color: cFirstColor, size: 16.w)
                          : null,
              hintText: isJSHSHIR ? '14 digits' : null,
              hintStyle: TextStyle(
                color: Colors.grey[400],
                fontSize: 14.sp,
              ),
            ),
          ),
          SizedBox(height: 16.h),
        ],
      ),
    );
  }

  String getJSHSHIRDigits(String jshshir) {
    // Remove all non-digit characters and return clean 14-digit string
    return jshshir.replaceAll(RegExp(r'[^0-9]'), '');
  }

  String getPhoneDigits(String formattedPhone) {
    // Remove all non-digit characters
    String digitsOnly = formattedPhone.replaceAll(RegExp(r'[^0-9]'), '');

    // Remove the 998 prefix if it exists
    if (digitsOnly.startsWith('998') && digitsOnly.length > 3) {
      return digitsOnly.substring(3); // Returns: 911234567
    }

    return digitsOnly;
  }

  Widget _buildDropdownField({
    required String label,
    required String? value,
    required List<String> items,
    required Function(String?) onChanged,
    bool isRequired = true,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            if (isRequired)
              Text(
                ' *',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.red,
                  fontWeight: FontWeight.w500,
                ),
              ),
          ],
        ),
        SizedBox(height: 8.h),
        DropdownButtonFormField<String>(
          value: value,
          onChanged: onChanged,
          decoration: InputDecoration(
            filled: true,
            fillColor: isDark() ? cCardDarkColor : cWhiteColor,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: Colors.blue, width: 2),
            ),
            contentPadding:
                EdgeInsets.symmetric(horizontal: 16.w,vertical:context.isTablet?20.h: 12.h),
          ),
          items: items.map((String item) {
            return DropdownMenuItem<String>(
              value: item,
              child: Text(item,style: TextStyle(fontSize:context.isTablet?10.sp:16.sp,height: 1),),
            );
          }).toList(),
        ),
        SizedBox(height: 16.h),
      ],
    );
  }

  Widget _buildDropdownProvinceField({
    required String label,
    required Province? value,
    required List<Province> items,
    required Function(Province?) onChanged,
    bool isRequired = true,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            if (isRequired)
              Text(
                ' *',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.red,
                  fontWeight: FontWeight.w500,
                ),
              ),
          ],
        ),
        SizedBox(height: 8.h),
        DropdownButtonFormField<Province>(
          value: value,
          onChanged: onChanged,
          decoration: InputDecoration(
            filled: true,
            fillColor: isDark() ? cCardDarkColor : cWhiteColor,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: Colors.blue, width: 2),
            ),
            contentPadding: EdgeInsets.symmetric(
                horizontal: 16.w, vertical: context.isTablet ? 20.h : 12.h),
          ),
          items: items.map((Province item) {
            return DropdownMenuItem<Province>(
              value: item,
              child: Text(
                getProvince(province: item,),
                style: TextStyle(fontSize:context.isTablet?10.sp:16.sp,height: 1),
              ),
            );
          }).toList(),
        ),
        SizedBox(height: 16.h),
      ],
    );
  }

  Widget _buildDropdownRegionField({
    required String label,
    required Region? value,
    required List<Region> items,
    required Function(Region?) onChanged,
    bool isRequired = true,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            if (isRequired)
              Text(
                ' *',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.red,
                  fontWeight: FontWeight.w500,
                ),
              ),
          ],
        ),
        SizedBox(height: 8.h),
        DropdownButtonFormField<Region>(
          value: value,
          onChanged: onChanged,
          decoration: InputDecoration(
            filled: true,
            fillColor: isDark() ? cCardDarkColor : cWhiteColor,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: Colors.blue, width: 2),
            ),
            contentPadding:
                EdgeInsets.symmetric(horizontal: 16.w,vertical:context.isTablet?20.h: 12.h),
          ),
          items: items.map((Region item) {
            return DropdownMenuItem<Region>(
              value: item,
              child: Text(getRegion(region: item),style: TextStyle(fontSize:context.isTablet?10.sp:16.sp,height: 1),),
            );
          }).toList(),
        ),
        SizedBox(height: 16.h),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: BlocConsumer<AddPersonBloc, AddPersonState>(
        listener: (context, state) {
          if (state.status == AddPersonStatus.success) {
            Navigator.pop(context, true);
          } else if (state.status == AddPersonStatus.failure) {
            CustomToast.showToast(state.message ?? "");
          }
        },
        builder: (context, state) {
          return AnimatedContainer(
            duration: _isDragging ? Duration.zero : Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            height: (_isExpanded
                    ? MediaQuery.of(context).size.height * 0.95
                    : MediaQuery.of(context).size.height * 0.8) -
                _dragExtent.abs(),
            transform: Matrix4.translationValues(
                0, _dragExtent.clamp(-50.0, 100.0), 0),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(cRadius22.r),
                topRight: Radius.circular(cRadius22.r),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: Offset(0, -2),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(cRadius22.r),
                  topRight: Radius.circular(cRadius22.r)),
              child: Scaffold(
                backgroundColor: isDark() ? cCardDarkColor : cWhiteColor,
                body: Form(
                  key: _formKey2,
                  child: Column(
                    children: [
                      // Drag handle area
                      GestureDetector(
                        onPanStart: _handleDragStart,
                        onPanUpdate: _handleDragUpdate,
                        onPanEnd: _handleDragEnd,
                        behavior: HitTestBehavior.translucent,
                        child: Container(
                          width: MediaQuery.of(context).size.width,
                          padding: EdgeInsets.symmetric(vertical: 15.h),
                          child: Column(
                            children: [
                              Container(
                                width: 80.w,
                                height: 5.h,
                                decoration: BoxDecoration(
                                  color: Colors.grey[300],
                                  borderRadius: BorderRadius.circular(2.5.h),
                                ),
                              ),
                              SizedBox(height: 16.h),
                              Text(
                                LocaleKeys.enter_information.tr(),
                                style: TextStyle(
                                  fontSize: 18.sp,
                                  fontWeight: FontWeight.w600,
                                  color: isDark() ? cGrayColor1 : cBlackColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      // Content area
                      Expanded(
                        child: SingleChildScrollView(
                          padding: EdgeInsets.symmetric(horizontal: 20.w),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(height: 8.h),

                              _buildTextField(
                                label: LocaleKeys.name.tr(),
                                controller: _nameController,
                              ),

                              _buildTextField(
                                label: LocaleKeys.surname.tr(),
                                controller: _surnameController,
                              ),

                              _buildTextField(
                                label: LocaleKeys.father_name.tr(),
                                controller: _fatherNameController,
                              ),

                              _buildTextField(
                                label: LocaleKeys.birth_date.tr(),
                                controller: _birthDateController,
                                enabled: false,
                                isCalendar: true,
                                onTap: () async {
                                  final DateTime? picked = await showDatePicker(
                                    context: context,
                                    initialDate: DateTime.now(),
                                    firstDate: DateTime(1900),
                                    lastDate: DateTime.now(),
                                  );
                                  if (picked != null) {
                                    // Format with leading zeros using padLeft
                                    // String formattedDate = "${picked.year}-${picked.month.toString().padLeft(2, '0')}-${picked.day.toString().padLeft(2, '0')}";
                                    // _birthDateController.text = formattedDate;
                                    // Alternative using DateFormat (requires intl package):
                                    _birthDateController.text =
                                        DateFormat('yyyy-MM-dd').format(picked);
                                  }
                                },
                              ),

                              _buildDropdownProvinceField(
                                label: LocaleKeys.region.tr(),
                                value: _selectedProvince,
                                items: province,
                                onChanged: (value) =>
                                    setState(() => _selectedProvince = value),
                              ),
                              _buildDropdownRegionField(
                                label: LocaleKeys.city.tr(),
                                value: _selectedCity,
                                items: region,
                                onChanged: (value) =>
                                    setState(() => _selectedCity = value),
                              ),

                              _buildTextField(
                                label: LocaleKeys.phone_number.tr(),
                                controller: _phoneController,
                                isPhoneNumber: true,
                                isRequired: true,
                              ),

                              _buildTextField(
                                label: LocaleKeys.live_place.tr(),
                                controller: _addressController,
                              ),

                              _buildDropdownField(
                                label: LocaleKeys.gender.tr(),
                                value: _selectedGender,
                                items: ['Erkak', 'Ayol'],
                                onChanged: (value) =>
                                    setState(() => _selectedGender = value),
                              ),
                              _buildTextField(
                                label: LocaleKeys.education_level.tr(),
                                controller: _educationLeveController,
                              ),

                              _buildTextField(
                                label: LocaleKeys.speciality.tr(),
                                controller: _specialtyController,
                              ),

                              _buildTextField(
                                label: LocaleKeys.JSHSHIR.tr(),
                                controller: _idNumberController,
                                isJSHSHIR: true,
                                isRequired: true,
                                keyboardType: TextInputType.number,
                              ),

                              SizedBox(height: 20.h),

                              // Save button
                              Container(
                                width: double.infinity,
                                height: 50.h,
                                child: ElevatedButton(
                                  onPressed: () {
                                    if (_validateForm()) {
                                      Map<String, dynamic> map = {
                                        "firstName": _nameController.text,
                                        "lastName": _surnameController.text,
                                        "middleName":
                                            _fatherNameController.text,
                                        "province": _selectedProvince?.id,
                                        "region": _selectedCity?.id,
                                        "address": _addressController.text,
                                        "birthDate": _birthDateController.text,
                                        "gender": _selectedGender,
                                        "pinfl": getJSHSHIRDigits(
                                            _idNumberController.text),
                                        "phone": getPhoneDigits(
                                            _phoneController.text),
                                        "information":
                                            _educationLeveController.text,
                                        "specialization":
                                            _specialtyController.text
                                      };
                                      context.read<AddPersonBloc>().add(
                                          AddUnEmployeedPersonEvent(map: map));
                                    }
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12.r),
                                    ),
                                    elevation: 0,
                                  ),
                                  child: _button(state),
                                ),
                              ),
                              SizedBox(height: 30.h),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

Widget _button(AddPersonState state) {
  if (state.status == AddPersonStatus.loading) {
    return CupertinoActivityIndicator(
      color: cWhiteColor,
      radius: 10.r,
    );
  } else {
    return Text(
      LocaleKeys.save.tr(),
      style: TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.w600,
      ),
    );
  }
}
