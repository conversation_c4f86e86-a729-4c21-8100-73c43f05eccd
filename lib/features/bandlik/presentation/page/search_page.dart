import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart' hide Trans;
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/core/widgets/custom_toast.dart';
import 'package:govassist/core/widgets/failure_widget.dart';
import 'package:govassist/features/bandlik/data/model/employee.dart';
import 'package:govassist/features/bandlik/presentation/bloc/search_bloc/search_bloc.dart';
import 'package:govassist/features/bandlik/presentation/page/detailed_page.dart';
import 'package:govassist/features/bandlik/presentation/widget/jobless_widget.dart';
import 'package:govassist/features/tasks/presentation/widgets/empty_list_widget.dart';
import 'package:govassist/translations/locale_keys.g.dart';

import 'category_bottom_sheet.dart';

class SearchPage extends StatefulWidget {
  final String searchText;

  const SearchPage({super.key, required this.searchText});

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  @override
  Widget build(BuildContext context) {
    return BlocConsumer<SearchBloc, SearchState>(
      listener: (context, state) {
        if (state.status == SearchStatus.failure) {
          CustomToast.showToast(state.message ?? "");
        }
      },
      builder: (context, state) {
        print(state.status);
        if (state.status == SearchStatus.loading) {
          return SizedBox(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            child: CupertinoActivityIndicator(
              color: Theme.of(context).primaryColor,
              radius: 30.r,
            ),
          );
        } else if (state.status == SearchStatus.success) {
          return ListView.separated(
              padding: EdgeInsets.only(
                  left: 20.w, right: 20.w, bottom: 100.h, top: 20.h),
              itemCount: state.list?.length ?? 0,
              itemBuilder: (context, index) {
                EmployeeItem employeeItem =
                    state.list?[index] ?? EmployeeItem();
                if (state.list?.isEmpty == true) {
                  return EmptyListWidget(
                      onTap: () {
                        BlocProvider.of<SearchBloc>(context).add(
                            SearchPersonEvent(searchText: widget.searchText));
                      },
                      title: LocaleKeys.empty_data.tr());
                } else {
                  return JobLessWidget(
                    onDetailTap: () {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => DetailedPage(
                                    employeeItem: employeeItem,
                                  )));
                    },
                    onEmployTap: () {
                      showModalBottomSheet(
                        context: context,
                        isScrollControlled: true,
                        backgroundColor: Colors.transparent,
                        isDismissible: true,
                        enableDrag: false,
                        builder: (context) {
                          return CategoryBottomSheet(
                            id: employeeItem.id ?? "",
                          );
                        },
                      );
                    },
                    employeeItem: employeeItem,
                    onTap: () {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => DetailedPage(
                                    employeeItem: employeeItem,
                                  )));
                    },
                  );
                }
              },
              separatorBuilder: (BuildContext context, int index) {
                return SizedBox(
                  height: 8.h,
                );
              });
        } else if (state.status == SearchStatus.failure) {
          return FailureWidget(
              text: state.message ?? "",
              onTap: () {
                BlocProvider.of<SearchBloc>(context)
                    .add(SearchPersonEvent(searchText: widget.searchText));
              });
        } else {
          return SizedBox();
        }
      },
    );
  }
}
