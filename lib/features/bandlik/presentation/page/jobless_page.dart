import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart' hide Trans;
import 'package:govassist/core/widgets/failure_widget.dart';
import 'package:govassist/features/bandlik/data/model/employee.dart';
import 'package:govassist/features/bandlik/presentation/bloc/get_persons_bloc/get_persons_bloc.dart';
import 'package:govassist/features/bandlik/presentation/page/category_bottom_sheet.dart';
import 'package:govassist/features/bandlik/presentation/page/detailed_page.dart';
import 'package:govassist/features/bandlik/presentation/widget/jobless_widget.dart';
import 'package:govassist/features/tasks/presentation/widgets/empty_list_widget.dart';
import 'package:govassist/translations/locale_keys.g.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';

class JoblessPage extends StatefulWidget {
  final bool employment;

  const JoblessPage({super.key, required this.employment});

  @override
  State<JoblessPage> createState() => _JoblessPageState();
}

class _JoblessPageState extends State<JoblessPage> {
  final PagingController<int, EmployeeItem> _pagingController =
      PagingController(firstPageKey: 1);
  bool refresh = false;
  List<EmployeeItem> list = [];

  void handleRefresh(bool refresh) {
    this.refresh = refresh;
    _pagingController.refresh();
  }

  void _pageRequestListener(int pageKey) {
    if (pageKey == 1) {
      BlocProvider.of<GetPersonsBloc>(context).add(GetEmployeeEvent(
          refresh: true, page: 1, limit: 10, employment: widget.employment));
    } else {
      BlocProvider.of<GetPersonsBloc>(context).add(GetEmployeeEvent(
          refresh: false,
          page: pageKey,
          limit: 10,
          employment: widget.employment));
    }
  }

  @override
  void initState() {
    super.initState();
    _pagingController.addPageRequestListener(_pageRequestListener);
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<GetPersonsBloc, GetPersonsState>(
      buildWhen: (previous, current) {
        if (current.status == GetPersonsStatus.reload) {
          return false;
        }
        return true;
      },
      listener: (context, state) {
        if (state.status == GetPersonsStatus.success) {
          list = state.employee?.employeeItem ?? [];
          int currentPage = state.employee?.page ?? 1;
          bool isLastPage = state.employee?.totalPages == currentPage;
          int next = currentPage + 1;
          if (isLastPage) {
            _pagingController.appendLastPage(list);
          } else {
            _pagingController.appendPage(list, next);
          }
        } else if (state.status == GetPersonsStatus.reload) {
          handleRefresh(true);
        } else if (state.status == GetPersonsStatus.failure) {
          print(state.message);
          // Handle errors in the listener to show in the paging UI
          _pagingController.error = state.message ?? "Unknown error";
        }
      },
      builder: (context, state) {
        return Column(
          children: [
            Expanded(
              child: RefreshIndicator(
                onRefresh: () async {
                  handleRefresh(true);
                },
                child: PagedListView(
                    physics: BouncingScrollPhysics(
                        parent: AlwaysScrollableScrollPhysics()),
                    padding:
                        EdgeInsets.only(bottom: 140.h, left: 16.w, right: 16.w),
                    pagingController: _pagingController,
                    builderDelegate: PagedChildBuilderDelegate<EmployeeItem>(
                        noItemsFoundIndicatorBuilder: (context) {
                          return EmptyListWidget(
                              onTap: () {
                                handleRefresh(true);
                              },
                              title: LocaleKeys.empty_data.tr());
                        },
                        firstPageErrorIndicatorBuilder: (context) =>
                            FailureWidget(
                              text: state.message ?? "",
                              onTap: () {
                                handleRefresh(true);
                              },
                            ),
                        newPageProgressIndicatorBuilder: (context) => Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16.0),
                                  child: CupertinoActivityIndicator(
                                    color: Theme.of(context).primaryColor,
                                    radius: 30.r,
                                  )),
                            ),
                        firstPageProgressIndicatorBuilder: (context) => Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16.0),
                                  child: CupertinoActivityIndicator(
                                    color: Theme.of(context).primaryColor,
                                    radius: 30.r,
                                  )),
                            ),
                        itemBuilder: (context, item, index) {
                          return Padding(
                              padding: EdgeInsets.only(top: 16.h),
                              child: JobLessWidget(
                                onDetailTap: () {
                                  Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                          builder: (context) => DetailedPage(
                                                employeeItem: item,
                                              )));
                                },
                                onEmployTap: () {
                                  showModalBottomSheet(
                                    context: context,
                                    isScrollControlled: true,
                                    backgroundColor: Colors.transparent,
                                    isDismissible: true,
                                    enableDrag: false,
                                    constraints: BoxConstraints(
                                      maxWidth: MediaQuery.of(context).size.width, // Force full width
                                    ),
                                    builder: (context) {
                                      return SizedBox(
                                        width: MediaQuery.of(context).size.width, // Ensure full width
                                        child: CategoryBottomSheet(
                                          id: item.id ?? "",
                                        ),
                                      );
                                    },
                                  ).then((value) {
                                    if(value)
                                    handleRefresh(true);
                                  });
                                },
                                employeeItem: item,
                                onTap: () {
                                  Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                          builder: (context) => DetailedPage(
                                                employeeItem: item,
                                              )));
                                },
                              ));
                        })),
              ),
            ),
          ],
        );
      },
    );
  }
}
