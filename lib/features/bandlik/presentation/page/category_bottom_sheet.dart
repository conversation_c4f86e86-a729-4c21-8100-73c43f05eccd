import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart' hide Trans;
import 'package:govassist/core/function/functions.dart';
import 'package:govassist/core/utils/api_path.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/core/widgets/custom_toast.dart';
import 'package:govassist/di/dependency_injection.dart';
import 'package:govassist/features/bandlik/data/model/employment_type.dart';
import 'package:govassist/generated/assets.dart';
import 'package:govassist/translations/locale_keys.g.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

import 'package:intl/intl.dart';

// Model class for Employment Type

class CategoryBottomSheet extends StatefulWidget {
  final String id;

  CategoryBottomSheet({super.key, required this.id});

  @override
  State<CategoryBottomSheet> createState() => _CategoryBottomSheetState();
}

class _CategoryBottomSheetState extends State<CategoryBottomSheet>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _dragAnimationController;
  late Animation<double> _animation;
  late Animation<double> _dragAnimation;
  double _dragExtent = 0.0;
  bool _isExpanded = false;
  bool _isDragging = false;
  final Dio dio = di();

  // Category selection
  EmploymentTypeInCategory? _selectedEmploymentType;
  List<EmploymentTypeInCategory> _employmentTypes = [];
  bool _isLoading = true;
  bool _isSaving = false; // Added for saving state
  String? _errorMessage;

  // Form controllers - using Map for dynamic field handling
  final Map<String, TextEditingController> _controllers = {};
  final GlobalKey<FormState> _formKey1 = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadEmploymentTypes();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _dragAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _dragAnimation = Tween<double>(
      begin: 0.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _dragAnimationController,
      curve: Curves.easeOut,
    ));
  }

  Future<void> _loadEmploymentTypes() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final response = await dio.post(employmentTypesPath);

      if (response.statusCode == 200) {
        final List<dynamic> jsonData = response.data;
        setState(() {
          _employmentTypes = jsonData
              .map((item) => EmploymentTypeInCategory.fromJson(item))
              .toList();
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage =
              '${LocaleKeys.loading_error.tr()}: ${response.statusCode}';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = '${LocaleKeys.network_error.tr()}: $e';
        _isLoading = false;
      });
    }
  }

  // New method to handle employment data submission
  Future<void> _saveEmploymentData() async {
    if (!_validateForm()) return;

    try {
      setState(() {
        _isSaving = true;
      });

      // Prepare the request body
      Map<String, dynamic> requestBody = {
        "employment": true,
        "employmentType": _selectedEmploymentType!.id,
      };

      // Add employment date if available (optional)
      String today = DateFormat('yyyy-MM-dd').format(DateTime.now());
      requestBody["employmentDate"] = today;

      // Add dynamic fields based on selected employment type
      _controllers.forEach((key, controller) {
        if (controller.text.isNotEmpty) {
          String value = controller.text.trim();

          // Handle different field types
          switch (key) {
            case 'certificateNumber':
              requestBody['certificateNumber'] = value;
              break;
            case 'certificateDate':
              requestBody['certificateDate'] = value;
              break;
            case 'companyName':
              requestBody['companyName'] = value;
              break;
            case 'position':
              requestBody['position'] = value;
              break;
            case 'orderNumber':
              requestBody['orderNumber'] = value;
              break;
            case 'orderDate':
              requestBody['orderDate'] = value;
              break;
            case 'creditPurpose':
              requestBody['creditPurpose'] = value;
              break;
            case 'creditAmount':
              // Convert to number for credit amount
              requestBody['creditAmount'] = int.tryParse(value) ?? 0;
              break;
            case 'ssudaPurpose':
              requestBody['SsudaPurpose'] = value; // Note: Capital S as per API
              break;
            case 'ssudaAmount':
              // Convert to number for ssuda amount
              requestBody['SsudaAmount'] =
                  int.tryParse(value) ?? 0; // Note: Capital S as per API
              break;
          }
        }
      });

      // Make PUT request
      final response = await dio.put(
        "${employmentPath}${widget.id}",
        data: requestBody,
      );

      setState(() {
        _isSaving = false;
      });

      if (response.statusCode == 200 || response.statusCode == 201) {
        CustomToast.showToast(LocaleKeys.successfully_saved.tr());
        Navigator.pop(context, true);
      } else {
        CustomToast.showToast(
            "${LocaleKeys.error_saving_data.tr()}: ${response.statusCode}");
      }
    } catch (e) {
      setState(() {
        _isSaving = false;
      });

      String errorMessage = '${LocaleKeys.network_error.tr()}: ';
      if (e is DioException) {
        if (e.response != null) {
          errorMessage += 'Status: ${e.response!.statusCode}';
          if (e.response!.data != null) {
            errorMessage += ', ${e.response!.data}';
          }
        } else {
          errorMessage += e.message ?? LocaleKeys.unknown_error_retry.tr();
        }
      } else {
        errorMessage += e.toString();
      }
      CustomToast.showToast(errorMessage);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _dragAnimationController.dispose();
    // Dispose all dynamic controllers
    _controllers.values.forEach((controller) => controller.dispose());
    super.dispose();
  }

  TextEditingController _getController(String key) {
    if (!_controllers.containsKey(key)) {
      _controllers[key] = TextEditingController();
    }
    return _controllers[key]!;
  }

  void _clearAllControllers() {
    _controllers.values.forEach((controller) => controller.clear());
  }

  void _handleDragStart(DragStartDetails details) {
    setState(() {
      _isDragging = true;
    });
    _dragAnimationController.stop();
  }

  void _handleDragUpdate(DragUpdateDetails details) {
    if (!_isDragging) return;
    setState(() {
      _dragExtent += details.delta.dy;
      _dragExtent = _dragExtent.clamp(-100.0, 150.0);
    });
  }

  void _handleDragEnd(DragEndDetails details) {
    if (!_isDragging) return;
    setState(() {
      _isDragging = false;
    });

    final velocity = details.velocity.pixelsPerSecond.dy;

    if (_dragExtent < -50 || velocity < -800) {
      if (!_isExpanded) {
        _expandModal();
      } else {
        _snapBack();
      }
    } else if (_dragExtent > 80 || velocity > 800) {
      if (_isExpanded) {
        _collapseModal();
      } else {
        _closeModal();
      }
    } else {
      _snapBack();
    }
  }

  void _snapBack() {
    _dragAnimation = Tween<double>(
      begin: _dragExtent,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _dragAnimationController,
      curve: Curves.easeOut,
    ));

    _dragAnimationController.forward(from: 0.0).then((_) {
      setState(() {
        _dragExtent = 0.0;
      });
    });
  }

  void _closeModal() {
    _dragAnimation = Tween<double>(
      begin: _dragExtent,
      end: 400.0,
    ).animate(CurvedAnimation(
      parent: _dragAnimationController,
      curve: Curves.easeIn,
    ));

    _dragAnimationController.forward(from: 0.0).then((_) {
      Navigator.pop(context);
    });
  }

  void _expandModal() {
    setState(() {
      _isExpanded = true;
      _dragExtent = 0.0;
    });
    _animationController.forward();
  }

  void _collapseModal() {
    setState(() {
      _isExpanded = false;
      _dragExtent = 0.0;
    });
    _animationController.reverse();
  }

  bool _validateForm() {
    if (_selectedEmploymentType == null) {
      CustomToast.showToast(LocaleKeys.select_category.tr() + "!");
      return false;
    }

    if (!_formKey1.currentState!.validate()) {
      return false;
    }

    return true;
  }

  Future<void> _selectDate(TextEditingController controller) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(),
          child: child!,
        );
      },
    );
    if (picked != null) {
      controller.text = DateFormat('yyyy-MM-dd').format(picked);
    }
  }

  Widget _buildTextField({
    required String label,
    required TextEditingController controller,
    TextInputType? keyboardType,
    bool enabled = true,
    VoidCallback? onTap,
    bool isRequired = true,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            if (isRequired)
              Text(
                ' *',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.red,
                  fontWeight: FontWeight.w500,
                ),
              ),
          ],
        ),
        SizedBox(height: 8.h),
        TextFormField(
          cursorColor: cFirstColor,
          controller: controller,
          keyboardType: keyboardType,
          readOnly: !enabled,
          onTap: onTap,
          validator: isRequired
              ? (value) {
                  if (value == null || value.trim().isEmpty) {
                    return LocaleKeys.must_fill_this_field.tr();
                  }
                  return null;
                }
              : null,
          decoration: InputDecoration(
            filled: true,
            fillColor: isDark() ? cCardDarkColor : cWhiteColor,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: Colors.blue, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: Colors.red, width: 1),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: Colors.red, width: 2),
            ),
            contentPadding:
                EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            suffixIcon: !enabled && onTap != null
                ? Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: SvgPicture.asset(
                      Assets.iconsCalendar,
                      color: cFirstColor,
                    ),
                  )
                : null,
          ),
        ),
        SizedBox(height: 16.h),
      ],
    );
  }

  Widget _buildCategoryDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              LocaleKeys.select_category.tr(),
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              ' *',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.red,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        SizedBox(height: 8.h),
        if (_isLoading)
          Container(
            height: 50.h,
            decoration: BoxDecoration(
              color: isDark() ? cCardDarkColor : cWhiteColor,
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Center(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 16.w,
                    height: 16.h,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  SizedBox(width: 8.w),
                  Text(LocaleKeys.loading.tr()),
                ],
              ),
            ),
          )
        else if (_errorMessage != null)
          Container(
            height: 50.h,
            decoration: BoxDecoration(
              color: Colors.red[50],
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(color: Colors.red[300]!),
            ),
            child: Center(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error, color: Colors.red, size: 16.sp),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      _errorMessage!,
                      style: TextStyle(color: Colors.red[700], fontSize: 12.sp),
                      maxLines: 2,
                    ),
                  ),
                  TextButton(
                    onPressed: _loadEmploymentTypes,
                    child: Text(LocaleKeys.retry.tr(),
                        style: TextStyle(fontSize: 12.sp)),
                  ),
                ],
              ),
            ),
          )
        else
          DropdownButtonFormField<EmploymentTypeInCategory>(
            value: _selectedEmploymentType,
            isExpanded: true,
            onChanged: (value) {
              setState(() {
                _selectedEmploymentType = value;
                _clearAllControllers();
              });
            },
            decoration: InputDecoration(
              filled: true,
              fillColor: isDark() ? cCardDarkColor : cWhiteColor,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: BorderSide(color: Colors.blue, width: 2),
              ),
              contentPadding:
                  EdgeInsets.symmetric(horizontal: 16.w, vertical:context.isTablet?20.h: 12.h),
            ),
            items: _employmentTypes.map((employmentType) {
              return DropdownMenuItem<EmploymentTypeInCategory>(
                value: employmentType,
                child: Text(
                  employmentTypeTitleInCategory(
                      employmentTypeInCategory: employmentType),
                  style: TextStyle(fontSize:context.isTablet?10.sp:16.sp,height: 1),
                  maxLines: 1,
                  overflow: TextOverflow.visible,
                ),
              );
            }).toList(),

          ),
        SizedBox(height: 16.h),
      ],
    );
  }

  Widget _buildDynamicFormFields() {
    if (_selectedEmploymentType == null) return SizedBox.shrink();

    List<Widget> fields = [];

    // Certificate Number
    if (_selectedEmploymentType!.certificateNumber == true) {
      fields.add(_buildTextField(
        label: LocaleKeys.certificate_number.tr(),
        controller: _getController('certificateNumber'),
      ));
    }

    // Certificate Date
    if (_selectedEmploymentType!.certificateDate == true) {
      fields.add(_buildTextField(
        label: LocaleKeys.certificate_date.tr(),
        controller: _getController('certificateDate'),
        enabled: false,
        onTap: () => _selectDate(_getController('certificateDate')),
      ));
    }

    // Company Name
    if (_selectedEmploymentType!.companyName == true) {
      fields.add(_buildTextField(
        label: LocaleKeys.company_name.tr(),
        controller: _getController('companyName'),
      ));
    }

    // Position
    if (_selectedEmploymentType!.position == true) {
      fields.add(_buildTextField(
        label: LocaleKeys.position.tr(),
        controller: _getController('position'),
      ));
    }

    // Order Number
    if (_selectedEmploymentType!.orderNumber == true) {
      fields.add(_buildTextField(
        label: LocaleKeys.order_number.tr(),
        controller: _getController('orderNumber'),
      ));
    }

    // Order Date
    if (_selectedEmploymentType!.orderDate == true) {
      fields.add(_buildTextField(
        label: LocaleKeys.orderDate.tr(),
        controller: _getController('orderDate'),
        enabled: false,
        onTap: () => _selectDate(_getController('orderDate')),
      ));
    }

    // Credit Purpose
    if (_selectedEmploymentType!.creditPurpose == true) {
      fields.add(_buildTextField(
        label: LocaleKeys.creditPurpose.tr(),
        controller: _getController('creditPurpose'),
      ));
    }

    // Credit Amount
    if (_selectedEmploymentType!.creditAmount == true) {
      fields.add(_buildTextField(
        label: LocaleKeys.creditAmount.tr(),
        controller: _getController('creditAmount'),
        keyboardType: TextInputType.number,
      ));
    }

    // Ssuda Purpose
    if (_selectedEmploymentType!.ssudaPurpose == true) {
      fields.add(_buildTextField(
        label: LocaleKeys.ssudaPurpose.tr(),
        controller: _getController('ssudaPurpose'),
      ));
    }

    // Ssuda Amount
    if (_selectedEmploymentType!.ssudaAmount == true) {
      fields.add(_buildTextField(
        label: LocaleKeys.ssudaAmount.tr(),
        controller: _getController('ssudaAmount'),
        keyboardType: TextInputType.number,
      ));
    }

    return Column(children: fields);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: AnimatedBuilder(
        animation: _dragAnimation,
        builder: (context, child) {
          final currentDragExtent =
              _isDragging ? _dragExtent : _dragAnimation.value;

          return AnimatedContainer(
            duration: _isDragging ? Duration.zero : Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            height: (_isExpanded
                    ? MediaQuery.of(context).size.height * 0.9
                    : MediaQuery.of(context).size.height * 0.6) -
                (currentDragExtent < 0 ? 0 : currentDragExtent),
            transform: Matrix4.translationValues(
                0, currentDragExtent.clamp(0.0, 150.0), 0),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(cRadius22.r),
                topRight: Radius.circular(cRadius22.r),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: Offset(0, -2),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(cRadius22.r),
                topRight: Radius.circular(cRadius22.r),
              ),
              child: Scaffold(
                backgroundColor: isDark() ? cCardDarkColor : cWhiteColor,
                body: Form(
                  key: _formKey1,
                  child: Column(
                    children: [
                      // Drag handle area
                      GestureDetector(
                        onPanStart: _handleDragStart,
                        onPanUpdate: _handleDragUpdate,
                        onPanEnd: _handleDragEnd,
                        behavior: HitTestBehavior.translucent,
                        child: Container(
                          width: double.infinity,
                          padding: EdgeInsets.symmetric(vertical: 15.h),
                          child: Column(
                            children: [
                              Container(
                                width: 80.w,
                                height: 5.h,
                                decoration: BoxDecoration(
                                  color: Colors.grey[300],
                                  borderRadius: BorderRadius.circular(2.5.h),
                                ),
                              ),
                              SizedBox(height: 16.h),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    LocaleKeys.make_employment.tr(),
                                    style: TextStyle(
                                      fontSize: 18.sp,
                                      fontWeight: FontWeight.w600,
                                      color:
                                          isDark() ? cGrayColor1 : cBlackColor,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),

                      // Content area
                      Expanded(
                        child: SingleChildScrollView(
                          padding: EdgeInsets.symmetric(horizontal: 20.w),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(height: 8.h),

                              _buildCategoryDropdown(),

                              _buildDynamicFormFields(),

                              SizedBox(height: 20.h),

                              // Save button - Updated with API call
                              Container(
                                width: double.infinity,
                                height: 50.h,
                                child: ElevatedButton(
                                  onPressed: (_isLoading || _isSaving)
                                      ? null
                                      : _saveEmploymentData,
                                  // Changed to call the new method
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: (_isLoading || _isSaving)
                                        ? Colors.grey
                                        : Colors.blue,
                                    foregroundColor: Colors.white,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12.r),
                                    ),
                                    elevation: 0,
                                  ),
                                  child: _isSaving
                                      ? Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            SizedBox(
                                              width: 16.w,
                                              height: 16.h,
                                              child: CircularProgressIndicator(
                                                strokeWidth: 2,
                                                color: Colors.white,
                                              ),
                                            ),
                                            SizedBox(width: 8.w),
                                            Text(
                                              LocaleKeys.saving.tr(),
                                              style: TextStyle(
                                                fontSize: 16.sp,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                          ],
                                        )
                                      : Text(
                                          LocaleKeys.save.tr(),
                                          style: TextStyle(
                                            fontSize: 16.sp,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                ),
                              ),

                              SizedBox(height: 30.h),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
