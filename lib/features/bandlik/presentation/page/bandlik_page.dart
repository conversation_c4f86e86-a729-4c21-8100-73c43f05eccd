import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart' hide Trans;
import 'package:govassist/core/function/functions.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/features/bandlik/presentation/bloc/get_persons_bloc/get_persons_bloc.dart';
import 'package:govassist/features/bandlik/presentation/bloc/search_bloc/search_bloc.dart';
import 'package:govassist/features/bandlik/presentation/page/add_person.dart';
import 'package:govassist/features/bandlik/presentation/page/jobless_page.dart';
import 'package:govassist/features/bandlik/presentation/page/search_page.dart';
import 'package:govassist/features/bandlik/presentation/widget/bandlik_header_widget.dart';
import 'package:govassist/features/home/<USER>/navigation.dart';
import 'package:govassist/features/payments/payment_res.dart';
import 'package:govassist/translations/locale_keys.g.dart';
import 'package:top_snackbar_flutter/custom_snack_bar.dart';
import 'package:top_snackbar_flutter/top_snack_bar.dart';

class BandlikPage extends StatefulWidget {
  final GlobalKey<ScaffoldState> navigatorKey;
  static Widget screen({required GlobalKey<ScaffoldState> navigatorKey}) {
    return MultiBlocProvider(providers: [
      BlocProvider(create: (context) => GetPersonsBloc()),
      BlocProvider(create: (context) => SearchBloc()),
    ], child: BandlikPage(navigatorKey: navigatorKey,));
  }

  const BandlikPage({super.key, required this.navigatorKey});

  @override
  State<BandlikPage> createState() => _BandlikPageState();
}

class _BandlikPageState extends State<BandlikPage>
    with SingleTickerProviderStateMixin {

  late TabController _tabController;
  bool search = false;
  String searchText = "";
  bool _isCheckingPayment = false; // Add this state variable

  @override
  void initState() {
    super.initState();
    _tabController = TabController(vsync: this, length: 2);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _handleFABPress() async {
    if (_isCheckingPayment) return; // Prevent multiple taps

    setState(() {
      _isCheckingPayment = true;
    });

    try {
      // Check payment status
      final paymentResult = await checkUserPayment();

      if (!mounted) return; // Check if widget is still mounted

      // Handle different payment check results
      if (paymentResult == false) {
        // No internet connection
        showTopSnackBar(
          Overlay.of(context),
          CustomSnackBar.error(
            textScaleFactor: context.isTablet ? 1.5 : 1,
            message: LocaleKeys.check_internet_connection.tr(),
          ),
        );
        return;
      }

      if (paymentResult == null) {
        // Server error or request failed
        showTopSnackBar(
          Overlay.of(context),
          CustomSnackBar.error(
            textScaleFactor: context.isTablet ? 1.5 : 1,
            message: "Failed to check payment status",
          ),
        );
        return;
      }

      // Cast to PaymentRes since we got a valid response
      PaymentRes paymentStatus = paymentResult as PaymentRes;

      if (paymentStatus.status == true) {
        // Payment is done - show the bottom sheet
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          isDismissible: true,
          enableDrag: true,
          constraints: BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width, // Force full width
          ),
          builder: (context) {
            return SizedBox(
              width: MediaQuery.of(context).size.width, // Ensure full width
              child: AddPerson.screen(),
            );
          },
        ).then((value) {
          if (mounted&&value) {
            BlocProvider.of<GetPersonsBloc>(context).add(ReloadEmployeeEvent());
          }
        });
      } else {
        // Payment not done - show error message
        showTopSnackBar(
          Overlay.of(context),
          CustomSnackBar.error(
            textScaleFactor: context.isTablet ? 1.5 : 1,
            message: payResModelTitle(paymentRes: paymentStatus),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        // Handle unexpected errors
        showTopSnackBar(
          Overlay.of(context),
          CustomSnackBar.error(
            textScaleFactor: context.isTablet ? 1.5 : 1,
            message: "An unexpected error occurred",
          ),
        );
      }
      print('Error checking payment: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isCheckingPayment = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: BandlikHeaderWidget(onHamburgerTap: () {
        widget.navigatorKey.currentState!.openDrawer();
      }, onActionTap: () {
        setState(() {
          search = !search;
          searchText = "";
          BlocProvider.of<SearchBloc>(context).add(SearchInitial());
        });
      }, onSearch: (String text) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          BlocProvider.of<SearchBloc>(context)
              .add(SearchPersonEvent(searchText: text));
        });
      }),
      floatingActionButton: Padding(
        padding: EdgeInsets.only(bottom: 70.h),
        child: SizedBox(
          width:context.isTablet? 40.w:50.w,
          height:context.isTablet? 40.w:50.w,
          child: FloatingActionButton(
            backgroundColor:
            _isCheckingPayment ? cFirstColor.withOpacity(0.7) : cFirstColor,
            shape: CircleBorder(),
            onPressed: _isCheckingPayment ? null : _handleFABPress,
            child: AnimatedSwitcher(
              duration: Duration(milliseconds: 200),
              child: _isCheckingPayment
                  ? SizedBox(
                width:  30.w,
                height:  30.w,
                child: CircularProgressIndicator(
                  strokeWidth: 3.w,
                  valueColor: AlwaysStoppedAnimation<Color>(cWhiteColor),
                ),
              )
                  : Icon(
                Icons.add,
                color: cWhiteColor,
                size: context.isTablet ? 30.w : 28.w,
              ),
            ),
          ),
        ),
      ),
      body: search
          ? SearchPage(searchText: searchText)
          : Column(
              children: [
                Container(
                  margin:
                      EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
                  padding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 3.w),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(cRadius10.r),
                    color: Theme.of(context).cardTheme.color,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.1),
                        spreadRadius: 4,
                        blurRadius: 5,
                        offset: Offset(0, 4),
                      ),
                    ],
                  ),
                  child: TabBar(
                    dividerColor: Colors.transparent,
                    indicatorSize: TabBarIndicatorSize.tab,
                    controller: _tabController,
                    indicatorColor: Colors.transparent,
                    unselectedLabelColor:
                        Theme.of(context).textTheme.bodySmall?.color,
                    labelColor: cWhiteColor,
                    indicator: BoxDecoration(
                        color: cFirstColor,
                        borderRadius: BorderRadius.circular(cRadius10.r)),
                    tabs: [
                      Tab(
                        height: context.isTablet ? 50.h : 40.h,
                        child: Text(
                          LocaleKeys.un_employed.tr(),
                          style: TextStyle(
                              fontWeight: FontWeight.w500, fontSize: 16.sp),
                        ),
                      ),
                      Tab(
                        height: context.isTablet ? 50.h : 40.h,
                        child: Text(
                          LocaleKeys.employed.tr(),
                          style: TextStyle(
                              fontWeight: FontWeight.w500, fontSize: 16.sp),
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                    child: TabBarView(controller: _tabController, children: [
                  JoblessPage(
                    employment: false,
                  ),
                  JoblessPage(
                    employment: true,
                  ),
                ]))
              ],
            ),
    );
  }
}
