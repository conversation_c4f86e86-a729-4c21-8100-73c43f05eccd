import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:govassist/core/function/functions.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/core/widgets/header_widget_sub.dart';
import 'package:govassist/features/bandlik/data/model/employee.dart';
import 'package:govassist/translations/locale_keys.g.dart';

import '../../../../generated/assets.dart';

class DetailedPage extends StatelessWidget {
  final EmployeeItem employeeItem;

  const DetailedPage({super.key, required this.employeeItem});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: isDark()?cFourthColorDark:cWhiteColor,
      appBar: AppHeaderWidgetSub(
        title: "",
        onBackTap: () {
          Navigator.pop(context);
        },
        actionImage: Assets.iconsPlaceholder,
        onActionTap: () {},
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 5.h),

              // Full Name
              Text(
                "${employeeItem.firstName} ${employeeItem.lastName} ${employeeItem.middleName}",
                style: Theme.of(context)
                    .textTheme
                    .bodyMedium
                    ?.copyWith(fontSize: 20.sp, fontWeight: FontWeight.bold),
              ),

              SizedBox(height: 15.h),

              // Address
              _buildInfoRow(
                context,
                "${LocaleKeys.address.tr()}:",
                "${getProvince(province: employeeItem.province)} ${getRegion(region: employeeItem.region)} ${employeeItem.address}",
              ),

              // Birth Date
              _buildInfoRow(
                context,
                "${LocaleKeys.birth_date.tr()}:",
                "${employeeItem.birthDate}",
              ),

              // PINFL (JSHSHIR)
              _buildInfoRow(
                context,
                "${LocaleKeys.JSHSHIR.tr()}:",
                "${employeeItem.pinfl}",
              ),

              // Gender
              _buildInfoRow(
                context,
                "${LocaleKeys.gender.tr()}:",
                "${employeeItem.gender}",
              ),

              // Education Level
              _buildInfoRow(
                context,
                "${LocaleKeys.education_level.tr()}:",
                "${employeeItem.information}",
              ),

              // Specialization
              _buildInfoRow(
                context,
                "${LocaleKeys.speciality.tr()}:",
                "${employeeItem.specialization}",
              ),

              SizedBox(height: 15.h),

              // Employment Status
              Container(
                padding: EdgeInsets.symmetric(horizontal:employeeItem.employment == true?0: 12.w, vertical: 4.h),
                decoration: BoxDecoration(
                    color: employeeItem.employment == true
                        ? Colors.transparent
                        : cRedColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(cRadius22.r)),
                child: Text(
                  employeeItem.employment == true
                      ? "${LocaleKeys.employment_status.tr()}:"
                      : LocaleKeys.un_employed.tr(),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      fontSize: 16.sp,
                      color: employeeItem.employment == true
                          ? null
                          : cRedTextColor),
                ),
              ),

              // Employment Details (only shown if employed)
              if (employeeItem.employment == true) ...[
                SizedBox(height: 10.h),

                // Employment Type Title
                Text(
                  "${employmentTypeTitle(employmentType:employeeItem.employmentType??EmploymentType() )}:",
                  style: Theme.of(context)
                      .textTheme
                      .bodyMedium
                      ?.copyWith(fontSize: 18.sp, fontWeight: FontWeight.bold),
                ),

                SizedBox(height: 10.h),

                // Certificate Information (if available)
                if (employeeItem.certificateNumber != null &&
                    employeeItem.certificateNumber!.isNotEmpty) ...[
                  _buildInfoRow(
                    context,
                    "${LocaleKeys.certificate_number.tr()}:",
                    employeeItem.certificateNumber!,
                  ),
                ],

                if (employeeItem.certificateDate != null &&
                    employeeItem.certificateDate!.isNotEmpty) ...[
                  _buildInfoRow(
                    context,
                    "${LocaleKeys.certificate_date.tr()}:",
                    employeeItem.certificateDate!,
                  ),
                ],

                // Company Information (if available)
                if (employeeItem.companyName != null &&
                    employeeItem.companyName!.isNotEmpty) ...[
                  _buildInfoRow(
                    context,
                    "${LocaleKeys.company_name.tr()}:",
                    employeeItem.companyName!,
                  ),
                ],

                if (employeeItem.position != null &&
                    employeeItem.position!.isNotEmpty) ...[
                  _buildInfoRow(
                    context,
                    "${LocaleKeys.position.tr()}:",
                    employeeItem.position!,
                  ),
                ],

                if (employeeItem.orderNumber != null &&
                    employeeItem.orderNumber!.isNotEmpty) ...[
                  _buildInfoRow(
                    context,
                    "${LocaleKeys.order_number.tr()}:",
                    employeeItem.orderNumber!,
                  ),
                ],

                if (employeeItem.orderDate != null &&
                    employeeItem.orderDate!.isNotEmpty) ...[
                  _buildInfoRow(
                    context,
                    "${LocaleKeys.orderDate.tr()}:",
                    employeeItem.orderDate!,
                  ),
                ],

                // Credit Information (if available)
                if (employeeItem.creditPurpose != null &&
                    employeeItem.creditPurpose!.isNotEmpty) ...[
                  _buildInfoRow(
                    context,
                    "${LocaleKeys.creditPurpose.tr()}:",
                    employeeItem.creditPurpose!,
                  ),
                ],

                if (employeeItem.creditAmount != null &&
                    employeeItem.creditAmount! > 0) ...[
                  _buildInfoRow(
                    context,
                    "${LocaleKeys.creditAmount.tr()}:",
                    "${NumberFormat('#,###').format(employeeItem.creditAmount)}",
                  ),
                ],

                // Ssuda Information (if available)
                if (employeeItem.ssudaPurpose != null &&
                    employeeItem.ssudaPurpose!.isNotEmpty) ...[
                  _buildInfoRow(
                    context,
                    "${LocaleKeys.ssudaPurpose.tr()}:",
                    employeeItem.ssudaPurpose!,
                  ),
                ],

                if (employeeItem.ssudaAmount != null &&
                    employeeItem.ssudaAmount! > 0) ...[
                  _buildInfoRow(
                    context,
                    "${LocaleKeys.ssudaAmount.tr()}:",
                    "${NumberFormat('#,###').format(employeeItem.ssudaAmount)}",
                  ),
                ],
              ],

              SizedBox(height: 20.h),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context)
              .textTheme
              .bodyMedium
              ?.copyWith(fontWeight: FontWeight.w400, fontSize: 16.sp),
        ),
        SizedBox(height: 6.h),
        Text(
          value,
          style: Theme.of(context)
              .textTheme
              .bodyMedium
              ?.copyWith(fontWeight: FontWeight.bold, fontSize: 16.sp),
        ),
        SizedBox(height: 10.h),
      ],
    );
  }
}