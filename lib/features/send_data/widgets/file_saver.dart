import 'dart:io';
import 'dart:typed_data';

import 'package:external_path/external_path.dart';
import 'package:permission_handler/permission_handler.dart';

Future<String> writeFile(Uint8List data, String name) async {
  // storage permission ask
  var status = await Permission.storage.status;
  if (!status.isGranted) {
    await Permission.storage.request();
  }
  // the downloads folder path
  var path = await ExternalPath.getExternalStorageDirectories();
  var downloadDir = path!.first + "/" + ExternalPath.DIRECTORY_DOWNLOAD+'/sounds';

  var filePath = downloadDir + '/$name';
  //

  // the data
  var bytes = ByteData.view(data.buffer);
  final buffer = bytes.buffer;
  // save the data in the path
  File(filePath)
      .writeAsBytes(buffer.asUint8List(data.offsetInBytes, data.lengthInBytes));
  return filePath;
}
