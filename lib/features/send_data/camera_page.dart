import 'dart:async';
import 'dart:io';

import 'package:camera/camera.dart';
import 'package:circular_countdown_timer/circular_countdown_timer.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter_new/return_code.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gal/gal.dart';
import 'package:get/get.dart' hide Trans;
import 'package:govassist/core/function/functions.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/core/widgets/custom_toast.dart';
import 'package:govassist/translations/locale_keys.g.dart';
import 'package:image_editor/image_editor.dart';
import 'package:mno_zoom_widget/zoom_widget.dart';
import 'package:native_device_orientation/native_device_orientation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sn_progress_dialog/sn_progress_dialog.dart';
import 'package:video_compress/video_compress.dart';
import 'package:video_editor/video_editor.dart';
import 'package:video_player/video_player.dart';
import 'package:zoom_tap_animation/zoom_tap_animation.dart';

import '../../di/dependency_injection.dart';

// A screen that allows users to take a picture using a given camera.
class TakePictureScreen extends StatefulWidget {
  final List<CameraDescription> cameras;
  final bool isVideo;
  final bool isHD;
  final bool isFront;
  final String? previewMessage;

  const TakePictureScreen(
      {Key? key,
        required this.cameras,
        required this.isVideo,
        required this.isHD,
        required this.isFront,
        this.previewMessage})
      : super(key: key);

  @override
  TakePictureScreenState createState() => TakePictureScreenState();
}

class TakePictureScreenState extends State<TakePictureScreen> {
  CameraController? _controller;
  Future<void>? _initializeControllerFuture;
  SharedPreferences? prefs = di();
  CountDownController counter = CountDownController();

  final ValueNotifier<bool> _buttonLoading = ValueNotifier<bool>(false);
  bool _isRecording = false;
  bool _isPaused = false;

  bool isPhoto = true;
  bool isFlashOn = false;
  bool isFrontOn = false;

  Future<void> initCamera(CameraDescription camera) async {
    // To display the current output from the Camera,
    // create a CameraController.
    _controller = CameraController(
      // Get a specific camera from the list of available cameras.
      camera,
      // Define the resolution to use.
      widget.isHD ? ResolutionPreset.high : ResolutionPreset.medium,
      imageFormatGroup: ImageFormatGroup.yuv420,
    );

    // Next, initialize the controller. This returns a Future.
    _initializeControllerFuture = _controller?.initialize().then((value) {
      /// Guess, this causes error sometimes
      _controller?.setFlashMode(FlashMode.off);
      _controller?.lockCaptureOrientation(DeviceOrientation.portraitUp);
    });
  }

  _recordVideo() async {
    if (_isRecording && !_isPaused) {
      await _controller?.pauseVideoRecording();
      counter.pause();

      setState(() {
        _isPaused = true;
      });
    } else if (_isRecording && _isPaused) {
      await _controller?.resumeVideoRecording();
      counter.resume();
      setState(() {
        _isPaused = false;
      });
    } else if (!_isRecording) {
      await _controller?.prepareForVideoRecording();
      await _controller?.startVideoRecording();
      counter.start();
      setState(() {
        _isRecording = true;
      });
    }
  }

  Future<File> _mirrorPhoto(XFile image) async {
    Uint8List? imageBytes = await image.readAsBytes();

    // 1. flip the image on the X axis
    final ImageEditorOption option = ImageEditorOption();
    option.addOption(FlipOption(horizontal: true));
    imageBytes = await ImageEditor.editImage(
        image: imageBytes, imageEditorOption: option);

    // 2. write the image back to disk
    await File(image.path).delete();
    return await File(image.path).writeAsBytes(imageBytes!, flush: true);

    ///This doesn't work on some devices (Samsung)
    // img.Image? originalImage = img.decodeImage(imageBytes);
    // img.Image fixedImage = img.flipHorizontal(originalImage!);
    //
    // File file = File(image.path);
    // return await file.writeAsBytes(
    //   img.encodeJpg(fixedImage),
    //   flush: true,
    // );
  }

  _stopVideo() async {
    var callback;
    if (_isRecording) {
      final file = await _controller?.stopVideoRecording();
      _isPaused = false;

      if (isFlashOn) {
        _controller?.setFlashMode(FlashMode.off);
        isFlashOn = false;
      }

      setState(() {
        _isRecording = false;
        counter.reset();
      });

      final route = MaterialPageRoute(
        fullscreenDialog: true,
        builder: (_) => VideoPage(filePath: file!.path),
      );
      callback = await Navigator.push(context, route);
      return callback;
    }
  }

  List<String> split(String string, String separator, {int max = 0}) {
    var result = <String>[];

    if (separator.isEmpty) {
      result.add(string);
      return result;
    }

    while (true) {
      var index = string.indexOf(separator, 0);
      if (index == -1 || (max > 0 && result.length >= max)) {
        result.add(string);
        break;
      }

      result.add(string.substring(0, index));
      string = string.substring(index + separator.length);
    }

    return result;
  }

  @override
  void initState() {
    super.initState();

    if (widget.isVideo) {
      isPhoto = false;
    }

    if (widget.isFront) {
      isFrontOn = true;
      initCamera(widget.cameras.firstWhere(
              (camera) => camera.lensDirection == CameraLensDirection.front));
    } else {
      initCamera(widget.cameras.first);
    }
  }

  Future<String?> exportVideoWithFFmpeg({
    required VideoEditorController controller,
    required BuildContext context,
    required ProgressDialog progressDialog,
    required Function(String) saveFileToInternalStorage,
    Function(String)? onError,
    bool saveToGallery = true,
  }) async {
    try {
      // Show initial progress
      progressDialog.update(value: 0);

      // Get execution configuration
      final config = VideoFFmpegVideoEditorConfig(controller);
      final execute = await config.getExecuteConfig();
      final outputPath = execute.outputPath;

      // Create a completer to handle the async result
      final completer = Completer<String?>();

      // Execute the FFmpeg command
      await FFmpegKit.executeAsync(execute.command, (session) async {
        final returnCode = await session.getReturnCode();
        if (ReturnCode.isSuccess(returnCode)) {
          progressDialog.close();

          // Save to gallery if requested
          if (saveToGallery) {
            await Gal.putVideo(outputPath);
          }

          // Save to internal storage and return the path
          final path = await saveFileToInternalStorage(outputPath);
          completer.complete(path);
        } else {
          // Handle error
          progressDialog.close();
          final errorMessage =
              await session.getFailStackTrace() ?? "Unknown error";
          CustomToast.showToast("Error on export video: $errorMessage");
          print("Error on export video: $errorMessage");

          if (onError != null) {
            onError(errorMessage);
          }

          completer.complete(null);
        }
      }, (log) {
        // Optional log handling
      }, (statistics) {
        // Update progress based on statistics
        final timeProcessed = statistics.getTime();
        final totalDuration = controller.videoDuration.inMilliseconds;
        if (totalDuration > 0) {
          final progress = timeProcessed / totalDuration;
          progressDialog.update(value: (progress * 100).toInt());
        }
      });

      // Wait for the result
      final result = await completer.future;

      // If successful, pop the context with the path
      if (result != null) {
        Navigator.pop(context, result);
      }

      return result;
    } catch (e, s) {
      progressDialog.close();
      final errorMessage = s.toString();
      CustomToast.showToast("Error on export video: $errorMessage");
      print("Error on export video: $errorMessage");

      if (onError != null) {
        onError(errorMessage);
      }

      return null;
    }
  }

  @override
  void dispose() {
    // Dispose of the controller when the widget is disposed.
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var pd = ProgressDialog(context: context);
    var callback;
    int turns = 0;
    var ctx;

    return Scaffold(
      extendBody: true,
      appBar: AppBar(
        title: Text(
            isPhoto ? LocaleKeys.take_picture.tr() : LocaleKeys.take_video.tr(),
            style: TextStyle(
                fontSize: context.isTablet ? 16.sp : 20.sp,
                color: cWhiteColor)),
        toolbarHeight: 50.h,
        centerTitle: true,
        leadingWidth: 40.w,
        iconTheme: IconThemeData(size: 22.h, color: cWhiteColor),
        actions: [
          Padding(
            padding: EdgeInsets.only(right: context.isTablet ? 10.w : 0),
            child: IconButton(
                icon: Icon(isFrontOn
                    ? Icons.photo_camera_back
                    : Icons.photo_camera_front_outlined),
                onPressed: () async {
                  if (!isFrontOn) {
                    initCamera(widget.cameras.firstWhere((camera) =>
                    camera.lensDirection == CameraLensDirection.front));

                    setState(() {
                      isFrontOn = true;
                    });
                  } else {
                    initCamera(widget.cameras.firstWhere((camera) =>
                    camera.lensDirection == CameraLensDirection.back));

                    setState(() {
                      isFrontOn = false;
                    });
                  }
                }),
          ),
          Padding(
            padding: EdgeInsets.only(right: context.isTablet ? 10.w : 0),
            child: IconButton(
                icon: Icon(isFlashOn ? Icons.flash_on : Icons.flash_off),
                onPressed: () async {
                  if (!isFlashOn) {
                    _controller?.setFlashMode(FlashMode.torch);

                    setState(() {
                      isFlashOn = true;
                    });
                  } else {
                    _controller?.setFlashMode(FlashMode.off);

                    setState(() {
                      isFlashOn = false;
                    });
                  }
                }),
          ),
        ],
        backgroundColor: cFirstColor,
      ),
      // Wait until the controller is initialized before displaying the
      // camera preview. Use a FutureBuilder to display a loading spinner
      // until the controller has finished initializing.
      body: NativeDeviceOrientationReader(
        useSensor: true,
        builder: (contextRotation) {
          ctx = contextRotation;

          NativeDeviceOrientation orientation =
          NativeDeviceOrientationReader.orientation(ctx);

          switch (orientation) {
            case NativeDeviceOrientation.landscapeLeft:
              turns = -1;
              break;
            case NativeDeviceOrientation.landscapeRight:
              turns = 1;
              break;
            case NativeDeviceOrientation.portraitDown:
              turns = 2;
              break;
            default:
              turns = 0;
              break;
          }
          final size = MediaQuery.of(context).size;

          return FutureBuilder<void>(
            future: _initializeControllerFuture,
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.done) {
                // If the Future is complete, display the preview.
                return Container(
                    width: size.width.w,
                    height: size.height.h,
                    child: FittedBox(
                      fit: BoxFit.cover,
                      child: Container(
                        width: size.width.w,
                        child: CameraPreview(
                          _controller!,
                          child: Container(
                            decoration: BoxDecoration(
                              border:
                              Border.all(color: cFirstColor, width: 2.w),
                            ),
                          ),
                        ),
                      ),
                    ));
              } else {
                // Otherwise, display a loading indicator.
                return Center(child: CircularProgressIndicator());
              }
            },
          );
        },
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: ZoomTapAnimation(
        begin: 1,
        end: 0.8,
        child: SizedBox(
          height: 55.h,
          width: 55.h,
          child: FloatingActionButton(
              backgroundColor: isPhoto
                  ? cFirstColor
                  : _isPaused
                  ? Colors.green
                  : cRedColor,
              shape: CircleBorder(),
              child: isPhoto
                  ? ValueListenableBuilder<bool>(
                  valueListenable: _buttonLoading,
                  builder: (ctx, value, _) {
                    if (value) {
                      return CupertinoActivityIndicator(
                        color: cWhiteColor,
                        radius: 10.r,
                      );
                    } else {
                      return Icon(
                        Icons.camera,
                        color: cWhiteColor,
                        size: 22.h,
                      );
                    }
                  })
                  : CircularCountDownTimer(
                duration: 60,
                initialDuration: 0,
                controller: counter,
                width: MediaQuery.of(context).size.width / 8,
                height: MediaQuery.of(context).size.height / 8,
                ringColor: Colors.grey[300]!,
                ringGradient: null,
                fillColor: cFirstColor,
                fillGradient: null,
                backgroundColor: cFirstColor.withAlpha(50),
                backgroundGradient: null,
                strokeWidth: 5.h,
                strokeCap: StrokeCap.round,
                textStyle: TextStyle(
                    fontSize: 15.sp,
                    color: Colors.white,
                    fontWeight: FontWeight.bold),
                textFormat: CountdownTextFormat.S,
                isReverse: true,
                isReverseAnimation: false,
                isTimerTextShown: true,
                autoStart: false,
                onStart: () {
                  debugPrint('Countdown Started');
                },
                onComplete: () async {
                  String? videoPath = await _stopVideo();

                  if (videoPath != null) {
                    var _videoController =
                    await VideoEditorController.file(File(videoPath),
                        maxDuration: const Duration(seconds: 60))
                      ..initialize().then((_) => setState(() {}));

                    if (turns == -1) {
                      _videoController
                          .rotate90Degrees(RotateDirection.left);
                    } else if (turns == 1) {
                      _videoController
                          .rotate90Degrees(RotateDirection.right);
                    }
                    pd.show(
                        max: 100,
                        msg: "${LocaleKeys.video_is_saving.tr()}...",
                        msgFontSize: context.isTablet ? 13.sp : 17.sp,
                        valueFontSize: context.isTablet ? 11.sp : 15.sp,
                        borderRadius: 15.r,
                        cancel: Cancel(
                            autoHidden: true,
                            cancelImageColor: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.color),
                        msgColor: Theme.of(context)
                            .textTheme
                            .bodyMedium!
                            .color!,
                        valueColor: Theme.of(context)
                            .textTheme
                            .bodyMedium!
                            .color!,
                        backgroundColor:
                        Theme.of(context).cardTheme.color!);

                    ///Export video
                    Future.delayed(Duration(seconds: 1))
                        .then((value) async {
                      await exportVideoWithFFmpeg(
                        controller: _videoController,
                        context: context,
                        progressDialog: pd,
                        saveFileToInternalStorage:
                        saveFileToInternalStorage,
                        onError: (errorMessage) {
                          // Additional error handling if needed
                        },
                        saveToGallery:
                        true, // Set to false if you don't want to save to gallery
                      );
                    });

                  }
                  counter.reset();
                  debugPrint('Countdown Ended');
                },
                onChange: (String timeStamp) {
                  debugPrint('Countdown Changed $timeStamp');
                },
              ),
              onPressed: () async {
                if (isPhoto && _buttonLoading.value != true) {
                  // Take the Picture in a try / catch block. If anything goes wrong,
                  // catch the error.
                  try {
                    ///Doesn't need
                    // // Ensure that the camera is initialized.
                    // await _initializeControllerFuture;

                    // Attempt to take a picture
                    _buttonLoading.value = true;

                    ///These speeds up the process
                    await _controller?.setFocusMode(FocusMode.locked);
                    await _controller?.setExposureMode(ExposureMode.locked);

                    dynamic image = await _controller?.takePicture();

                    await _controller?.setFocusMode(FocusMode.auto);
                    await _controller?.setExposureMode(ExposureMode.auto);

                    _buttonLoading.value = false;

                    if (image != null) {
                      ///Prevents flash is on after taking pic/vid
                      if (isFlashOn) {
                        await _controller?.setFlashMode(FlashMode.off);
                        setState(() {
                          isFlashOn = false;
                        });
                      }

                      ///Prevents mirror effect on selfie
                      if (isFrontOn) {
                        var mirroredFile = await _mirrorPhoto(image);
                        image = new XFile(mirroredFile.path);
                      }

                      // If the picture was taken, display it on a new screen
                      callback = await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => DisplayPictureScreen(
                            // Pass the automatically generated path to
                            // the DisplayPictureScreen widget.
                              message: widget.previewMessage,
                              imagePath: image.path,
                              orientation: turns),
                        ),
                      );

                      if (callback != null) {
                        int turn = callback;

                        var modifiedPath = split(image.path, image.name).first +
                            "mod" +
                            image.name;

                        print(turn.toString());

                        ///Potential problem is here
                        XFile? rotatedResult =
                        await FlutterImageCompress.compressAndGetFile(
                          image.path,
                          modifiedPath,
                          keepExif: true,
                          rotate: turn == -1
                              ? -90
                              : turn == 1
                              ? 90
                              : turn == 2
                              ? 180
                              : 0,
                        );

                        // final img.Image? capturedImage = img.decodeImage(
                        //     File(rotatedResult!.path ?? '').readAsBytesSync());
                        // final img.Image orientedImage =
                        //     img.bakeOrientation(capturedImage!);

                        var exists =
                        await File(rotatedResult?.path ?? '').exists();

                        if (rotatedResult != null && exists) {
                          await Gal.putImage(rotatedResult.path);

                          String savedPath = await saveFileToInternalStorage(
                              rotatedResult.path);

                          print("Cache file: ${rotatedResult.path}");
                          print("Saved file: ${savedPath}");

                          Navigator.pop(context, savedPath);
                        } else {
                          Navigator.pop(context, image.path);
                          CustomToast.showToast(
                              "Rotated result doesn't exist!");
                        }
                      }
                    }
                  } catch (e) {
                    // If an error occurs, log the error to the console.
                    CustomToast.showToast('Error camera_page: $e');
                  }
                } else {
                  _recordVideo();
                }
              }),
        ),
      ),
      bottomNavigationBar: BottomAppBar(
        height: 50.h,
        color: cFirstColor,
        shape: CircularNotchedRectangle(),
        notchMargin: 4.h,
        child: new Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            ZoomTapAnimation(
              begin: 1,
              end: 0.8,
              child: Visibility(
                //Permanent false for some reasons
                visible: false,
                child: IconButton(
                  icon: Icon(
                    isPhoto
                        ? Icons.video_camera_back_outlined
                        : Icons.photo_camera_back,
                    color: cWhiteColor,
                    size: 22.h,
                  ),
                  onPressed: () {
                    if (_isRecording) {
                      _controller?.stopVideoRecording();
                      _isPaused = false;
                      _isRecording = false;
                    }
                  },
                ),
              ),
            ),
            ZoomTapAnimation(
              begin: 1,
              end: 0.8,
              child: MaterialButton(
                onPressed: () async {
                  String? videoPath = await _stopVideo();

                  if (videoPath != null) {
                    var _videoController = await VideoEditorController.file(
                        File(videoPath),
                        maxDuration: const Duration(seconds: 60))
                      ..initialize().then((_) => setState(() {}));

                    if (turns == -1) {
                      _videoController.rotate90Degrees(RotateDirection.left);
                    } else if (turns == 1) {
                      _videoController.rotate90Degrees(RotateDirection.right);
                    }
                    pd.show(
                        max: 100,
                        msg: "${LocaleKeys.video_is_saving.tr()}...",
                        msgFontSize: context.isTablet ? 13.sp : 17.sp,
                        valueFontSize: context.isTablet ? 11.sp : 15.sp,
                        borderRadius: 15.r,
                        cancel: Cancel(
                            autoHidden: true,
                            cancelImageColor:
                            Theme.of(context).textTheme.bodyMedium?.color),
                        msgColor:
                        Theme.of(context).textTheme.bodyMedium!.color!,
                        backgroundColor: Theme.of(context).cardTheme.color!);

                    ///Export video
                    Future.delayed(Duration(seconds: 1))
                        .then((value) async {
                      await exportVideoWithFFmpeg(
                        controller: _videoController,
                        context: context,
                        progressDialog: pd,
                        saveFileToInternalStorage:
                        saveFileToInternalStorage,
                        onError: (errorMessage) {
                          // Additional error handling if needed
                        },
                        saveToGallery:
                        true, // Set to false if you don't want to save to gallery
                      );
                    });

                  }
                },
                child: Visibility(
                  visible: !isPhoto,
                  child: Row(
                    children: [
                      Text("STOP",
                          style: TextStyle(
                              fontSize: 20.sp,
                              color: cWhiteColor,
                              fontFamily: 'Medium')),
                      SizedBox(
                        width: 5.w,
                      ),
                      Icon(
                        Icons.stop_circle_outlined,
                        color: cWhiteColor,
                        size: 22.h,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// A widget that displays the picture taken by the user..\
// If the picture was taken, pop and return callback path.
class DisplayPictureScreen extends StatefulWidget {
  final String imagePath;
  final int orientation;
  final String? message;

  const DisplayPictureScreen(
      {Key? key,
        required this.imagePath,
        required this.orientation,
        this.message})
      : super(key: key);

  @override
  State<DisplayPictureScreen> createState() => _DisplayPictureScreenState();
}

class _DisplayPictureScreenState extends State<DisplayPictureScreen> {
  int orientation = 0;

  @override
  void initState() {
    orientation = widget.orientation;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        extendBody: true,
        floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
        floatingActionButton: ZoomTapAnimation(
          begin: 1,
          end: 0.8,
          child: SizedBox(
            height: 55.h,
            width: 55.h,
            child: FloatingActionButton(
              backgroundColor: cFirstColor,
              shape: CircleBorder(),
              child: Icon(
                Icons.check_circle_outline,
                color: cWhiteColor,
                size: 22.h,
              ),
              onPressed: () {
                Navigator.pop(context, orientation);
              },
            ),
          ),
        ),
        appBar: AppBar(
          title: Text(LocaleKeys.taken_picture.tr(),
              style: TextStyle(
                  fontSize: context.isTablet ? 16.sp : 20.sp,
                  color: cWhiteColor)),
          toolbarHeight: 50.h,
          centerTitle: true,
          leadingWidth: 40.w,
          iconTheme: IconThemeData(size: 22.h, color: cWhiteColor),
          backgroundColor: cFirstColor,
        ),
        bottomNavigationBar: BottomAppBar(
          height: 50.h,
          color: cFirstColor,
          shape: CircularNotchedRectangle(),
          notchMargin: 4.0,
          child: new Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: <Widget>[
              Spacer(),
              InkWell(
                child: Container(
                  height: 32.h,
                  width: 32.h,
                  child: Icon(
                    Icons.rotate_90_degrees_ccw,
                    color: Colors.white,
                    size: 22.h,
                  ),
                ),
                onTap: () {
                  setState(() {
                    switch (orientation) {
                      case -1:
                        orientation = 2;
                        break;
                      case 1:
                        orientation = 0;
                        break;
                      case 2:
                        orientation = 1;
                        break;
                      case 0:
                        orientation = -1;
                        break;
                    }
                  });
                },
              ),
              Spacer(
                flex: 20,
              ),
              InkWell(
                  child: Container(
                    height: 32.h,
                    width: 32.h,
                    child: Icon(
                      Icons.rotate_90_degrees_cw_outlined,
                      color: Colors.white,
                      size: 22.h,
                    ),
                  ),
                  onTap: () {
                    setState(
                          () {
                        switch (orientation) {
                          case -1:
                            orientation = 0;
                            break;
                          case 1:
                            orientation = 2;
                            break;
                          case 2:
                            orientation = -1;
                            break;
                          case 0:
                            orientation = 1;
                            break;
                        }
                      },
                    );
                  }),
              Spacer(),
            ],
          ),
        ),
        body: Stack(
          alignment: AlignmentDirectional.topCenter,
          children: [
            Zoom(
              maxZoomWidth: 8000,
              maxZoomHeight: 8000,
              scrollWeight: 10.0,
              canvasColor: cFirstColor.withAlpha(60),
              centerOnScale: true,
              enableScroll: true,
              doubleTapZoom: true,
              zoomSensibility: 2.3,
              initZoom: 0.0,
              axis: Axis.horizontal,
              child: RotatedBox(
                quarterTurns: orientation,
                child: Container(
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      fit: BoxFit.contain,
                      image: FileImage(File(widget.imagePath)),
                    ),
                  ),
                ),
              ),
            ),
            Visibility(
              visible: widget.message != null,
              child: Padding(
                padding: EdgeInsets.all(30.h),
                child: Card(
                  child: Padding(
                    padding: EdgeInsets.all(8.h),
                    child: Text(
                      widget.message ?? '...',
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ));
  }
}

class VideoPage extends StatefulWidget {
  final String filePath;

  const VideoPage({Key? key, required this.filePath}) : super(key: key);

  @override
  _VideoPageState createState() => _VideoPageState();
}

class _VideoPageState extends State<VideoPage> {
  late VideoPlayerController _videoPlayerController;

  @override
  void dispose() {
    _videoPlayerController.dispose();
    super.dispose();
  }

  Future _initVideoPlayer() async {
    _videoPlayerController = VideoPlayerController.file(File(widget.filePath));
    await _videoPlayerController.initialize();
    await _videoPlayerController.setLooping(true);
    await _videoPlayerController.play();
  }

  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(LocaleKeys.taken_video.tr(),
            style: TextStyle(
                fontSize: context.isTablet ? 16.sp : 20.sp,
                color: cWhiteColor)),
        toolbarHeight: 50.h,
        centerTitle: true,
        leadingWidth: 40.w,
        iconTheme: IconThemeData(size: 22.h, color: cWhiteColor),
        elevation: 0,
        backgroundColor: Colors.black26,
        actions: [
          Padding(
            padding: EdgeInsets.only(right: context.isTablet ? 10.w : 0),
            child: IconButton(
              icon: Icon(
                Icons.check,
                color: cWhiteColor,
                size: 22.h,
              ),
              onPressed: () {
                Navigator.pop(context, widget.filePath);
              },
            ),
          )
        ],
      ),
      extendBodyBehindAppBar: true,
      body: FutureBuilder(
        future: _initVideoPlayer(),
        builder: (context, state) {
          if (state.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else {
            return VideoPlayer(_videoPlayerController);
          }
        },
      ),
    );
  }
}

class ThumbnailViewer extends StatefulWidget {
  final String filePath;

  ThumbnailViewer({Key? key, required this.filePath}) : super(key: key);

  @override
  _ThumbnailViewerState createState() => _ThumbnailViewerState();
}

class _ThumbnailViewerState extends State<ThumbnailViewer> {
  late final thumbnailFile;

  @override
  void dispose() {
    super.dispose();
  }

  Future _initVideoPlayer() async {
    thumbnailFile = await VideoCompress.getFileThumbnail(
      widget.filePath,
      quality: 100, // default(100)
    );
  }

  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: FutureBuilder(
        future: _initVideoPlayer(),
        builder: (context, state) {
          if (state.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else {
            return Stack(
                alignment: Alignment.center,
                fit: StackFit.expand,
                children: [
                  Image.file(thumbnailFile,
                      cacheHeight: 200, cacheWidth: 200, fit: BoxFit.cover),
                  Icon(
                    Icons.slow_motion_video,
                    size: 60.h,
                    color: cFirstColor.withAlpha(150),
                  ),
                ]);
          }
        },
      ),
    );
  }
}
