import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:animated_loading_border/animated_loading_border.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart' hide Trans;
import 'package:isar/isar.dart';
import 'package:path/path.dart' as p;
import 'package:permission_handler/permission_handler.dart';
import 'package:sn_progress_dialog/options/cancel.dart';
import 'package:sn_progress_dialog/progress_dialog.dart';
import 'package:govassist/core/database/embedded_models.dart';
import 'package:govassist/core/database/isar_service.dart';
import 'package:govassist/core/errors/failures.dart';
import 'package:govassist/core/function/functions.dart';
import 'package:govassist/core/location/location_service.dart';
import 'package:govassist/core/photo/image_picker_utils.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/core/widgets/alert_dialog.dart';
import 'package:govassist/core/widgets/custom_toast.dart';
import 'package:govassist/core/widgets/dotted_border.dart';
import 'package:govassist/core/widgets/gradient_material_button.dart';
import 'package:govassist/core/widgets/header_widget.dart';
import 'package:govassist/core/widgets/simple_material_button.dart';
import 'package:govassist/di/dependency_injection.dart';
import 'package:govassist/features/home/<USER>/models/support_model.dart';
import 'package:govassist/features/home/<USER>/navigation.dart';
import 'package:govassist/features/send_data/models/img_file_model.dart';
import 'package:govassist/features/send_data/models/not_send_model.dart';
import 'package:govassist/features/send_data/select_item.dart';
import 'package:govassist/generated/assets.dart';
import 'package:govassist/translations/locale_keys.g.dart';
import 'package:zoom_tap_animation/zoom_tap_animation.dart';

import 'camera_page.dart';
import 'widgets/voice_widget.dart';

///TODO: Fix sending anomalia here
class SendPage extends StatefulWidget {
  final String categoryId;
  final String subCategoryId;
  final String subCategoryName;
  final bool withVideo;
  final bool withMeet;
  final bool isWithAid;
  final bool isWebEdit;

  const SendPage(
      {super.key,
      required this.categoryId,
      required this.subCategoryId,
      required this.subCategoryName,
      required this.withVideo,
      required this.withMeet,
      required this.isWithAid,
      required this.isWebEdit});

  @override
  State<SendPage> createState() => _SendPageState();
}

class _SendPageState extends State<SendPage> {
  late AnimationController controllerMedia;
  late AnimationController controllerType;
  late AnimationController controllerAid;
  late AnimationController controllerCount;
  late AnimationController controllerText;
  late AnimationController controllerName;
  late ProgressDialog pd;
  late final AppLifecycleListener _listener;
  late Future<List<SupportModel>> _initFuture;
  late AndroidDeviceInfo? androidInfo;

  final FocusNode unitCodeCtrlFocusNode = FocusNode();
  final IsarService isarService = di();

  TextEditingController count = TextEditingController();
  TextEditingController text = TextEditingController();
  TextEditingController name = TextEditingController();
  String eventType = "none";
  String aidType = "none";
  bool checkWeb = false;
  bool checkAid = false;
  String? voice = "";
  String root = "";
  bool hasVideo = false;
  int videoIndex = -1;
  bool _shouldRequestPermission = false;

  String sana = "0000-00-00", latLang = "0.00,0.00";

  final customFormat = DateFormat('yyyy-MM-dd HH:mm:ss');

  List<int>? mediaBytes;
  String? mediaString;
  final ScrollController _sc = ScrollController();

  late List<ImgModel> media = [];
  List<ImgFileModel?> works = [];

  void scrollDown() {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      _sc.animateTo(_sc.position.maxScrollExtent,
          duration: const Duration(seconds: 1), curve: Curves.fastOutSlowIn);
    });
  }

  @override
  void initState() {
    // Initialize the AppLifecycleListener class and pass callbacks

    if (Platform.isAndroid) {
      androidInfo = di();
    } else {
      androidInfo = null;
    }

    _listener = AppLifecycleListener(
      onStateChange: _onStateChanged,
    );

    _initFuture = init();

    permissionWall();

    pd = ProgressDialog(context: context);

    super.initState();
  }

  // Listen to the app lifecycle state changes
  void _onStateChanged(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.detached:
        _onDetached();
        break;
      case AppLifecycleState.resumed:
        _onResumed();
        break;
      case AppLifecycleState.inactive:
        _onInactive();
        break;
      case AppLifecycleState.hidden:
        _onHidden();
        break;
      case AppLifecycleState.paused:
        _onPaused();
        break;
    }
  }

  void _onDetached() => print('detached');

  void _onResumed() {
    if (_shouldRequestPermission) {
      permissionWall();
      _shouldRequestPermission = false;
    }
  }

  void _onInactive() => print('inactive');

  void _onHidden() => print('hidden');

  void _onPaused() async {
    _shouldRequestPermission = true;
  }

  reInitializerButton() {
    setState(() {
      _initFuture = init();
    });
  }

  Future<List<SupportModel>> init() async {
    try {
      List<SupportModel> list =
          await isarService.isar.supportModels.where().findAllSync();
      return list;
    } catch (e) {
      throw Exception(e);
    }
  }

  @override
  void dispose() {
    _listener.dispose();

    text.dispose();
    count.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;

    /*24 is for notification bar on Android*/
    final double itemHeight =
        ((size.height - kToolbarHeight - 24) / (context.isPhone ? 200 : 300)).h;
    final double itemWidth = (size.width).w;

    final crossAxisCountLogic = works.length != 0
        ? works.length != 3
            ? widget.withVideo
                ? works.length + 2
                : works.length + 1
            : works.length
        : widget.withVideo
            ? 2
            : 1;
    final itemCountLogic =
        works.length + (works.length != 3 ? (widget.withVideo ? 2 : 1) : 0);

    return Scaffold(
      appBar: AppHeaderWidget(
          title: widget.subCategoryName,
          onBackTap: () async {
            var result =
                await showAlertText(context, LocaleKeys.sure_to_exit.tr()) ??
                    false;
            if (result) {
              Navigator.of(context).pop();
            }
          },
          isBackVisible: true),
      body: FutureBuilder<List<SupportModel>>(
          future: _initFuture,
          builder: (context, snapshot) {
            switch (snapshot.connectionState) {
              case ConnectionState.waiting:
                {
                  // Otherwise, display a loading indicator.
                  return Center(
                      child: CupertinoActivityIndicator(
                    color: Theme.of(context).primaryColor,
                    radius: 30.r,
                  ));
                }
              default:
                if (snapshot.hasError) {
                  return Container(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          ClipRect(
                            child: Container(
                              height: 300.h,
                              child: Column(
                                children: [
                                  SizedBox(
                                    height: 20.h,
                                  ),
                                  Expanded(
                                      child: SvgPicture.asset(
                                    Assets.iconsWarning,
                                    height: 140.h,
                                  )),
                                  Padding(
                                      padding: EdgeInsets.only(
                                          top: 10.h,
                                          left: 30.w,
                                          right: 30.w,
                                          bottom: 10.h),
                                      child: Text(
                                        LocaleKeys.error.tr(),
                                        textAlign: TextAlign.center,
                                        style: TextStyle(color: cGrayColor1),
                                      )),
                                  CupertinoButton(
                                      child: Text(
                                        LocaleKeys.refresh.tr(),
                                        style: TextStyle(color: cGrayColor1),
                                      ),
                                      color: cGrayColor1.withAlpha(80),
                                      onPressed: () {
                                        reInitializerButton();
                                      }),
                                  SizedBox(
                                    height: 20.h,
                                  )
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                } else {
                  return Column(
                    children: [
                      Expanded(
                        flex: 12,
                        child: GestureDetector(
                          onTap: () {
                            FocusScope.of(context).requestFocus(FocusNode());
                          },
                          behavior: HitTestBehavior.translucent,
                          child: SingleChildScrollView(
                            controller: _sc,
                            physics: BouncingScrollPhysics(
                                parent: AlwaysScrollableScrollPhysics()),
                            padding: EdgeInsets.symmetric(horizontal: 20.w),
                            child: Column(
                              children: [
                                SizedBox(
                                  height: 20.h,
                                ),
                                GridView.builder(
                                  shrinkWrap: true,
                                  primary: false,
                                  padding: EdgeInsets.symmetric(vertical: 10.h),
                                  physics: BouncingScrollPhysics(
                                      parent: AlwaysScrollableScrollPhysics()),
                                  gridDelegate:
                                      SliverGridDelegateWithFixedCrossAxisCount(
                                          crossAxisCount: crossAxisCountLogic,
                                          crossAxisSpacing: 10.w,
                                          mainAxisSpacing: 10.h,
                                          mainAxisExtent:
                                              (itemWidth / itemHeight)),
                                  itemCount: itemCountLogic,
                                  // scrollDirection: Axis.horizontal,
                                  itemBuilder: (context, index) {
                                    return AnimatedLoadingBorder(
                                      controller: (control) {
                                        if (index == 0) {
                                          controllerMedia = control;
                                        }
                                        control.reset();
                                      },
                                      borderColor: cFirstColor,
                                      trailingBorderColor: cSecondColor,
                                      duration: Duration(milliseconds: 800),
                                      borderWidth: 2.w,
                                      cornerRadius: cRadius16.r,
                                      child: ZoomTapAnimation(
                                        begin: 1,
                                        end: 0.8,
                                        child: widget.withVideo
                                            ? index == works.length
                                                ? mediaWidget(index)
                                                : mediaWidget(index,
                                                    isVideo: true)
                                            : mediaWidget(index),
                                      ),
                                    );
                                  },
                                ),
                                SizedBox(
                                  height: 12.h,
                                ),
                                Visibility(
                                  visible: widget.withMeet,
                                  child: Column(
                                    children: [
                                      Row(
                                        children: [
                                          Expanded(
                                            flex: 3,
                                            child: AnimatedLoadingBorder(
                                              controller: (control) {
                                                controllerType = control;
                                                control.reset();
                                              },
                                              borderColor: cFirstColor,
                                              trailingBorderColor: cSecondColor,
                                              duration: Duration(seconds: 1),
                                              borderWidth: 3.w,
                                              cornerRadius: cRadius12.r,
                                              child:
                                                  DropdownSearch<SelectObject>(
                                                itemAsString:
                                                    (SelectObject u) => u.title,
                                                popupProps: PopupProps.menu(
                                                  menuProps: MenuProps(
                                                      backgroundColor:
                                                          Theme.of(context)
                                                              .cardTheme
                                                              .color,
                                                      borderRadius:
                                                          BorderRadius.only(
                                                              bottomLeft: Radius
                                                                  .circular(
                                                                      20.r),
                                                              bottomRight: Radius
                                                                  .circular(20),
                                                              topRight:
                                                                  Radius.circular(
                                                                      10.r),
                                                              topLeft:
                                                                  Radius.circular(
                                                                      10.r))),
                                                  constraints: BoxConstraints(
                                                    maxHeight: 120.h,
                                                  ),
                                                  listViewProps: ListViewProps(
                                                      physics:
                                                          BouncingScrollPhysics()),
                                                  itemBuilder: (context,
                                                      selectedItem, bool) {
                                                    return Padding(
                                                      padding:
                                                          EdgeInsets.symmetric(
                                                              horizontal: 20.w,
                                                              vertical: 10.h),
                                                      child: Text(
                                                          selectedItem.title ??
                                                              "",
                                                          style:
                                                              Theme.of(context)
                                                                  .textTheme
                                                                  .bodyMedium),
                                                    );
                                                  },
                                                ),
                                                items: [
                                                  SelectObject(
                                                      title: LocaleKeys.common
                                                          .tr(),
                                                      key: 'many'),
                                                  SelectObject(
                                                      title: LocaleKeys
                                                          .individual
                                                          .tr(),
                                                      key: 'single'),
                                                ],
                                                dropdownDecoratorProps: DropDownDecoratorProps(
                                                    dropdownSearchDecoration: InputDecoration(
                                                        floatingLabelBehavior:
                                                            FloatingLabelBehavior
                                                                .never,
                                                        filled: true,
                                                        fillColor:
                                                            Theme.of(context)
                                                                .cardTheme
                                                                .color,
                                                        enabledBorder: OutlineInputBorder(
                                                            borderSide: BorderSide(
                                                                color:
                                                                    cFirstColor),
                                                            borderRadius: BorderRadius.circular(
                                                                cRadius12.r)),
                                                        border: OutlineInputBorder(
                                                            borderSide: BorderSide(
                                                                color:
                                                                    cFirstColor),
                                                            borderRadius:
                                                                BorderRadius.circular(cRadius12.r)),
                                                        labelText: LocaleKeys.event_type.tr(),
                                                        hintText: LocaleKeys.event_type.tr(),
                                                        labelStyle: TextStyle(fontSize: context.isTablet ? 12.sp : 16.sp, color: Theme.of(context).primaryColor),
                                                        hintStyle: TextStyle(fontSize: context.isTablet ? 12.sp : 16.sp, color: Theme.of(context).primaryColor),
                                                        contentPadding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 12.h),
                                                        suffixIconColor: Theme.of(context).iconTheme.color)),
                                                onChanged: (e) {
                                                  if (e != null) {
                                                    setState(() {
                                                      eventType = e.key;
                                                    });
                                                  }
                                                },
                                              ),
                                            ),
                                          ),
                                          Visibility(
                                            visible: eventType == 'many'
                                                ? true
                                                : false,
                                            child: SizedBox(
                                              width: 12.w,
                                            ),
                                          ),
                                          Visibility(
                                            visible: eventType == 'many'
                                                ? true
                                                : false,
                                            child: Expanded(
                                              flex: 1,
                                              child: AnimatedLoadingBorder(
                                                controller: (control) {
                                                  controllerCount = control;
                                                  control.reset();
                                                },
                                                borderColor: cFirstColor,
                                                trailingBorderColor:
                                                    cSecondColor,
                                                duration:
                                                    Duration(milliseconds: 800),
                                                borderWidth: 2.w,
                                                cornerRadius: cRadius12.r,
                                                child: Container(
                                                  child: TextField(
                                                    keyboardType:
                                                        TextInputType.number,
                                                    inputFormatters: [
                                                      FilteringTextInputFormatter
                                                          .digitsOnly
                                                    ],
                                                    controller: count,
                                                    decoration: InputDecoration(
                                                        enabledBorder:
                                                            OutlineInputBorder(
                                                          borderSide: BorderSide(
                                                              color:
                                                                  cFirstColor),
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(
                                                                      cRadius12
                                                                          .r),
                                                        ),
                                                        border:
                                                            OutlineInputBorder(
                                                          borderSide: BorderSide(
                                                              color:
                                                                  cFirstColor),
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(
                                                                      cRadius12
                                                                          .r),
                                                        ),
                                                        filled: true,
                                                        contentPadding:
                                                            EdgeInsets
                                                                .symmetric(
                                                                    horizontal:
                                                                        10.w,
                                                                    vertical:
                                                                        12.h),
                                                        hintStyle: TextStyle(
                                                            color: Colors
                                                                .grey[500]),
                                                        hintText: LocaleKeys
                                                            .count
                                                            .tr(),
                                                        fillColor:
                                                            Theme.of(context)
                                                                .cardTheme
                                                                .color),
                                                    style: Theme.of(context)
                                                        .textTheme
                                                        .bodyMedium,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(
                                        height: 20.h,
                                      ),
                                    ],
                                  ),
                                ),
                                Align(
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    LocaleKeys.comment.tr(),
                                    style: TextStyle(
                                      fontSize:
                                          context.isTablet ? 12.sp : 16.sp,
                                      fontWeight: FontWeight.w600,
                                    ),
                                    textAlign: TextAlign.left,
                                  ),
                                ),
                                SizedBox(
                                  height: 6.h,
                                ),
                                Row(
                                  children: [
                                    SizedBox(
                                      width: 5.w,
                                    ),
                                    Text(
                                      !checkWeb
                                          ? "${text.text.length}/70 - 700"
                                          : "${text.text.length}/0 - 700",
                                      style: TextStyle(
                                          fontSize:
                                              context.isTablet ? 12.sp : 16.sp,
                                          color: !checkWeb
                                              ? (text.text.length < 70 ||
                                                      text.text.length > 700)
                                                  ? cRedColor
                                                  : cFirstColor
                                              : text.text.length > 700
                                                  ? cRedColor
                                                  : cFirstColor),
                                    ),
                                    Spacer(),
                                    Icon(
                                      !checkWeb
                                          ? text.text.length < 70 ||
                                                  text.text.length > 700
                                              ? Icons.cancel
                                              : Icons.check_circle
                                          : Icons.check_circle,
                                      color: !checkWeb
                                          ? text.text.length < 70 ||
                                                  text.text.length > 700
                                              ? cCarrotColor.withAlpha(120)
                                              : cFirstColor
                                          : cFirstColor,
                                      size: 22.h,
                                    ),
                                    SizedBox(
                                      width: 5.w,
                                    ),
                                  ],
                                ),
                                SizedBox(
                                  height: 5.h,
                                ),
                                AnimatedLoadingBorder(
                                  controller: (control) {
                                    controllerText = control;
                                    control.reset();
                                  },
                                  borderColor: cFirstColor,
                                  trailingBorderColor: cSecondColor,
                                  duration: Duration(milliseconds: 800),
                                  borderWidth: 4.w,
                                  cornerRadius: cRadius12.r,
                                  child: Container(
                                    child: TextField(
                                      textAlign: TextAlign.start,
                                      inputFormatters: [
                                        FilteringTextInputFormatter.deny('   ')
                                      ],
                                      maxLines: 20,
                                      controller: text,
                                      onChanged: (_) {
                                        setState(() {});
                                      },
                                      cursorColor: cGrayColor2,
                                      decoration: InputDecoration(
                                        hintText: LocaleKeys.text.tr(),
                                        border: InputBorder.none,
                                        hintStyle: TextStyle(
                                            color: cGrayColor1,
                                            fontSize: context.isTablet
                                                ? 12.sp
                                                : 16.sp,
                                            fontFamily: 'Medium'),
                                      ),
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium,
                                    ),
                                    height: 200.h,
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 25.w),
                                    decoration: BoxDecoration(
                                        color:
                                            Theme.of(context).cardTheme.color,
                                        boxShadow: [
                                          boxShadow20,
                                        ],
                                        borderRadius:
                                            BorderRadius.circular(cRadius12.r)),
                                  ),
                                ),
                                SizedBox(
                                  height: 25.h,
                                ),
                                Visibility(
                                  visible: widget.isWebEdit,
                                  child: Column(
                                    children: [
                                      Padding(
                                        padding: EdgeInsets.only(bottom: 5.h),
                                        child: Row(
                                          children: [
                                            Transform.scale(
                                              scale: context.isTablet ? 2 : 1,
                                              child: Checkbox(
                                                value: checkWeb,
                                                materialTapTargetSize:
                                                    MaterialTapTargetSize
                                                        .shrinkWrap,
                                                onChanged: (newValue) {
                                                  setState(() {
                                                    scrollDown();
                                                    checkWeb =
                                                        newValue ?? false;
                                                  });
                                                },
                                              ),
                                            ),
                                            SizedBox(
                                              width: context.isTablet ? 5.w : 0,
                                            ),
                                            Text(
                                              LocaleKeys.web_edit.tr(),
                                              style: TextStyle(
                                                  fontSize: context.isTablet
                                                      ? 12.sp
                                                      : 16.sp,
                                                  fontWeight: FontWeight.w600),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Divider(
                                        color: cFirstColor,
                                        height: context.isTablet ? 18.h : 14.h,
                                      ),
                                    ],
                                  ),
                                ),
                                Visibility(
                                  visible: widget.isWithAid,
                                  child: Padding(
                                    padding: EdgeInsets.only(
                                        bottom: context.isTablet ? 30.h : 20.h),
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        Transform.scale(
                                          scale: context.isTablet ? 2 : 1,
                                          child: Checkbox(
                                            value: checkAid,
                                            materialTapTargetSize:
                                                MaterialTapTargetSize
                                                    .shrinkWrap,
                                            onChanged: (newValue) {
                                              setState(() {
                                                scrollDown();
                                                checkAid = newValue ?? false;
                                              });
                                            },
                                          ),
                                        ),
                                        SizedBox(
                                          width: context.isTablet ? 5.w : 0,
                                        ),
                                        Text(
                                          LocaleKeys.aid_supported.tr(),
                                          style: TextStyle(
                                              fontSize: context.isTablet
                                                  ? 12.sp
                                                  : 16.sp,
                                              fontWeight: FontWeight.w600),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                Visibility(
                                  visible: checkAid,
                                  child: Column(
                                    children: [
                                      Align(
                                        alignment: Alignment.centerLeft,
                                        child: Text(
                                          LocaleKeys.fio.tr(),
                                          style: TextStyle(
                                            fontSize: context.isTablet
                                                ? 12.sp
                                                : 16.sp,
                                            fontWeight: FontWeight.w400,
                                          ),
                                          textAlign: TextAlign.left,
                                        ),
                                      ),
                                      SizedBox(
                                        height: 6.h,
                                      ),
                                      AnimatedLoadingBorder(
                                        controller: (control) {
                                          controllerName = control;
                                          control.reset();
                                        },
                                        borderColor: cFirstColor,
                                        trailingBorderColor: cSecondColor,
                                        duration: Duration(milliseconds: 800),
                                        borderWidth: 4.w,
                                        cornerRadius: cRadius12.r,
                                        child: Container(
                                          child: Center(
                                            child: TextField(
                                              textAlign: TextAlign.start,
                                              textAlignVertical:
                                                  TextAlignVertical.center,
                                              inputFormatters: [
                                                FilteringTextInputFormatter
                                                    .deny('   ')
                                              ],
                                              maxLines: 1,
                                              controller: name,
                                              onChanged: (text) {
                                                //
                                              },
                                              cursorColor: cGrayColor2,
                                              decoration: InputDecoration(
                                                hintText:
                                                    "Aliyev Vali Alimovich",
                                                border: InputBorder.none,
                                                hintStyle: TextStyle(
                                                    color: cGrayColor1,
                                                    fontSize: context.isTablet
                                                        ? 12.sp
                                                        : 16.sp,
                                                    fontFamily: 'Medium'),
                                              ),
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .bodyMedium,
                                            ),
                                          ),
                                          padding: EdgeInsets.only(
                                              left: 10.w,
                                              right: 10.w,
                                              top: 2.h,
                                              bottom:
                                                  context.isTablet ? 8.h : 6.h),
                                          decoration: BoxDecoration(
                                              color: Theme.of(context)
                                                  .cardTheme
                                                  .color,
                                              boxShadow: [
                                                boxShadow20,
                                              ],
                                              borderRadius:
                                                  BorderRadius.circular(
                                                      cRadius12.r)),
                                        ),
                                      ),
                                      SizedBox(
                                        height: 12.h,
                                      ),
                                      AnimatedLoadingBorder(
                                        controller: (control) {
                                          controllerAid = control;
                                          control.reset();
                                        },
                                        borderColor: cFirstColor,
                                        trailingBorderColor: cSecondColor,
                                        duration: Duration(seconds: 1),
                                        borderWidth: 3.w,
                                        cornerRadius: cRadius12.r,
                                        child: Container(
                                          decoration: BoxDecoration(
                                            boxShadow: [
                                              boxShadow20,
                                            ],
                                          ),
                                          child: DropdownSearch<SupportModel>(
                                            itemAsString: (SupportModel u) =>
                                                u.titleUZ ?? ':(',
                                            clearButtonProps: ClearButtonProps(
                                                isVisible: true),
                                            popupProps: PopupProps.dialog(
                                                title: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Padding(
                                                      padding:
                                                          EdgeInsets.symmetric(
                                                              horizontal: 20.w,
                                                              vertical: 10.h),
                                                      child: Text(
                                                          LocaleKeys.aid_type
                                                              .tr(),
                                                          style:
                                                              Theme.of(context)
                                                                  .textTheme
                                                                  .bodyMedium),
                                                    ),
                                                    Divider(
                                                      color: cFirstColor,
                                                      height: 1.h,
                                                    ),
                                                  ],
                                                ),
                                                dialogProps: DialogProps(
                                                    barrierColor:
                                                        cFirstColorDark
                                                            .withAlpha(150),
                                                    backgroundColor:
                                                        Theme.of(context)
                                                            .cardTheme
                                                            .color,
                                                    shape:
                                                        RoundedRectangleBorder(
                                                      side: BorderSide(
                                                          color: Colors.white70,
                                                          width: 1),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              20.r),
                                                    )),
                                                constraints: BoxConstraints(
                                                  minWidth: 500.w,
                                                  maxWidth: 500.w,
                                                  maxHeight: 200.h,
                                                ),
                                                listViewProps: ListViewProps(
                                                  physics:
                                                      BouncingScrollPhysics(),
                                                ),
                                                itemBuilder: (context,
                                                    selectedItem, bool) {
                                                  return Padding(
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                            horizontal: 20.w,
                                                            vertical: 10.h),
                                                    child: Text(
                                                        selectedItem.titleUZ ??
                                                            "",
                                                        style: Theme.of(context)
                                                            .textTheme
                                                            .bodyMedium),
                                                  );
                                                }),
                                            items: snapshot.data ?? [],
                                            dropdownDecoratorProps:
                                                DropDownDecoratorProps(
                                              dropdownSearchDecoration: InputDecoration(
                                                  floatingLabelBehavior:
                                                      FloatingLabelBehavior
                                                          .never,
                                                  filled: true,
                                                  fillColor: Theme.of(context)
                                                      .cardTheme
                                                      .color,
                                                  enabledBorder: OutlineInputBorder(
                                                      borderSide: BorderSide(
                                                          color: Colors
                                                              .transparent),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              cRadius12.r)),
                                                  border: OutlineInputBorder(
                                                      borderSide: BorderSide(
                                                          color: cFirstColor),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              cRadius12.r)),
                                                  labelText: LocaleKeys.choose_aid_type.tr(),
                                                  hintText: LocaleKeys.choose_aid_type.tr(),
                                                  labelStyle: TextStyle(fontSize: context.isTablet ? 12.sp : 16.sp, color: Theme.of(context).primaryColor),
                                                  hintStyle: TextStyle(fontSize: context.isTablet ? 12.sp : 16.sp, color: Theme.of(context).primaryColor),
                                                  contentPadding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 12.h),
                                                  suffixIconColor: Theme.of(context).iconTheme.color),
                                            ),
                                            onChanged: (e) {
                                              if (e != null) {
                                                setState(() {
                                                  aidType = e.id ?? '';
                                                });
                                              }
                                            },
                                          ),
                                        ),
                                      ),
                                      SizedBox(
                                        height: 30.h,
                                      ),
                                    ],
                                  ),
                                ),
                                Row(
                                  children: [
                                    Expanded(
                                      child: SimpleMaterialButton(
                                        title: LocaleKeys.cancelling1.tr(),
                                        onTap: () async {
                                          var result = await showAlertText(
                                                  context,
                                                  LocaleKeys.sure_to_cancel
                                                      .tr()) ??
                                              false;
                                          if (result) {
                                            Navigator.of(context).pop();
                                          }
                                        },
                                        color: Theme.of(context).cardColor,
                                        textColor: cRedTextColor,
                                      ),
                                      flex: 1,
                                    ),
                                    SizedBox(
                                      width: 12.w,
                                    ),
                                    Expanded(
                                      child: GradientMaterialButton(
                                        title: LocaleKeys.save.tr(),
                                        onTap: () async {
                                          pd.show(
                                              max: 100,
                                              msg:
                                                  "${LocaleKeys.data_is_saving.tr()}...",
                                              msgFontSize: context.isTablet
                                                  ? 11.sp
                                                  : 15.sp,
                                              valueFontSize: context.isTablet
                                                  ? 11.sp
                                                  : 15.sp,
                                              borderRadius: 15.r,
                                              cancel: Cancel(
                                                  autoHidden: true,
                                                  cancelImageColor:
                                                      Theme.of(context)
                                                          .textTheme
                                                          .bodyMedium
                                                          ?.color),
                                              msgColor: Theme.of(context)
                                                  .textTheme
                                                  .bodyMedium!
                                                  .color!,
                                              valueColor: Theme.of(context)
                                                  .textTheme
                                                  .bodyMedium!
                                                  .color!,
                                              backgroundColor: Theme.of(context)
                                                  .cardTheme
                                                  .color!);

                                          addFile().then((value) => pd.close());
                                        },
                                      ),
                                      flex: 1,
                                    ),
                                  ],
                                ),
                                SizedBox(
                                  height: 40.h,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      Visibility(
                        visible: context.isTablet ? false : checkWeb,
                        child: SingleChildScrollView(
                          child: Container(
                            padding: EdgeInsets.only(
                                left: 5.w, right: 5.w, bottom: 5.h),
                            alignment: Alignment.center,
                            child: VoiceWidget(
                              onRecorded: (path) {
                                print("Callback: " + path);
                                Future.delayed(Duration(milliseconds: 1000),
                                    () async {
                                  // Delayed converting (important)
                                  var bytes = File(path).readAsBytesSync();
                                  // print ("saved: " + await writeFile(bytes, path.getFileName()));
                                  voice = base64Encode(bytes);
                                });
                              },
                              root: root,
                            ),
                            decoration: BoxDecoration(
                                color: Theme.of(context).cardTheme.color,
                                borderRadius: BorderRadius.only(
                                    topRight: Radius.circular(cRadius12.r),
                                    topLeft: Radius.circular(cRadius12.r)),
                                border: Border.all(
                                    color: cFirstColor.withAlpha(60),
                                    width: 1.5.w)),
                          ),
                        ),
                      ),
                    ],
                  );
                }
            }
          }),
    );
  }

  ///========= Internal functions =========

  animate(AnimationController controller) {
    controller.repeat();
    Future.delayed(Duration(seconds: 2)).then((value) {
      controller.reset();
    });
  }

  Future<Map<Permission, PermissionStatus>> requestPermissions() async {
    var verSDK = androidInfo?.version.sdkInt ?? 0;
    Map<Permission, PermissionStatus> statuses = await [
      Platform.isAndroid && verSDK < 33
          ? Permission.storage
          : Permission.photos,
      Permission.location,
      Permission.camera,
      Permission.microphone,
    ].request();

    return statuses;
  }

  void permissionWall() async {
    Map<Permission, PermissionStatus> statuses = {};
    var verSDK = androidInfo?.version.sdkInt ?? 0;
    statuses = await requestPermissions();

    if (statuses[Permission.location] != PermissionStatus.granted ||
        statuses[Platform.isAndroid && verSDK < 33
                ? Permission.storage
                : Permission.photos] !=
            PermissionStatus.granted ||
        statuses[Permission.camera] != PermissionStatus.granted ||
        statuses[Permission.microphone] != PermissionStatus.granted) {
      if (statuses[Permission.location] == PermissionStatus.permanentlyDenied ||
          statuses[Platform.isAndroid && verSDK < 33
                  ? Permission.storage
                  : Permission.photos] ==
              PermissionStatus.permanentlyDenied ||
          statuses[Permission.camera] == PermissionStatus.permanentlyDenied ||
          statuses[Permission.microphone] ==
              PermissionStatus.permanentlyDenied) {
        print(statuses[Permission.camera]);
        print(statuses[Permission.storage]);
        print(statuses[Permission.microphone]);
        print(statuses[Permission.location]);

        ///Shows custom dialog after user refuses for giving of any permissions
        showCustomDialog(context);
      } else {
        ///Points to the recursion
        permissionWall();
      }
    } else {
      /// If everything is granted, getting location and date
      getInfo();
    }
  }

  getInfo() async {
    try {
      var position = await determinePosition();
      latLang = "${position.latitude},${position.longitude}";
      sana = customFormat.format(DateTime.now()).toString();

      var lat = latLang.split(",").first;
      var lng = latLang.split(",").last;

      print(double.parse(lat));
      print(double.parse(lng));
    } on LocationServiceDisabledException catch (e) {
      return LocaleKeys.give_accurate_location_permission.tr();
    } catch (e) {
      print(e);
      return e.toString();
    }
  }

  takeCam(int index, bool withVideo) async {
    if (works.length < 3) {
      if (withVideo ? !hasVideo : true) {
        File? mediaFile;
        final picker = di<ImagePickerUtils>();

        mediaFile = File(
            await picker.selectImageFromCamera(context, isVideo: withVideo));
        mediaFile.path == "" ? mediaFile = null : mediaFile;
        sana = customFormat.format(DateTime.now()).toString();

        if (mediaFile != null) {
          if (latLang != "0.00,0.00") {
            ///TODO: this line causes exception
            setState(() {
              works.add(
                  ImgFileModel(latLang: latLang, sana: sana, image: mediaFile));
              if (p.extension(works.last?.image?.path ?? '') == '.mp4') {
                hasVideo = true;
                videoIndex = index;
              }
            });
          } else {
            CustomToast.showToast(
                "Aniq lokatsiya olinmadi, birozdan so'ng qayta urining...");
            permissionWall();
          }
        } else {
          CustomToast.showToast(LocaleKeys.content_empty.tr());
          permissionWall();
        }
      } else {
        CustomToast.showToast(LocaleKeys.only_one_video.tr());
      }
    } else {
      CustomToast.showToast(LocaleKeys.maximum_video.tr());
    }
  }

  Widget mediaWidget(int index, {bool isVideo = false}) {
    unitCodeCtrlFocusNode.unfocus();

    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () async {
        // You can request multiple permissions at once.
        Map<Permission, PermissionStatus> statuses = await [
          Permission.location,
          Permission.camera,
          Permission.microphone,
        ].request();

        if (statuses[Permission.location] == PermissionStatus.granted &&
            statuses[Permission.camera] == PermissionStatus.granted &&
            statuses[Permission.microphone] == PermissionStatus.granted) {
          takeCam(index, isVideo);
        } else {
          permissionWall();
        }
      },
      child: !works.asMap().containsKey(index) || works[index]?.image == ''
          ? DottedBorderWidget(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgPicture.asset(
                      isVideo ? Assets.iconsCamera2 : Assets.iconsImageUpload,
                      height: 30.h,
                      width: 30.w,
                      color: cFirstColor,
                    ),
                    SizedBox(
                      height: 4.h,
                    ),
                    Text(
                      !isVideo
                          ? LocaleKeys.picture.tr()
                          : LocaleKeys.video.tr(),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          color: cFirstColor,
                          fontWeight: FontWeight.w500,
                          fontSize: context.isTablet ? 12.sp : 16.sp),
                    )
                  ],
                ),
              ),
            )
          : Stack(
              fit: StackFit.expand,
              children: [
                ClipRRect(
                    borderRadius: BorderRadius.circular(12.r),
                    child:
                        p.extension(works[index]?.image?.path ?? '') == '.mp4'
                            ? ThumbnailViewer(
                                filePath: works[index]?.image?.path ?? '')
                            : Image.file(
                                works[index]?.image ?? File(''),
                                fit: BoxFit.cover,
                              )),
                Align(
                  alignment: Alignment.center,
                  child: InkWell(
                    onTap: () {
                      setState(() {
                        p.extension(works[index]?.image?.path ?? '') == '.mp4'
                            ? hasVideo = false
                            : null;
                        works.removeAt(index);
                      });
                    },
                    child: SvgPicture.asset(
                      Assets.iconsDelete,
                      color: cRedColor,
                      width: 40.w,
                      height: 40.h,
                    ),
                  ),
                ),
              ],
            ),
    );
  }

  Future addFile() async {
    var now = DateTime.now();
    var time = (DateFormat('yyyy-MM-dd_HH-mm-ss').format(now));
    try {
      media.clear();

      if (works.isNotEmpty) {
        await Future.wait(works.map((e) async {
          if (p.extension(e?.image?.path ?? '') != '.mp4') {
            var doExist = await File(e?.image?.path ?? '').exists();

            if (doExist) {
              mediaBytes = await e?.image?.readAsBytes();
              mediaString = base64Encode(mediaBytes!);
              media.add(ImgModel(
                  latLang: e?.latLang, sana: e?.sana, image: mediaString));
            } else {
              setState(() {
                works.remove(e);
              });
              CustomToast.showToast("Fayl o'chib ketgan, qayta qo'shing");
            }
          } else {
            var path = e?.image?.path;
            print("Video path: ${e?.image?.path}");
            media
                .add(ImgModel(latLang: e?.latLang, sana: e?.sana, image: path));
          }
        }));
      }

      if (media.isNotEmpty &&
          (checkAid
              ? aidType == 'none'
                  ? false
                  : true
              : true) &&
          (checkAid
              ? name.text.isEmpty
                  ? false
                  : true
              : true) &&
          (eventType == 'none'
              ? widget.withMeet
                  ? false
                  : true
              : eventType == 'many'
                  ? count.text.isNotEmpty
                  : count.text.isEmpty)) {
        ///Close the Progress dialog
        pd.close();

        if (checkWeb != true && text.text.length < 70) {
          animate(controllerText);
          CustomToast.showToast(LocaleKeys.need_70_letters.tr());
        } else if (text.text.length > 700) {
          animate(controllerText);
          CustomToast.showToast(LocaleKeys.maximum_letter.tr());
        } else {
          NotSendModel work = NotSendModel(
              subCategoryTitle: widget.subCategoryName,
              categoryId: widget.categoryId.toString(),
              subCategoryId: widget.subCategoryId.toString(),
              webEdit: checkWeb,
              mediaList: media,
              desc: text.text,
              supportedPerson: name.text.isEmpty ? null : name.text,
              supportType: aidType == 'none' ? null : aidType,
              publicCount: eventType == 'none'
                  ? 0
                  : eventType != 'single'
                      ? int.parse(count.text)
                      : 1,
              sound: voice != "" ? voice : null,
              status: checkWeb == true ? 1 : 2);

          try {
            try {
              await isarService.isar.writeTxn(() async {
                isarService.isar.notSendModels.put(work);
              });

              await isarService.isar.writeTxn(() async {
                print(isarService.isar.notSendModels.where().findAll());
              });
              Get.offAll(BottomNavigationPage(
                pageIndex: 1,
                tabIndex: 0,
              ));

              if (works.isNotEmpty) {
                await Future.wait(works.map((e) async {
                  if (p.extension(e?.image?.path ?? '') != '.mp4') {
                    await deleteFileFromInternalStorage(e?.image?.path ?? '');
                  } else {
                    ///Delete video later
                  }
                }));
              }

              return true;
            } catch (e) {
              print(e);
              return false;
            }
          } catch (e) {
            debugPrint(e.toString());
            CustomToast.showToast(LocaleKeys.error_in_uploading.tr());
          }
        }
      } else {
        if (checkWeb != true && text.text.isEmpty) {
          animate(controllerText);
        }
        if (checkAid == true && name.text.isEmpty) {
          animate(controllerName);
        }

        if (checkAid == true && aidType == 'none') {
          animate(controllerAid);
        }

        if (media.isEmpty) {
          animate(controllerMedia);
        }

        if (eventType == 'none') {
          widget.withMeet ? animate(controllerType) : true;
        }
        if (count.text.isEmpty) {
          eventType == 'many' ? animate(controllerCount) : true;
        }

        pd.close();
        CustomToast.showToast(LocaleKeys.enter_necessary_data.tr());
      }
    } on InputFormatterFailure catch (e) {
      debugPrint(LocaleKeys.error_in_uploading.tr());
      pd.close();
      return;
    } on PathNotFoundException catch (e) {
      print(e);
    } catch (e) {
      CustomToast.showToast("${LocaleKeys.error1.tr()}: $e");
      print("${LocaleKeys.error1.tr()}: $e");
      pd.close();
    }
  }
}
