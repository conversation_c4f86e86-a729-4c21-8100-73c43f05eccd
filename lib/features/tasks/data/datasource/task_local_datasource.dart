import 'package:govassist/features/tasks/data/model/special_new_task.dart';
import 'package:isar/isar.dart';
import 'package:govassist/core/database/isar_service.dart';

abstract class TaskLocalDataSource {
  Future<List<SpecialNewTaskItem>> getSpecialNewTasks();

  Future<bool> setSpecialNewTasks(List<SpecialNewTaskItem> list, bool refresh);
}

class TaskLocalDataSourceImpl extends TaskLocalDataSource {
  final IsarService isarService;

  TaskLocalDataSourceImpl({required this.isarService});

  @override
  Future<List<SpecialNewTaskItem>> getSpecialNewTasks({bool? isDone}) async {
    try {
      List<SpecialNewTaskItem> list =
          await isarService.isar.specialNewTaskItems.where().findAll();

      return list;
    } catch (e) {
      throw Exception(e);
    }
  }

  @override
  Future<bool> setSpecialNewTasks(
      List<SpecialNewTaskItem> list, bool refresh) async {
    print("coming...");
    try {
      await isarService.isar.writeTxnSync(() {
        if (refresh) {
          isarService.isar.specialNewTaskItems.where().deleteAllSync();
          isarService.isar.specialNewTaskItems.putAllSync(list);
        } else {
          isarService.isar.specialNewTaskItems.putAllSync(list);
        }
      });
      return true;
    } catch (e) {
      print("ISAR ERROR:$e");
      return false;
    }
  }
}
