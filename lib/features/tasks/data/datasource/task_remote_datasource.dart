import 'package:dio/dio.dart';
import 'package:govassist/core/utils/api_path.dart';
import 'package:govassist/features/tasks/data/model/special_new_task.dart';

abstract class TaskRemoteDatasource {
  Future<SpecialNewTask> getSpecialNewTasks({required int pageKey});
}

class TaskRemoteDatasourceImpl extends TaskRemoteDatasource {
  final Dio dio;

  TaskRemoteDatasourceImpl({required this.dio});

  @override
  Future<SpecialNewTask> getSpecialNewTasks({required int pageKey}) async {
    try {
      var response = await dio.post(specialTaskPath,
          data: {"done": false, "limit": 10, "page": pageKey});
      print(SpecialNewTask.fromJson(response.data));
      return SpecialNewTask.fromJson(response.data);
    } on DioException catch (e) {
      print("ERROR:${e}");
      throw e;
    }
  }
}
