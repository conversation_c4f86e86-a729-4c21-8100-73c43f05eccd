import 'package:dio/dio.dart';
import 'package:govassist/core/utils/api_path.dart';
import 'package:govassist/features/tasks/data/model/special_done_task.dart';

abstract class TaskDoneRemoteDatasource {
  Future<SpecialDoneTask> getSpecialNewTasks({required int pageKey});
}

class TaskDoneRemoteDatasourceImpl extends TaskDoneRemoteDatasource {
  final Dio dio;

  TaskDoneRemoteDatasourceImpl({required this.dio});

  @override
  Future<SpecialDoneTask> getSpecialNewTasks({required int pageKey}) async {
    try {
      var response = await dio.post(specialTaskPath,
          data: {"done": true, "limit": 10, "page": pageKey});
      return SpecialDoneTask.fromJson(response.data);
    } on DioException catch (e) {
      print("ERROR:${e}");
      throw e;
    }
  }
}
