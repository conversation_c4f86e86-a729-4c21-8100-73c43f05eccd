import 'package:govassist/features/tasks/data/model/special_done_task.dart';
import 'package:isar/isar.dart';
import 'package:govassist/core/database/isar_service.dart';

abstract class TaskDoneLocalDataSource {
  Future<List<SpecialDoneTaskItem>> getSpecialNewTasks();

  Future<bool> setSpecialDoneTasks(List<SpecialDoneTaskItem> list, bool refresh);
}

class TaskDoneLocalDataSourceImpl extends TaskDoneLocalDataSource {
  final IsarService isarService;

  TaskDoneLocalDataSourceImpl({required this.isarService});

  @override
  Future<List<SpecialDoneTaskItem>> getSpecialNewTasks({bool? isDone}) async {
    try {
      List<SpecialDoneTaskItem> list =
          await isarService.isar.specialDoneTaskItems.where().findAll();

      return list;
    } catch (e) {
      throw Exception(e);
    }
  }

  @override
  Future<bool> setSpecialDoneTasks(
      List<SpecialDoneTaskItem> list, bool refresh) async {
    print("coming...");
    try {
      await isarService.isar.writeTxnSync(() {
        if (refresh) {
          isarService.isar.specialDoneTaskItems.where().deleteAllSync();
          isarService.isar.specialDoneTaskItems.putAllSync(list);
        } else {
          isarService.isar.specialDoneTaskItems.putAllSync(list);
        }
      });
      return true;
    } catch (e) {
      print("ISAR ERROR:$e");
      return false;
    }
  }
}
