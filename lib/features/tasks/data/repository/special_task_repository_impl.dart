import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:govassist/core/errors/failures.dart';
import 'package:govassist/features/tasks/data/datasource/task_local_datasource.dart';
import 'package:govassist/features/tasks/data/datasource/task_remote_datasource.dart';
import 'package:govassist/features/tasks/data/model/special_new_task.dart';
import 'package:govassist/features/tasks/domain/repository/special_task_repository.dart';
import 'package:govassist/translations/locale_keys.g.dart';

import '../../../../core/network/network_info.dart';

class SpecialNewTaskRepositoryImpl extends SpecialNewTaskRepository {
  final TaskRemoteDatasourceImpl taskRemoteDatasourceImpl;
  final TaskLocalDataSourceImpl taskLocalDataSourceImpl;
  final NetworkInfo networkInfo;

  SpecialNewTaskRepositoryImpl(
      {required this.taskRemoteDatasourceImpl,
      required this.taskLocalDataSourceImpl,
      required this.networkInfo});

  @override
  Future<Either<Failure, SpecialNewTask>> getSpecialNewTasks(
      {required int pageKey, required bool refresh}) async {
    if (await networkInfo.isConnected) {
      try {
        SpecialNewTask specialNewTask =
            await taskRemoteDatasourceImpl.getSpecialNewTasks(pageKey: pageKey);
        taskLocalDataSourceImpl.setSpecialNewTasks(
            specialNewTask.docs ?? [], refresh);
        return Right(specialNewTask);
      } catch (e) {
        if (e is DioException) {
          if (e.response?.statusCode == 400) {
            return Left(
                ServerFailure(e.response?.data['message'].toString() ?? ""));
          } else if (e.type == DioExceptionType.connectionTimeout) {
            return Left(ServerFailure("Time out exception" ?? ""));
          } else {
            return Left(ServerFailure(LocaleKeys.error_download_data.tr()));
          }
        } else {
          return Left(ServerFailure(LocaleKeys.error_download_data.tr()));
        }
      }
    } else {
      try {
        List<SpecialNewTaskItem> list =
            await taskLocalDataSourceImpl.getSpecialNewTasks();
        return Right(SpecialNewTask(docs: list, page: 1, totalPages: 1));
      } catch (e) {
        return Left(ServerFailure(LocaleKeys.error_download_data.tr()));
      }
    }
  }
}
