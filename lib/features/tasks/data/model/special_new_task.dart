import 'package:govassist/features/tasks/data/model/user.dart';
import 'package:isar/isar.dart';
part 'special_new_task.g.dart';
class SpecialNewTask {
  SpecialNewTask({
    this.docs,
    this.totalDocs,
    this.limit,
    this.totalPages,
    this.page,
    this.pagingCounter,
    this.hasPrevPage,
    this.hasNextPage,
    this.prevPage,
    this.nextPage,
  });

  SpecialNewTask.fromJson(dynamic json) {
    if (json['docs'] != null) {
      docs = [];
      json['docs'].forEach((v) {
        docs?.add(SpecialNewTaskItem.fromJson(v));
      });
    }
    totalDocs = json['totalDocs'];
    limit = json['limit'];
    totalPages = json['totalPages'];
    page = json['page'];
    pagingCounter = json['pagingCounter'];
    hasPrevPage = json['hasPrevPage'];
    hasNextPage = json['hasNextPage'];
    prevPage = json['prevPage'];
    nextPage = json['nextPage'];
  }

  List<SpecialNewTaskItem>? docs;
  int? totalDocs;
  int? limit;
  int? totalPages;
  int? page;
  int? pagingCounter;
  bool? hasPrevPage;
  bool? hasNextPage;
  int? prevPage;
  dynamic nextPage;
}

@collection
@Name("SpecialNewTask")
class SpecialNewTaskItem {
  SpecialNewTaskItem({
    this.id,
    this.titleUZ,
    this.titleRU,
    this.titleQQ,
    this.desc,
    this.forReference,
    this.withPhoto,
    this.withVideo,
    this.withText,
    this.withFile,
    this.telegramGroup,
    this.file,
    this.user,
    this.role,
    this.date,
    this.createdAt,
    this.updatedAt,
  });

  SpecialNewTaskItem.fromJson(dynamic json) {
    id = json['_id'];
    titleUZ = json['titleUZ'];
    titleRU = json['titleRU'];
    titleQQ = json['titleQQ'];
    desc = json['desc'];
    forReference = json['forReference'];
    withPhoto = json['withPhoto'];
    withVideo = json['withVideo'];
    withText = json['withText'];
    withFile=json['withFile'];
    telegramGroup = json['telegramGroup'];
    file = json['file'];
    user = json['user']!=null?User.fromJson(json['user']):null;
    role = json['role'];
    date = json['date'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }
  Id localId = Isar.autoIncrement;
  String? id;
  String? titleUZ;
  String? titleRU;
  String? titleQQ;
  String? desc;
  bool? forReference;
  bool? withPhoto;
  bool? withVideo;
  bool? withText;
  bool? withFile;
  bool? telegramGroup;
  String? file;
  User? user;
  String? role;
  String? date;
  String? createdAt;
  String? updatedAt;

}
