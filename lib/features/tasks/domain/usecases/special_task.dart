import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:govassist/core/errors/failures.dart';
import 'package:govassist/core/usescases/usecase.dart';
import 'package:govassist/features/tasks/data/model/special_new_task.dart';
import 'package:govassist/features/tasks/domain/repository/special_task_repository.dart';

class SpecialNewTaskUseCase
    extends UseCase<SpecialNewTask, SpecialDoneTaskParams> {
  final SpecialNewTaskRepository specialNewTaskRepository;

  SpecialNewTaskUseCase({required this.specialNewTaskRepository});

  @override
  Future<Either<Failure, SpecialNewTask>> call(SpecialDoneTaskParams params) {
    return specialNewTaskRepository.getSpecialNewTasks(
        pageKey: params.pageKey, refresh: params.refresh);
  }
}

class SpecialDoneTaskParams extends Equatable {
  final int pageKey;
  final bool refresh;

  SpecialDoneTaskParams({required this.pageKey, required this.refresh});

  @override
  List<Object?> get props => [pageKey, refresh];
}
