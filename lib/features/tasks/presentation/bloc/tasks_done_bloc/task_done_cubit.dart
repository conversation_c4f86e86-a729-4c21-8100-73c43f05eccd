import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:govassist/features/tasks/data/model/special_done_task.dart';
import 'package:govassist/features/tasks/domain/usecases/special_done_task.dart';

part 'task_done_state.dart';

class TaskDoneCubit extends Cubit<SpecialTaskDoneState> {
  final SpecialDoneTaskUseCase specialDoneTaskUseCase;

  TaskDoneCubit({required this.specialDoneTaskUseCase})
      : super(SpecialTaskDoneState.initial());

  void getTasks({required int pageKey, bool refresh = false}) async {
    var result;
    if (refresh) {
      emit(state.copyWith(status: TaskDoneStatus.loading));
    }
    result = await specialDoneTaskUseCase
        .call(SpecialDoneTaskParams(pageKey: pageKey, refresh: refresh));
    result.fold((left) {
      emit(state.copyWith(
          status: TaskDoneStatus.failure, message: left.message));
    }, (right) {
      emit(state.copyWith(
          status: TaskDoneStatus.success, specialNewTask: right));
    });
  }
}
