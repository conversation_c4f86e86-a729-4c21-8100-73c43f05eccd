import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:govassist/features/tasks/data/model/special_new_task.dart';
import 'package:govassist/features/tasks/domain/usecases/special_task.dart';

part 'task_new_state.dart';

class TaskNewCubit extends Cubit<SpecialNewTaskNewState> {
  final SpecialNewTaskUseCase specialNewTaskUseCase;

  TaskNewCubit({required this.specialNewTaskUseCase})
      : super(SpecialNewTaskNewState.initial());

  void getTasks({required int pageKey, bool refresh = false}) async {
    var result;
    if (refresh) {
      emit(state.copyWith(status: TaskNewStatus.loading));
    }
    result = await specialNewTaskUseCase
        .call(SpecialDoneTaskParams(pageKey: pageKey, refresh: refresh));
    result.fold((left) {
      emit(
          state.copyWith(status: TaskNewStatus.failure, message: left.message));
    }, (right) {
      emit(
          state.copyWith(status: TaskNewStatus.success, specialNewTask: right));
    });
  }
}
