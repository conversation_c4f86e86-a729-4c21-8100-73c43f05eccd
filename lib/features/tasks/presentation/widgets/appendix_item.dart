import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart' hide Trans;
import 'package:open_filex/open_filex.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:govassist/core/function/functions.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/core/widgets/custom_toast.dart';
import 'package:govassist/core/widgets/permission_dialog.dart';
import 'package:govassist/features/tasks/presentation/bloc/download/download_file_cubit.dart';
import 'package:govassist/generated/assets.dart';
import 'package:govassist/translations/locale_keys.g.dart';

class AppendixItem extends StatefulWidget {
  final String? link;

  const AppendixItem({super.key, required this.link});

  @override
  State<AppendixItem> createState() => _AppendixItemState();
}

class _AppendixItemState extends State<AppendixItem> {
  @override
  void initState() {
    super.initState();
    BlocProvider.of<DownloadFileCubit>(context)
        .emitInitialState(widget.link ?? "");
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: 60.h,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(cRadius8.r),
          color: isDark() ? cCardDarkColor : cGrayColor0),
      child: MaterialButton(
        padding: EdgeInsets.zero,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(cRadius8.r)),
        onPressed: () async {
          bool fileExist = await checkFileExist(widget?.link ?? "");
          if (fileExist) {
            OpenFilex.open(
                File("${androidDownloadPath}${widget.link?.split("/").last}")
                    .path);
          } else {
            DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
            AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
            var sdkInt = androidInfo.version.sdkInt ?? 0;
            bool storage = await Permission.storage.shouldShowRequestRationale;
            Map<Permission, PermissionStatus> statuses = {};
            statuses = await requestPermissionsForFile();
            if (sdkInt < 33
                ? statuses[Permission.storage] != PermissionStatus.granted
                : false) {
              if (((sdkInt < 33
                      ? statuses[Permission.storage] ==
                          PermissionStatus.permanentlyDenied
                      : false) &&
                  !storage)) {
                showDialog(
                    context: context,
                    builder: (context) {
                      return PermissionDialog(
                        icon: Icons.folder,
                        text: LocaleKeys.give_permission.tr(),
                      );
                    });
              }
            } else {
              BlocProvider.of<DownloadFileCubit>(context)
                  .download(widget.link ?? "", "");
            }
          }
        },
        child: Row(
          children: [
            SizedBox(
              width: 20.w,
            ),
            BlocConsumer<DownloadFileCubit, DownloadFileState>(
                listener: (context, state) {
              if (state is DownloadFileError) {
                CustomToast.showToast(LocaleKeys.download_error.tr());
              }
            }, builder: (context, state) {
              if (state is DownloadFileInitial) {
                return state.isFileExist
                    ? SvgPicture.asset(
                        Assets.iconsDocument,
                        width: 20.h,
                        height: 20.h,
                        colorFilter: ColorFilter.mode(
                            Theme.of(context).iconTheme.color!,
                            BlendMode.srcIn),
                      )
                    : Icon(
                        Icons.download,
                        size: 30.h,
                        color: Theme.of(context).iconTheme.color!,
                      );
              } else if (state is DownloadFileLoading) {
                return Center(
                  child: CupertinoActivityIndicator(
                    radius: 12.h,
                  ),
                );
              } else if (state is DownloadFileSuccess) {
                return Center(
                  child: SvgPicture.asset(
                    Assets.iconsDocument,
                    colorFilter: ColorFilter.mode(
                        Theme.of(context).iconTheme.color!, BlendMode.srcIn),
                    width: 20.h,
                    height: 20.h,
                  ),
                );
              } else {
                return Center(
                  child: Icon(
                    Icons.download,
                    size: 30.h,
                    color: Theme.of(context).iconTheme.color!,
                  ),
                );
              }
            }),
            SizedBox(
              width: 20.w,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    widget.link?.split('/').last ?? "",
                    style: TextStyle(
                        fontSize: context.isTablet ? 12.sp : 16.sp,
                        color: Theme.of(context).textTheme.bodyMedium?.color),
                  ),
                ],

              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<bool> checkFileExist(String fileName) async {
    String url = "${androidDownloadPath}${fileName.split("/").last}";
    if (await File(url).exists()) {
      return true;
    } else {
      return false;
    }
  }
}
