import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart' hide Trans;
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/core/widgets/header_widget.dart';
import 'package:govassist/features/drawer/presentation/page/drawer_page.dart';
import 'package:govassist/features/tasks/presentation/pages/done_task_page.dart';
import 'package:govassist/features/tasks/presentation/pages/new_task_page.dart';
import 'package:govassist/translations/locale_keys.g.dart';

class TaskMainPage extends StatefulWidget {


  const TaskMainPage({super.key});

  @override
  State<TaskMainPage> createState() => _TaskMainPageState();
}

class _TaskMainPageState extends State<TaskMainPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(vsync: this, initialIndex: 0, length: 2);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: DrawerPage(),
      appBar: AppHeaderWidget(
        title: LocaleKeys.tasks.tr(),
        onBackTap: () {
          Get.back();
        },
        isBackVisible: true,
      ),
      body: Column(
        children: [
          Container(
            margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
            padding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 3.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(cRadius10.r),
              color: Theme
                  .of(context)
                  .cardTheme
                  .color,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  spreadRadius: 4,
                  blurRadius: 5,
                  offset: Offset(0, 4), // changes position of shadow
                ),
              ],
            ),
            child: TabBar(
              dividerColor: Colors.transparent,
              indicatorSize: TabBarIndicatorSize.tab,
              indicatorPadding: EdgeInsets.zero,
              controller: _tabController,
              indicatorColor: Colors.transparent,
              unselectedLabelColor:
              Theme
                  .of(context)
                  .textTheme
                  .bodySmall
                  ?.color,
              labelColor: cWhiteColor,
              indicator: BoxDecoration(
                color: cFirstColor,
                borderRadius: BorderRadius.circular(cRadius10.r),
              ),
              tabs: [
                Tab(
                  height: context.isTablet ? 50.h : 40.h,
                  child: Text(
                    LocaleKeys.new_word.tr(),
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 16.sp,
                    ),
                  ),
                ),
                Tab(
                  height: context.isTablet ? 50.h : 40.h,
                  child: Text(
                    LocaleKeys.done.tr(),
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 16.sp,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
              child: TabBarView(
                  controller: _tabController,
                  children: [NewTaskPage.screen(),DoneTaskPage.screen()]))
        ],
      ),
    );
  }
}
