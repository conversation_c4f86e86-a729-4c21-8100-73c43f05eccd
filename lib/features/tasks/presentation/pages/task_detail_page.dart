import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart' hide Trans;
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/core/widgets/custom_toast.dart';
import 'package:govassist/core/widgets/gradient_material_button.dart';
import 'package:govassist/core/widgets/header_widget_sub.dart';
import 'package:govassist/di/dependency_injection.dart';
import 'package:govassist/features/face_control/facedetectionview.dart';
import 'package:govassist/features/tasks/data/model/special_new_task.dart';
import 'package:govassist/features/tasks/presentation/bloc/check_file_exist_cubit/check_file_exist_cubit.dart';
import 'package:govassist/features/tasks/presentation/bloc/confirm/confirm_cubit.dart';
import 'package:govassist/features/tasks/presentation/bloc/download/download_file_cubit.dart';
import 'package:govassist/features/tasks/presentation/widgets/appendix_item.dart';
import 'package:govassist/generated/assets.dart';
import 'package:govassist/translations/locale_keys.g.dart';

import '../../../task_send/presentation/pages/task_send_page.dart';

class TaskDetailPage extends StatefulWidget {
  final SpecialNewTaskItem specialNewTaskItem;
  final bool isDone;

  static Widget screen(SpecialNewTaskItem specialNewTaskItem, bool isDone) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => di<DownloadFileCubit>(),
        ),
        BlocProvider(
          create: (context) => CheckFileExistCubit(),
        ),
        BlocProvider(
          create: (context) => ConfirmCubit(),
        ),
      ],
      child: TaskDetailPage(
        specialNewTaskItem: specialNewTaskItem,
        isDone: isDone,
      ),
    );
  }

  TaskDetailPage(
      {super.key, required this.specialNewTaskItem, required this.isDone});

  @override
  State<TaskDetailPage> createState() => _TaskDetailPageState();
}

class _TaskDetailPageState extends State<TaskDetailPage> {
  final DateFormat formatterDate = DateFormat('dd.MM.yyyy');

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppHeaderWidgetSub(
        title: LocaleKeys.task.tr(),
        onBackTap: () {
          Navigator.pop(context);
        },
        actionImage: Assets.iconsPlaceholder,
        onActionTap: () {},
      ),
      body: Container(
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 30.h,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "${LocaleKeys.task_giver.tr()}:",
                          style: Theme.of(context)
                              .textTheme
                              .bodyMedium
                              ?.copyWith(
                                  fontWeight: FontWeight.w400, fontSize: 16.sp),
                        ),
                        Flexible(
                          child: Text(
                            "${widget.specialNewTaskItem.user?.lastName ?? ""} ${widget.specialNewTaskItem.user?.firstName ?? ""} ${widget.specialNewTaskItem.user?.middleName ?? ""}",
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    fontSize: 16.sp),
                            textAlign: TextAlign.end,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 24.h,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "${LocaleKeys.given_date.tr()}:",
                          style: Theme.of(context)
                              .textTheme
                              .bodyMedium
                              ?.copyWith(
                                  fontWeight: FontWeight.w400, fontSize: 16.sp),
                        ),
                        Text(
                          formatterDate.format(DateTime.parse(
                              widget.specialNewTaskItem.updatedAt ??
                                  "1998-10-16")),
                          style: Theme.of(context)
                              .textTheme
                              .bodyMedium
                              ?.copyWith(
                                  fontWeight: FontWeight.w600, fontSize: 16.sp),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 24.h,
                    ),
                    Text(
                      widget.specialNewTaskItem.desc ?? EMPTY,
                      textAlign: TextAlign.left,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w400, fontSize: 16.sp),
                    ),
                    SizedBox(
                      height: 20.h,
                    ),
                    Visibility(
                      visible: (widget.specialNewTaskItem.file != null &&
                              widget.specialNewTaskItem.file?.isNotEmpty ==
                                  true)
                          ? true
                          : false,
                      child: Column(
                        children: [
                          AppendixItem(
                            link: widget.specialNewTaskItem.file,
                          ),
                          SizedBox(
                            height: 20.h,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Column(
              children: [
                Visibility(
                  visible: widget.isDone,
                  child: widget.specialNewTaskItem.forReference == true
                      ? BlocConsumer<ConfirmCubit, ConfirmState>(
                          listener: (context, state) {
                            if (state.status == ConfirmStatus.success) {
                              Navigator.pop(context);
                            } else if (state.status == ConfirmStatus.failure) {
                              CustomToast.showToast(state.message ??
                                  LocaleKeys.download_error.tr());
                            }
                          },
                          builder: (context, state) {
                            return GradientMaterialButton(
                              child: _button(state),
                              onTap: () {
                                BlocProvider.of<ConfirmCubit>(context).confirm(
                                    widget.specialNewTaskItem.id ?? "-1");
                              },
                              width: MediaQuery.of(context).size.width,
                              height: 56.h,
                            );
                          },
                        )
                      : GradientMaterialButton(
                          title: LocaleKeys.doing_task.tr(),
                          onTap: () {
                            Get.off(() => FaceRecognitionView(
                                debug: EMULATOR,
                                child: TaskSendPage(
                                    specialNewTaskItem:
                                        widget.specialNewTaskItem)));
                          },
                          width: MediaQuery.of(context).size.width,
                          height: 56.h,
                        ),
                ),
                SizedBox(
                  height: 20.h,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _button(state) {
    if (state.status == ConfirmStatus.loading) {
      return const CupertinoActivityIndicator(
        color: cWhiteColor,
      );
    } else {
      return Text(
        LocaleKeys.confirm.tr(),
        style: TextStyle(fontSize: 14.sp, color: cWhiteColor),
      );
    }
  }
}
