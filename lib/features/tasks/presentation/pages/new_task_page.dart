import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:govassist/core/utils/app_constants.dart';
import 'package:govassist/di/dependency_injection.dart';
import 'package:govassist/features/tasks/data/model/special_new_task.dart';
import 'package:govassist/features/tasks/presentation/bloc/tasks_new_bloc/task_new_cubit.dart';
import 'package:govassist/features/tasks/presentation/pages/task_detail_page.dart';
import 'package:govassist/features/tasks/presentation/widgets/empty_list_widget.dart';
import 'package:govassist/features/tasks/presentation/widgets/task_widget.dart';
import 'package:govassist/generated/assets.dart';
import 'package:govassist/translations/locale_keys.g.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';

class NewTaskPage extends StatefulWidget {
  NewTaskPage({super.key});

  static Widget screen() {
    return BlocProvider(
      create: (context) => di<TaskNewCubit>(),
      child: NewTaskPage(),
    );
  }

  @override
  State<NewTaskPage> createState() => _NewTaskPageState();
}

class _NewTaskPageState extends State<NewTaskPage> {
  final PagingController<int, SpecialNewTaskItem> _pagingController =
      PagingController(firstPageKey: 1);
  List<SpecialNewTaskItem> list = [];
  bool refresh = false;

  handleRefresh(bool refresh) {
    this.refresh = refresh;
    _pagingController.refresh();
  }

  @override
  void initState() {
    super.initState();
    print("init....");
    if (refresh) {
      ///First event when online
      _pagingController.notifyPageRequestListeners(1);
    } else {
      ///First event when offline
      BlocProvider.of<TaskNewCubit>(context)
          .getTasks(pageKey: 1, refresh: true);
    }
    _pagingController.addPageRequestListener((pageKey) {
      if (pageKey == 1) {
        BlocProvider.of<TaskNewCubit>(context)
            .getTasks(pageKey: 1, refresh: true);
      } else {
        BlocProvider.of<TaskNewCubit>(context)
            .getTasks(pageKey: pageKey, refresh: false);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<TaskNewCubit, SpecialNewTaskNewState>(
      listener: (context, state) {
        if (state.status == TaskNewStatus.success) {
          print("working...");
          list = state.specialNewTask?.docs ?? [];
          print(list.length);
          int currentPage = state.specialNewTask?.page ?? 1;
          bool isLastPage = state.specialNewTask?.totalPages == currentPage;
          int next = currentPage + 1;
          if (isLastPage) {
            _pagingController.appendLastPage(list);
          } else {
            _pagingController.appendPage(list, next);
          }
        }
      },
      builder: (context, state) {
        if (state.status == TaskNewStatus.loading ||
            state.status == TaskNewStatus.initial) {
          return Center(
            child: CupertinoActivityIndicator(
              color: Theme.of(context).primaryColor,
              radius: 30.r,
            ),
          );
        } else if (state.status == TaskNewStatus.success) {
          return RefreshIndicator(
            onRefresh: () async {
              handleRefresh(true);
            },
            child: PagedListView(
                physics: BouncingScrollPhysics(
                    parent: AlwaysScrollableScrollPhysics()),
                padding: EdgeInsets.symmetric(vertical: 4.h),
                pagingController: _pagingController,
                builderDelegate: PagedChildBuilderDelegate<SpecialNewTaskItem>(
                    noItemsFoundIndicatorBuilder: (context) {
                  return EmptyListWidget(
                    onTap: () {
                      handleRefresh(true);
                    },
                    title: LocaleKeys.new_task_not_exist.tr(),
                  );
                }, itemBuilder: (context, item, index) {
                  return TaskWidget(
                      onTap: () {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (_) =>
                                    TaskDetailPage.screen(item,true))).then((value) {
                          handleRefresh(refresh);
                        });
                      },
                      specialNewTaskItem: item);
                })),
          );
        } else if (state.status == TaskNewStatus.failure) {
          return Container(
            color: Theme.of(context).cardTheme.color,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ClipRect(
                    child: Container(
                      height: 300.h,
                      child: Column(
                        children: [
                          SizedBox(
                            height: 20.h,
                          ),
                          Expanded(
                              child: SvgPicture.asset(
                            Assets.iconsWarning,
                            height: 140.h,
                          )),
                          Padding(
                              padding: EdgeInsets.only(
                                  top: 10.h,
                                  left: 30.w,
                                  right: 30.w,
                                  bottom: 10.h),
                              child: Text(
                                state.message.toString(),
                                textAlign: TextAlign.center,
                                style: TextStyle(color: cGrayColor1),
                              )),
                          CupertinoButton(
                              child: Text(
                                LocaleKeys.refresh.tr(),
                                style: TextStyle(color: cGrayColor1),
                              ),
                              color: cGrayColor1.withAlpha(80),
                              onPressed: () {
                                handleRefresh(true);
                              }),
                          SizedBox(
                            height: 20.h,
                          )
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        } else {
          return SizedBox();
        }
      },
    );
  }
}
