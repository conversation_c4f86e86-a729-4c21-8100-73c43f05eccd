name: govassist
description: App for "GovAssist"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.7+10

environment:
  sdk: '>=3.1.0 <4.0.0'

isar_version: &isar_version ^3.1.0+1
auto_version: &auto_version ^7.8.4

dependencies:
  flutter:
    sdk: flutter

  dartz: ^0.10.1
  intl: ^0.19.0
  ntp: ^2.0.0
  isar: *isar_version
  isar_flutter_libs: *isar_version
  cupertino_icons: ^1.0.2
  internet_connection_checker: ^1.0.0+1
  get: ^4.6.6
  get_storage: ^2.1.1
  auto_route: *auto_version
  #TODO: Use later
  logger: ^2.0.2+1
  get_it: ^7.2.0
  flutter_bloc: ^8.0.1
  # For mime type
  http_parser: ^4.0.1
  # Identify mime/extension
  mime: ^1.0.2
  http_interceptor: ^1.0.2
  dio: ^5.3.3
  shared_preferences: ^2.0.13
  fluttertoast: ^8.0.8
  flutter_svg: ^2.0.9
  flutter_screenutil: ^5.8.2
  mask_text_input_formatter: ^2.1.0
  # Bloc cached clear helper
  bloc_concurrency: ^0.2.0
  # Image picker from camera
  image_picker: ^1.1.2
  image_editor: ^1.5.1
  carousel_slider: ^4.0.0
  equatable: ^2.0.3
  # For getting Mac address/Other info
  platform_device_id_plus: ^1.0.7-beta
  # For android version
  device_info_plus: ^11.0.0
  # For versioning
  package_info_plus: ^4.2.0
  geolocator: ^10.1.0
  cached_network_image: ^3.2.1
  sn_progress_dialog: ^1.1.3
  # For Fingerprint
  local_auth: ^2.1.7
  # For Slidable items list
  flutter_slidable: ^3.0.1
  # For Recording Voice
  #  convertedDateTime = convertedDateTime.replaceAll(":", ".");
  social_media_recorder: ^1.1.12
  # For Playing Audio
  kplayer: ^0.4.2
  aescryptojs: ^1.0.0
  jwt_decoder: ^2.0.1
  # Smart Select
  flutter_awesome_select: ^6.4.0
  # SMS Autoreader
  auto_sms_verification: ^0.0.8
  #  otp_autofill: ^3.0.0 #This plugin uses SMS User Consent API and SMS Retriever API on Android.
  pinput: ^3.0.1 #More advanced (Pin terminal)
  pin_code_fields: ^8.0.1 #Simpler (Pin terminal)
  custom_pin_screen: ^0.3.0 #For pin code number fields
  # Gallery Saver (Saves Image and Videos even for Android 10/11/12)
  gal: ^2.3.1
  # Opening URL/DIAL/MAILS/FILES
  url_launcher: ^6.1.5
  # Badges
  badges: ^3.1.2
  # Fading scrollview
  fading_edge_scrollview: ^4.1.1
  # Orientation
  native_device_orientation: ^2.0.3
  # Image Modifications
  flutter_image_compress: ^2.2.0
  mno_zoom_widget: ^0.1.0
  # SelectBox
  dropdown_search: 5.0.6
  # Animated things
  animated_custom_dropdown: ^2.0.0
  animated_loading_border: ^0.0.1
  # Playing video
  video_player: ^2.2.6
  video_editor: ^3.0.0
  ffmpeg_kit_flutter_new: ^1.6.1
  # Timer widget
  circular_countdown_timer: ^0.2.2
  pausable_timer: ^3.1.0+3
  # Uint8List saver
  file_saver: ^0.2.14
  in_app_update: ^4.2.2
  zoom_tap_animation: ^1.1.0
  # Video thumbnail
  video_compress: ^3.1.2
  # For beautiful Gradients
  flutter_gradients_reborn: ^1.0.0+7
  permission_handler: ^11.0.1
  external_path: ^2.2.0
  # For casual camera
  camera: ^0.10.5+9
  camera_android: ^0.10.8+13
  # For Compression
  flutter_native_image: ^0.0.6+1
  # For getting root path
  path_provider: ^2.0.11
  # Need to show top snack-bar
  top_snackbar_flutter: ^3.0.0+1
  youtube_player_flutter: ^8.1.2
  # For grouping list
  grouped_list: ^5.1.2
  dotted_border: ^2.1.0
  lite_rolling_switch: ^1.0.1
  group_radio_button: ^1.3.0
  # Firebase
  firebase_crashlytics: ^3.4.3
  firebase_core: ^2.21.0
  # UI Things
  swipeable_button_view: ^0.0.2
  page_transition: ^2.1.0
  slide_action: ^0.0.2
  awesome_circular_chart: ^1.1.0
  flutter_alice: ^1.1.1
  appcheck: ^1.0.6
  easy_localization_loader: ^1.0.0
  easy_localization: ^3.0.3
  flutter_phoenix: ^1.1.0
  overlay_support: ^2.1.0
  open_filex: ^4.7.0
  google_nav_bar: ^5.0.6
  flutter_animate: ^3.0.0
  settings_ui: ^2.0.2
  sqflite: ^2.3.2
  lottie: ^2.7.0
  vibration: ^3.1.3
  flutter_mask_view: ^1.0.2
  flutter_session_manager: ^1.0.3
  flutter_background_service: ^5.0.5
  flutter_background_service_android: ^6.2.2
  flutter_background_service_ios: ^5.0.0
  flutter_network_connectivity: ^0.0.6
  rocket_singleton: ^0.0.1
  theme_mode_handler: ^3.0.0
  time_change_detector: 0.0.3
  awesome_notifications: ^0.10.1
  flutter_pdfview: ^1.3.2
  action_slider: ^0.7.0
  google_maps_flutter: ^2.5.0
  flutter_local_notifications: ^17.2.4
  flutter_timezone: ^1.0.8
  infinite_scroll_pagination: ^4.0.0
  firebase_messaging: ^14.7.10 #latest version flutter pub cache repair without conflicts (must not use with awesome's FCM plugin
  context_holder: ^0.0.5
  screen_brightness: ^1.0.1
  google_fonts: ^4.0.4
  epubx: ^4.0.0
  flutter_html: ^3.0.0-beta.2
  fancy_indicator: ^0.1.2
  win32: ^5.7.2
  archive: ^3.6.1
  syncfusion_flutter_datepicker: ^26.2.13
  typewritertext: ^3.0.9
  rive: 0.13.18
  keyboard_dismisser: ^3.0.0
  file_picker: ^9.0.2
  rxdart:
  device_preview: ^1.2.0

  auto_start_flutter:
    git:
      url: https://github.com/ashiq-kodali/auto_start_flutter.git
      ref: master


  #Causes conflict with inappbrowser
  #  flutter_widget_from_html: ^0.14.11

  #  cosmos_epub:
  #    git: https://github.com/Mamasodikov/cosmos_epub.git

  #  easy_isolate: ^1.3.0
  #  epub_view: ^3.2.0
  #  flutter_autostart: ^0.0.2 (requires newer Dart SDK)
  point_in_polygon:
    git:
      url: https://github.com/aa-cee/point_in_polygon.git
      ref: feature-fixes

#  video_compress:
#    git:
#      url: https://github.com/SpectoraSoftware/VideoCompress
#      ref: caf41183e84adebd6ef5c8fba547937daf556a65 # Or master

dependency_overrides:
  flutter_inappwebview: ^6.1.5
  intl: ^0.20.0
  connectivity_plus: ^6.1.3
  flutter_gen_runner: ^5.10.0
  flutter_gen_core: ^5.10.0
  analyzer: ^6.9.0


dev_dependencies:
  flutter_test:
    sdk: flutter

  # For Syntax Correction (Not used)
  flutter_lints: ^3.0.1

  # For Splash
  flutter_native_splash: ^2.0.3+1

  # For FaceControl

  facesdk_plugin:
    path: ./facesdk_plugin

  # For Local database
  build_runner: any
  isar_generator: *isar_version
  auto_route_generator: ^7.3.2
  flutter_gen_runner: ^5.3.2
  change_app_package_name: ^1.1.0
  # flutter pub run build_runner build --delete-conflicting-outputs

  #Changing app & package name
  rename: ^3.0.1
  # https://pub:dev/packages/rename

  #Changing app icon
  flutter_launcher_icons: ^0.11.0
  # dart run flutter_launcher_icons:main -f pubspec.yaml


flutter_launcher_icons:
  android: "launcher_icon"
  ios: false
  image_path: assets/icons/app_icon.png
  image_path_ios: assets/icons/logoIOS.png
  min_sdk_android: 21 # android min sdk min:16, default 21

#  dart run flutter_native_splash:create
flutter_native_splash:
  image: assets/icons/app_icon.png
  color: "#659961"
  android: true
  ios: false
  info_plist_files:
    - 'ios/Runner/Info-Debug.plist'
    - 'ios/Runner/Info-Release.plist'
  android_12:
    color: "#659961"
    # The image parameter sets the splash screen icon image.  If this parameter is not specified,
    # the app's launcher icon will be used instead.
    # Please note that the splash screen will be clipped to a circle on the center of the screen.
    # App icon with an icon background: This should be 960×960 pixels, and fit within a circle
    # 640 pixels in diameter.
    # App icon without an icon background: This should be 1152×1152 pixels, and fit within a circle
    # 768 pixels in diameter.
    image: assets/icons/logoAPI31.png

    #App icon background color.
    icon_background_color: "#659961"

    # The branding property allows you to specify an image used as branding in the splash screen.
    #branding: assets/dart.png

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
    - assets/icons/
    - assets/images/
    - assets/translations/
    - assets/fonts/
    - assets/documents/
    - assets/map_style/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Segoe
      fonts:
        - asset: assets/fonts/Segoe_400.ttf
          weight: 400
        - asset: assets/fonts/Segoe_Black_900.ttf
          weight: 900
        - asset: assets/fonts/Segoe_Black_Italic_900.ttf
          style: italic
          weight: 900
        - asset: assets/fonts/Segoe_Gras_700.ttf
          weight: 700
        - asset: assets/fonts/Segoe_Gras_Italique_700.ttf
          style: italic
          weight: 700
        - asset: assets/fonts/Segoe_Italique_400.ttf
          style: italic
          weight: 400
        - asset: assets/fonts/Segoe_Light_300.ttf
          weight: 300
        - asset: assets/fonts/Segoe_Light_Italic_300.ttf
          style: italic
          weight: 300
        - asset: assets/fonts/Segoe_Semibold_600.ttf
          weight: 600
        - asset: assets/fonts/Segoe_Semibold_Italic_600.ttf
          style: italic
          weight: 600
  #        - asset: assets/fonts/Segoe_Semilight_350.ttf
  #          weight: 350
  #        - asset: assets/fonts/Segoe_Semilight_Italic_350.ttf
  #          style: italic
  #          weight: 350
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

            # Language generate
          #       flutter pub run easy_localization:generate -S "assets/translations" -O "lib/translations"
          #       flutter pub run easy_localization:generate -S "assets/translations" -O "lib/translations" -o "locale_keys.g.dart" -f  keys

# Delete unused imports:
# dart fix --apply --code=unused_import


#          Recommended
#       flutter build apk -t lib/main_dev.dart --flavor=dev --release
#       flutter build apk -t lib/main_prod.dart --flavor=prod --release
#       flutter build appbundle -t lib/main_prod.dart --flavor=prod

#       Running application
#       flutter run -t lib/main_prod.dart --flavor prod
#       flutter run -t lib/main_dev.dart --flavor dev